export const indexData = {
  code: 1,
  res: '成功',
  data: {
    dimList: [
      {
        id: 1, // 维度ID
        dataType: 'string', // date: 日期  number:数字  string: 字符串
        type: 1, // 1: 物理字段 2: 计算字段
        name: '城市', // 维度名称
      },
      {
        id: 2, // 维度ID
        dataType: 'string', // date: 日期  number:数字  string: 字符串
        type: 1, // 1: 物理字段 2: 计算字段
        name: '公司', // 维度名称
      },
      {
        id: 3, // 维度ID
        dataType: 'string', // date: 日期  number:数字  string: 字符串
        type: 1, // 1: 物理字段 2: 计算字段
        name: '品牌', // 维度名称
      },
    ],
    indexList: [
      {
        id: 1, // 维度ID
        dataType: 'string', // date: 日期  number:数字  string: 字符串
        type: 1, // 1: 物理字段 2: 计算字段
        name: '收入', // 维度名称
      },
      {
        id: 2, // 维度ID
        dataType: 'string', // date: 日期  number:数字  string: 字符串
        type: 1, // 1: 物理字段 2: 计算字段
        name: '支出', // 维度名称
      },
      {
        id: 3, // 维度ID
        dataType: 'string', // date: 日期  number:数字  string: 字符串
        type: 1, // 1: 物理字段 2: 计算字段
        name: '同环比', // 维度名称
      },
    ],
  },
};

export const verifySql = {
  code: 1,
  msg: '语法错误',
  data: [],
};

export const saveSql = {
  code: 1,
  msg: '成功',
  data: [],
};

export const getSql = {
  code: 1,
  msg: '成功',
  data: {
    reportId: 1, // 报表ID
    dataSourceId: 1, // 数据集ID
    columnId: 1, // 数据集字段ID
    fieldName: 'update内容', // 字段名称
    expression: 'update内容', // 语法
    type: 1, // 1: 维度 2: 指标
  },
};

export const saveComponentData = {
  code: 1,
  msg: '成功',
  data: {
    id: 234234,
  },
};

// 保存页面结构
export const savePageStructure = function (list) {
  return {
    code: 1,
    msg: '成功',
    data: {
      fileName: '页面名称', //必填
      reportId: '', // 报告id
      children: list.map((v, i) => {
        return {
          ...v,
          elementId: i,
        };
      }),
    },
  };
};
