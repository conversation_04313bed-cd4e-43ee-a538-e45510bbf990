export const mockData = {
  code: 1,
  msg: '成功',
  data: {
    dimensionInfo: [
      // 图表下代表x轴，交叉表代表行， 表格没有， 筛选器的行
      {
        text: '城市',
        id: 2, // 维度ID
        key: 'cityId', // 维度字段key
        config: '{}', // 对维度的一些规则
      },
    ],
    measureInfo: [
      // 图表下代表y轴，交叉表代表列， 表格下代表列
      {
        text: '收入',
        computeModeId: 108, // 计算规则（同比、环比）
        id: 4, // 指标的ID
        key: 'income1', // 指标key
        config: '{}',
      },
    ],
    contrastInfo: [
      //交叉表下代表对比
      {
        text: '日期',
        id: 1, // 维度ID
        key: 'date', // 维度key
        config: '{}',
      },
    ],
    // 适配其他图表、表格的
    values: [
      {
        date: '2018',
        cityId: '北京',
        tenantId: '约1',
        income1: 666,
      },
      {
        date: '2018',
        cityId: '山西',
        tenantId: '约1',
        income1: 222,
      },
      {
        date: '2018',
        cityId: '天津',
        tenantId: '约1',
        income1: 999,
      },
      {
        date: '2018',
        cityId: '上海',
        tenantId: '约1',
        income1: 888,
      },
    ],
  },
};

export default function (dataSetConfig) {
  let values = [];
  try {
    const wdKey = dataSetConfig.dimensionInfo[0].key;
    for (let i = 0; i < 10; i++) {
      const op = {
        [wdKey]: '维度' + i,
      };
      dataSetConfig.measureInfo.forEach((dl) => {
        op[dl.key] = parseFloat((Math.random() * (20 - 10) + 10).toFixed(2));
      });
      values.push(op);
    }
  } catch (e) {
    values = '出错啦';
  }
  return {
    code: 1,
    msg: '成功',
    data: {
      dimensionInfo: dataSetConfig?.dimensionInfo,
      measureInfo: dataSetConfig?.measureInfo,
      // 适配其他图表、表格的
      values: values,
    },
  };
}
