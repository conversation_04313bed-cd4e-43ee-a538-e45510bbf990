export const layoutInfo = {
  reportName: '未命名报告',
  reportId: null,
  children: [
    {
      title: '未命名图表2',
      id: 'node_oclr4nnxaf8',
      componentType: 3,
      elementId: 0,
      dataType: 2,
    },
    {
      id: 'node_oclr4nnxaf3',
      componentType: 1,
      elementId: 1,
    },
    {
      id: 'node_oclr4nnxaf5',
      componentType: 1,
      elementId: 2,
    },
    {
      title: '未命名图表1',
      id: 'node_oclr4no8pqm6',
      componentType: 1,
      elementId: 3,
    },
    {
      title: '未命名图表3',
      id: 'node_oclr4no8pqss',
      componentType: 1,
      elementId: 4,
    },
    {
      title: '未命名图表4',
      id: 'node_oclr4no8pq11e',
      componentType: 1,
      elementId: 5,
    },
    {
      title: '未命名图表5',
      id: 'node_oclr4no8pq15t',
      componentType: 3,
      elementId: 6,
      dataType: 2,
    },
    {
      title: '未命名图表8',
      id: 'node_oclr4no8pq16s',
      componentType: 3,
      elementId: 7,
      dataType: 2,
    },
    {
      title: '未命名图表7',
      id: 'node_oclr4no8pq1aj',
      componentType: 2,
      elementId: 8,
      dataType: 3,
    },
    {
      title: '未命名图表9',
      id: 'node_oclr4no8pq1ch',
      elementId: 9,
    },
  ],
  schema:
    '{"version":"1.0.0","componentsMap":[{"package":"leopard-web-comp","version":"0.1.0","exportName":"CityFilter","main":"src/pages/lego/libraryMaterials/index.tsx","destructuring":true,"subName":"","componentName":"CityFilter"},{"package":"@alifd/layout","version":"2.4.1","exportName":"Cell","main":"lib/index.js","destructuring":true,"subName":"","componentName":"FDCell"},{"package":"leopard-web-comp","version":"0.1.0","exportName":"CapacityCompanyFilter","main":"src/pages/lego/libraryMaterials/index.tsx","destructuring":true,"subName":"","componentName":"CapacityCompanyFilter"},{"package":"@alifd/layout","version":"2.4.1","exportName":"Row","main":"lib/index.js","destructuring":true,"subName":"","componentName":"FDRow"},{"package":"leopard-web-comp","version":"0.1.0","exportName":"DateFilter","main":"src/pages/lego/libraryMaterials/index.tsx","destructuring":true,"subName":"","componentName":"DateFilter"},{"package":"leopard-web-comp","version":"0.1.0","exportName":"SearchButton","main":"src/pages/lego/libraryMaterials/index.tsx","destructuring":true,"subName":"","componentName":"SearchButton"},{"package":"leopard-web-comp","version":"0.1.0","exportName":"ListFilter","main":"src/pages/lego/libraryMaterials/index.tsx","destructuring":true,"subName":"","componentName":"ListFilter"},{"package":"leopard-web-comp","version":"0.1.0","exportName":"LineChart","main":"src/pages/lego/libraryMaterials/index.tsx","destructuring":true,"subName":"","componentName":"LineChart"},{"package":"leopard-web-comp","version":"0.1.0","exportName":"PieChart","main":"src/pages/lego/libraryMaterials/index.tsx","destructuring":true,"subName":"","componentName":"PieChart"},{"package":"leopard-web-comp","version":"0.1.0","exportName":"Table","main":"src/pages/lego/libraryMaterials/index.tsx","destructuring":true,"subName":"","componentName":"Table"},{"package":"leopard-web-comp","version":"0.1.0","exportName":"TableSheet","main":"src/pages/lego/libraryMaterials/index.tsx","destructuring":true,"subName":"","componentName":"TableSheet"},{"devMode":"lowCode","componentName":"Page"}],"componentsTree":[{"componentName":"Page","id":"node_dockcviv8fo1","docId":"doclaqkk3b9","props":{"ref":"outerView","style":{"height":"100%"}},"fileName":"/","dataSource":{},"state":{"text":{"type":"JSExpression","value":"\\"outer\\""},"isShowDialog":{"type":"JSExpression","value":"false"}},"css":"body {\\n  font-size: 12px;\\n}\\n\\n.button {\\n  width: 100px;\\n  color: #ff00ff\\n}","lifeCycles":{"componentDidMount":{"type":"JSFunction","value":"function componentDidMount() {\\n  console.log(\'did mount\');\\n}"}},"methods":{},"originCode":"class LowcodeComponent extends Component {\\n  state = {\\n    \\"text\\": \\"outer\\",\\n    \\"isShowDialog\\": false\\n  }\\n  componentDidMount() {\\n    console.log(\'did mount\');\\n  }\\n  componentWillUnmount() {\\n    console.log(\'will unmount\');\\n  }\\n  testFunc() {\\n    console.log(\'test func\');\\n  }\\n  onClick() {\\n    this.setState({\\n      isShowDialog: true\\n    });\\n  }\\n  closeDialog() {\\n    this.setState({\\n      isShowDialog: false\\n    });\\n  }\\n  getHelloWorldText() {\\n    return this.i18n(\'i18n-jwg27yo4\');\\n  }\\n  getHelloWorldText2() {\\n    return this.i18n(\'i18n-jwg27yo3\', {\\n      name: \'絮黎\',\\n    });\\n  }\\n  onTestConstantsButtonClicked() {\\n    console.log(\'constants.ConstantA:\', this.constants.ConstantA);\\n    console.log(\'constants.ConstantB:\', this.constants.ConstantB);\\n\\t}\\n\\tonTestUtilsButtonClicked(){\\n    this.utils.demoUtil(\'param1\', \'param2\');\\n\\t}\\n}","hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"FDRow","id":"node_oclr4nnxaf1","docId":"doclr4nnxaf","props":{},"title":"页面","hidden":false,"isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"FDCell","id":"node_oclr4nnxaf2","docId":"doclr4nnxaf","props":{"style":{"backgroundColor":"rgba(255,255,255,1)","marginBottom":"8px","minHeight":2},"align":"left","verAlign":"top","width":458.5},"title":"页面","hidden":false,"isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"CityFilter","id":"node_oclr4nnxaf3","docId":"doclr4nnxaf","props":{},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]},{"componentName":"FDCell","id":"node_oclr4nnxaf4","docId":"doclr4nnxaf","props":{"style":{"backgroundColor":"rgba(255,255,255,1)","marginBottom":"8px"}},"title":"页面","hidden":false,"isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"CapacityCompanyFilter","id":"node_oclr4nnxaf5","docId":"doclr4nnxaf","props":{},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]}]},{"componentName":"FDRow","id":"node_oclr4nnxaf6","docId":"doclr4nnxaf","props":{},"title":"页面","hidden":false,"isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"FDCell","id":"node_oclr4no8pqm5","docId":"doclr4no8pq","props":{"align":"center","verAlign":"top","style":{"backgroundColor":"rgba(255,255,255,1)","marginBottom":"8px"}},"title":"容器","hidden":false,"isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"DateFilter","id":"node_oclr4no8pqm6","docId":"doclr4no8pq","props":{},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]},{"componentName":"FDCell","id":"node_oclr4no8pqsr","docId":"doclr4no8pq","props":{"align":"center","verAlign":"top","style":{"backgroundColor":"rgba(255,255,255,1)","marginBottom":"8px"}},"title":"容器","hidden":false,"isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"SearchButton","id":"node_oclr4no8pqss","docId":"doclr4no8pq","props":{},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]},{"componentName":"FDCell","id":"node_oclr4no8pq11d","docId":"doclr4no8pq","props":{"align":"center","verAlign":"top","style":{"backgroundColor":"rgba(255,255,255,1)","marginBottom":"8px"}},"title":"容器","hidden":false,"isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"ListFilter","id":"node_oclr4no8pq11e","docId":"doclr4no8pq","props":{},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]}]},{"componentName":"FDCell","id":"node_oclr4nnxaf7","docId":"doclr4nnxaf","props":{"style":{"backgroundColor":"rgba(255,255,255,1)","marginBottom":"8px","minHeight":""},"align":"left","verAlign":"top"},"title":"页面","hidden":false,"isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"LineChart","id":"node_oclr4nnxaf8","docId":"doclr4nnxaf","props":{"dataSetConfig":{"dataSourceId":1,"dimensionInfo":[{"columnId":1,"key":"date1","fieldType":"dimensionInfo","dataType":"string","title":"数据魔方维度1"}],"measureInfo":[{"columnId":5,"key":"city","fieldType":"dimensionInfo","dataType":"string","title":"城市字段"}]}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]},{"componentName":"FDRow","id":"node_oclr4no8pq15r","docId":"doclr4no8pq","props":{},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"FDCell","id":"node_oclr4no8pq15s","docId":"doclr4no8pq","props":{"align":"center","verAlign":"top","style":{"backgroundColor":"rgba(255,255,255,1)","marginBottom":"8px","minHeight":190},"width":391},"title":"容器","hidden":false,"isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"LineChart","id":"node_oclr4no8pq15t","docId":"doclr4no8pq","props":{"dataSetConfig":{"dataSourceId":1,"dimensionInfo":[{"columnId":1,"key":"date1","fieldType":"dimensionInfo","dataType":"string","title":"数据魔方维度1"}],"measureInfo":[{"columnId":2,"key":"income","fieldType":"measureInfo","title":"订单量"},{"columnId":3,"key":"income2","fieldType":"measureInfo","title":"完单率"}]}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]},{"componentName":"FDCell","id":"node_oclr4no8pq16r","docId":"doclr4no8pq","props":{"align":"center","verAlign":"top","style":{"backgroundColor":"rgba(255,255,255,1)","marginBottom":"8px"}},"title":"容器","hidden":false,"isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"PieChart","id":"node_oclr4no8pq16s","docId":"doclr4no8pq","props":{"dataSetConfig":{"dataSourceId":1,"dimensionInfo":[{"columnId":1,"key":"date1","fieldType":"dimensionInfo","dataType":"string","title":"数据魔方维度1"}],"measureInfo":[{"columnId":2,"key":"income","fieldType":"measureInfo","title":"订单量"}]}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]}]},{"componentName":"FDRow","id":"node_oclr4no8pq1ah","docId":"doclr4no8pq","props":{},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"FDCell","id":"node_oclr4no8pq1ai","docId":"doclr4no8pq","props":{"align":"center","verAlign":"top","style":{"backgroundColor":"rgba(255,255,255,1)","marginBottom":"8px","minHeight":178},"width":399},"title":"容器","hidden":false,"isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"Table","id":"node_oclr4no8pq1aj","docId":"doclr4no8pq","props":{"dataSetConfig":{"dataSourceId":1,"dimensionInfo":[{"columnId":2,"key":"date2","fieldType":"dimensionInfo","dataType":"string","title":"数据魔方维度二"},{"columnId":4,"key":"date","fieldType":"dimensionInfo","dataType":"date","title":"日期字段"},{"columnId":4,"key":"rate","fieldType":"measureInfo","title":"占比"},{"columnId":5,"key":"income3","fieldType":"measureInfo","title":"人数总和"}]}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]},{"componentName":"FDCell","id":"node_oclr4no8pq1cg","docId":"doclr4no8pq","props":{"align":"center","verAlign":"top","style":{"backgroundColor":"rgba(255,255,255,1)","marginBottom":"8px"}},"title":"容器","hidden":false,"isLocked":false,"condition":true,"conditionGroup":"","children":[{"componentName":"TableSheet","id":"node_oclr4no8pq1ch","docId":"doclr4no8pq","props":{"dataSetConfig":{"dataSourceId":1,"dimensionInfo":[{"columnId":1,"key":"date1","fieldType":"dimensionInfo","dataType":"string","title":"数据魔方维度1"},{"columnId":2,"key":"date2","fieldType":"dimensionInfo","dataType":"string","title":"数据魔方维度二"}],"measureInfo":[{"columnId":2,"key":"income","fieldType":"measureInfo","title":"订单量"},{"columnId":3,"key":"income2","fieldType":"measureInfo","title":"完单率"}],"contrastInfo":[{"columnId":2,"key":"date2","fieldType":"dimensionInfo","dataType":"string","title":"数据魔方维度二"},{"columnId":4,"key":"date","fieldType":"dimensionInfo","dataType":"date","title":"日期字段"}]}},"hidden":false,"title":"","isLocked":false,"condition":true,"conditionGroup":""}]}]}]}],"i18n":{"zh-CN":{"i18n-jwg27yo4":"你好 ","i18n-jwg27yo3":"{name}"},"en-US":{"i18n-jwg27yo4":"Hello ","i18n-jwg27yo3":"{name}"}}}',
};

export const pageLayoutInfo = {
  code: 1,
  res: '成功',
  data: layoutInfo,
};
