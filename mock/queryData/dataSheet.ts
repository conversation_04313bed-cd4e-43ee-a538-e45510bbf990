export const dataSheet = {
  code: 1,
  res: '成功',
  data: {
    dimensionInfo: [
      {
        id: 1,
        title: '区域',
        key: 'area',
      },
    ],
    measureInfo: [
      {
        id: 2,
        title: '发单量',
        key: 'fadan',
      },
      {
        id: 3,
        title: '完单量',
        key: 'wandan',
      },
      {
        id: 4,
        title: '匹配量',
        key: 'pipei',
      },
    ],
    contrasts: [
      {
        title: '订单类型',
        id: 1,
        key: 'orderType',
        config: '{}',
      },
    ],
    isMore: 1, // 下一页是否还有数据   1：有数据  0：无数据
    pageSize: 10, // 分页阈值
    pageNum: 1, // 当前页数
    totalPage: 10, // 总页数
    totalNum: 100, // 总条数
    value: {
      rows: [
        {
          cells: [
            '区域',
            '西区1',
            '西区2',
            '西区3',
            '西区4',
            '西区5',
            '西区6',
            '西区7',
            '西区8',
            '西区9',
            '西区10',
          ],
        },
      ],
      columns: [
        {
          cells: ['完单量', '自营'],
        },
        {
          cells: ['完单量', 'CP'],
        },
        {
          cells: ['完单量', '公海'],
        },
        {
          cells: ['完单量', 'DCP'],
        },
        {
          cells: ['完单量', 'DP'],
        },
        {
          cells: ['发单量', '自营'],
        },
        {
          cells: ['发单量', 'CP'],
        },
        {
          cells: ['发单量', '公海'],
        },
        {
          cells: ['发单量', 'DCP'],
        },
        {
          cells: ['发单量', 'DP'],
        },
        {
          cells: ['匹配量', '自营'],
        },
        {
          cells: ['匹配量', 'CP'],
        },
        {
          cells: ['匹配量', '公海'],
        },
        {
          cells: ['匹配量', 'DCP'],
        },
        {
          cells: ['匹配量', 'DP'],
        },
      ],
      values: [
        [
          32332, 33223, 2233, 333, 33, 32332, 33223, 2233, 333, 33, 32332,
          33223, 2233, 333, 33,
        ],
        [
          13223, 4322, 5443, 76, 55, 23223, 4322, 5443, 76, 55, 23223, 4322,
          5443, 76, 55,
        ],
        [
          33223, 4322, 5443, 76, 55, 23223, 4322, 5443, 76, 55, 23223, 4322,
          5443, 76, 55,
        ],
        [
          43223, 4322, 5443, 76, 55, 23223, 4322, 5443, 76, 55, 23223, 4322,
          5443, 76, 55,
        ],
        [
          53223, 4322, 5443, 76, 55, 23223, 4322, 5443, 76, 55, 23223, 4322,
          5443, 76, 55,
        ],
        [
          63223, 4322, 5443, 76, 55, 23223, 4322, 5443, 76, 55, 23223, 4322,
          5443, 76, 55,
        ],
        [
          73223, 4322, 5443, 76, 55, 23223, 4322, 5443, 76, 55, 23223, 4322,
          5443, 76, 55,
        ],
        [
          83223, 4322, 5443, 76, 55, 23223, 4322, 5443, 76, 55, 23223, 4322,
          5443, 76, 55,
        ],
        [
          93223, 4322, 5443, 76, 55, 23223, 4322, 5443, 76, 55, 23223, 4322,
          5443, 76, 55,
        ],
        [
          203223, 4322, 5443, 76, 55, 23223, 4322, 5443, 76, 55, 23223, 4322,
          5443, 76, 55,
        ],
      ],
    },
  },
};
