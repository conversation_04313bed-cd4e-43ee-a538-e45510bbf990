export const mockDataSourceDetail = {
  code: 1,
  res: '成功',
  data: {
    dimensionList: [
      {
        columnId: 1, // 维度ID
        dataType: 0, // 2 date: 日期  1 number:数字  0 string: 字符串
        type: 1, // 1: 物理字段 2: 计算字段
        title: '数据魔方维度1', // 维度名称
        key: 'date1', // 维度唯一标识
        isAggr: 1, //是否可以再次sum 1可以|0不可以
      },
      {
        columnId: 2, // 维度ID
        dataType: 0, // date: 日期  number:数字  string: 字符串
        type: 1, // 1: 物理字段 2: 计算字段
        title: '数据魔方维度二', // 维度名称
        key: 'date2', // 维度唯一标识
        isAggr: 1, //是否可以再次sum 1可以|0不可以
      },
      {
        columnId: 3, // 维度ID
        dataType: 0, // date: 日期  number:数字  string: 字符串
        type: 1, // 1: 物理字段 2: 计算字段
        title: '数据魔方维度三', // 维度名称
        key: 'date3', // 维度唯一标识
        isAggr: 1, //是否可以再次sum 1可以|0不可以
      },
      {
        columnId: 4, // 维度ID
        dataType: 2, // date: 日期  number:数字  string: 字符串
        type: 1, // 1: 物理字段 2: 计算字段
        title: '日期字段', // 维度名称
        key: 'date', // 维度唯一标识
        isAggr: 1, //是否可以再次sum 1可以|0不可以
      },
      {
        columnId: 5, // 维度ID
        dataType: 0, // date: 日期  number:数字  string: 字符串
        type: 1, // 1: 物理字段 2: 计算字段
        title: '城市字段', // 维度名称
        key: 'city', // 维度唯一标识
        isAggr: 1, //是否可以再次sum 1可以|0不可以
      },
    ],
    measureList: [
      {
        columnId: 2, // 指标ID
        type: 1, // 1: 物理字段 2: 计算字段
        title: '订单量', // 指标名称
        key: 'income', // 指标唯一标识
        isAggr: 1, //是否可以再次sum 1可以|0不可以
      },
      {
        columnId: 3, // 指标ID
        type: 1, // 1: 物理字段 2: 计算字段
        title: '完单率', // 指标名称
        key: 'income2', // 指标唯一标识
        isAggr: 1, //是否可以再次sum 1可以|0不可以
      },
      {
        columnId: 4, // 指标ID
        type: 1, // 1: 物理字段 2: 计算字段
        title: '占比', // 指标名称
        key: 'rate', // 指标唯一标识
        isAggr: 1, //是否可以再次sum 1可以|0不可以
      },
      {
        columnId: 5, // 指标ID
        type: 1, // 1: 物理字段 2: 计算字段
        title: '人数总和', // 指标名称
        key: 'income3', // 指标唯一标识
        isAggr: 1, //是否可以再次sum 1可以|0不可以
      },
      {
        columnId: 6, // 指标ID
        type: 1, // 1: 物理字段 2: 计算字段
        title: '司机人数', // 指标名称
        key: 'income4', // 指标唯一标识
        isAggr: 0, //是否可以再次sum 1可以|0不可以
      },
    ],
  },
};
