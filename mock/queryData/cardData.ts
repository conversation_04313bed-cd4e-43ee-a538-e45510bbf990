
/*
 * Created on Tue Jan 09 2024
 *
 * Copyright (c) 2024 bai long ma
 */

export const cardData = {
    code: 1,
    res: "成功",
    data: {
        elementId: 234234,             // 组件服务id
        title: "精选司机数",
        value: 22.14,
        MoM: "+12.13%",  // 环比
        YoY: "-12%",		 // 同比
        explain: "本月呈下降趋势，环比上升12.13%,同比下降14%",
        chartData: {		// 图表数据
            dimensionInfo: [
                {
                    title: "日期",
                    id: 1,								// 指标/维度ID
                    key: 'date',					    // 唯一标识
                    config: {}
                }
            ],
            measureInfo: [             		// 图表下代表y轴
                {
                    title: "收入",
                    computeModeId: 100,          // 计算规则（同比、环比）
                    id: 1,       								// 指标的ID
                    key: "income",							// 指标唯一标识
                    config: {}
                }
            ],
            values: [		                     //按列返回
                {
                    date: '2018-08-08',
                    income: 1
                },
                {
                    date: '2018-08-09',
                    income: 3
                },
                {
                    date: '2018-08-10',
                    income: 1
                }
            ]
        }
    }
};
