import { mockDataSourceDetail } from './queryData/mockDataSourceDetail';
import { dataSheet } from './queryData/dataSheet';
import createChartData from './queryData/mockData';
import { tableData } from './queryData/tableData';
import { mockDataSourceList } from './queryData/mockDataSourceList';
import {
  indexData,
  verifySql,
  saveSql,
  getSql,
  saveComponentData,
  savePageStructure,
} from './queryData/indexData';
import { cityData, capacityCompany } from './queryData/cityData';
import { pageLayoutInfo } from './queryData/pageLayoutInfo';
import { filterMock } from './queryData/filter';

export default {};
