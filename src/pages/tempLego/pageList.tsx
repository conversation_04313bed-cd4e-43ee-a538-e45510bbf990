import { lazy } from 'react';
// import NewComponent from '@/pages/newLego/pageList';
// import OldComponent from '@/pages/lego/modules/pageList';
import { legoGeneralizeSwitch } from '@/utils/sceneSwitch';

const NewComponent = lazy(
  () => import(/*webpackChunkName: "pageList-new"*/ '@/pages/newLego/pageList'),
);
const OldComponent = lazy(
  () =>
    import(
      /*webpackChunkName: "pageList-old"*/ '@/pages/lego/modules/pageList'
    ),
);

export default function () {
  return legoGeneralizeSwitch() ? <NewComponent /> : <OldComponent />;
}
