import { lazy } from 'react';
// import NewComponent from '@/pages/newLego/permission';
// import OldComponent from '@/pages/lego/modules/permission';
import { legoGeneralizeSwitch } from '@/utils/sceneSwitch';

const NewComponent = lazy(
  () =>
    import(/*webpackChunkName: "permission-new"*/ '@/pages/newLego/permission'),
);
const OldComponent = lazy(
  () =>
    import(
      /*webpackChunkName: "permission-old"*/ '@/pages/lego/modules/permission'
    ),
);

export default function () {
  return legoGeneralizeSwitch() ? <NewComponent /> : <OldComponent />;
}
