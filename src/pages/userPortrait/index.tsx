import dayjs from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import { blmAnalysisPageView } from '@/utils/eventTracking';
import {
  crowdCopy,
  pageQueryRecommendCrowdInfoList,
  pageQueryRecommendCrowdInfoListNew,
  querySpecifiedLevelLabel,
  querySpecifiedLevelLabelNew,
} from './api';
import OperationPoolOrg from './components/operationPool/operationPoolOrg';
import TagSquare from './components/tagSquare';
import styles from './index.less';

const UserPortrait = () => {
  const operationPoolRef = useRef();
  const operationPoolOrgRef = useRef();
  const [formInstance, setFormInstance] = useState();
  // 标签添加操作
  const [operationPoolChange, setOperationPoolChange] = useState([
    { key: 0, tagList: [], isDeleted: false, operator: 'AND', status: 'AND' },
  ]);
  // 复制基础信息
  const [copyBaseInformation, setCopyBaseInformation] = useState(null);
  // 复制保存运算符
  const [copyOperationPoolChange, setCopyOperationPoolChange] = useState([]);
  const DaysEnum = {
    1: '周六',
    2: '周日',
    3: '工作日',
  };
  const generateExclusionString = (daysString) => {
    const daysArray = daysString.split(',').map(Number);
    let exclusionString = '剔除';

    daysArray.forEach((day, index) => {
      exclusionString += DaysEnum[day];
      if (index < daysArray.length - 1) {
        exclusionString += '、';
      }
    });

    return exclusionString;
  };
  const handleTimeTextCopy = (tagItem) => {
    let timesText = '';
    if (tagItem.valueType === 2) {
      timesText = `${dayjs(tagItem.startDate).format('YYYY-MM-DD')} 至 ${dayjs(
        tagItem.endDate,
      ).format('YYYY-MM-DD')}${
        tagItem.dateCancelType?.length
          ? ' | ' + generateExclusionString(tagItem.dateCancelType)
          : ''
      }`;
    } else if (tagItem.valueType === 3) {
      if (tagItem.dyStartDateType === 1) {
        timesText = `${dayjs(tagItem.startDate).format('YYYY-MM-DD')} 至 过去${
          tagItem.subEnd
        }日 ${
          tagItem.dateCancelType?.length
            ? ' | ' + generateExclusionString(tagItem.dateCancelType)
            : ''
        }`;
      } else {
        timesText = `过去${tagItem.subStart}日 至 过去${tagItem.subEnd}日 ${
          tagItem.dateCancelType?.length
            ? ' | ' + generateExclusionString(tagItem.dateCancelType)
            : ''
        }`;
      }
    }
    return timesText;
  };

  // 查找对应名称转换
  const handleLabelStartValue = (data, str) => {
    return data?.find((item) => item.value === str)?.label;
  };
  // 处理复制中标签描述信息
  const handleCopyFormValueStr = (curTagItem) => {
    if (curTagItem.showType === 1) {
      return `${curTagItem.levelName} ${handleTimeTextCopy(curTagItem)}`;
    } else if ([2, 10].includes(curTagItem.showType)) {
      return `${curTagItem.levelName} 等于 ${handleLabelStartValue(
        curTagItem.enumValueParams,
        curTagItem.startValue,
      )}`;
    } else if (curTagItem.showType === 3) {
      if (curTagItem.scope === 1) {
        return `${curTagItem.levelName} ${handleTimeTextCopy(
          curTagItem,
        )} ${handleLabelStartValue(
          curTagItem.fomulaRelationList,
          curTagItem.calType,
        )} ${handleLabelStartValue(
          curTagItem.calRelationList,
          curTagItem.scope,
        )} 在${curTagItem.startValue}${curTagItem.unit}~${curTagItem.endValue}${
          curTagItem.unit
        }之间`;
      } else {
        return `${curTagItem.levelName} ${handleTimeTextCopy(
          curTagItem,
        )} ${handleLabelStartValue(
          curTagItem.fomulaRelationList,
          curTagItem.calType,
        )} ${handleLabelStartValue(
          curTagItem.calRelationList,
          curTagItem.scope,
        )} ${curTagItem.startValue} ${curTagItem.unit}`;
      }
    } else if (curTagItem.showType === 5) {
      return `${curTagItem.levelName} ${handleLabelStartValue(
        curTagItem.calRelationList,
        curTagItem.scope,
      )} ${curTagItem.startValue}`;
    } else if (curTagItem.showType === 4) {
      if (curTagItem.scope === 1) {
        return `${curTagItem.levelName} ${handleLabelStartValue(
          curTagItem.calRelationList,
          curTagItem.scope,
        )} 在${curTagItem.startValue}${curTagItem.unit}~${curTagItem.endValue}${
          curTagItem.unit
        }之间`;
      } else {
        return `${curTagItem.levelName} ${handleLabelStartValue(
          curTagItem.calRelationList,
          curTagItem.scope,
        )} ${curTagItem.startValue} ${curTagItem.unit}`;
      }
    } else if (curTagItem.showType === 9) {
      // 数组类型value值用/隔开
      let replaceValueTxt = curTagItem.startValue?.replace(/,/g, '/');
      return `${
        curTagItem.levelName
      } 等于  ${replaceValueTxt} ${handleLabelStartValue(
        curTagItem.calRelationList,
        curTagItem.scope,
      )}`;
    } else if (curTagItem.showType === 13) {
      const formValueStrTree = JSON.parse(curTagItem.startValue)
        .map((item) => item[item?.length - 1]?.valueName)
        .join(',');
      return `${curTagItem.levelName} 包含 ${formValueStrTree}`;
    } else if (curTagItem.showType === 11) {
      return `${curTagItem.levelName} ${handleTimeTextCopy(
        curTagItem,
      )} ${handleLabelStartValue(
        curTagItem.fomulaRelationList,
        curTagItem.calType,
      )} ${handleLabelStartValue(
        curTagItem.calRelationList,
        curTagItem.scope,
      )} ${handleLabelStartValue(
        curTagItem.enumValueParams,
        curTagItem.startValue,
      )}`;
    } else if ([6, 12, 14].includes(curTagItem.showType)) {
      return `人群名称：${curTagItem?.tagDesc}`;
    }
  };
  useEffect(() => {
    blmAnalysisPageView({
      pageId: 'p_leopard_cp_00000402',
      eventId: 'e_leopard_cp_pv_00001802',
    });
    setFormInstance(operationPoolOrgRef?.current?.getFormInstance());
    const crowdId =
      new URLSearchParams(location.search.slice(1)).get('crowdId') ?? '';
    const recommendCrowdId =
      new URLSearchParams(location.search.slice(1)).get('recommendCrowdId') ??
      '';
    // 人群推荐跳转进来
    if (recommendCrowdId) {
      const recommendCrowdInfoFn = window.$BLMReleaseCenter.getSceneSwitch(
        'crowdValidity',
      )
        ? pageQueryRecommendCrowdInfoListNew
        : pageQueryRecommendCrowdInfoList;
      recommendCrowdInfoFn({
        platformId: 1,
        businessType: 1,
        entityType: 1,
        pageSize: 1000,
        pageNum: 1,
      }).then((resRecommend) => {
        if (resRecommend?.code === 1 && resRecommend.data) {
          const recommendCrowdItem = resRecommend.data?.items?.find(
            (item) => item.id === Number(recommendCrowdId),
          );
          const recommendCrowdList = [
            {
              ...recommendCrowdItem,
              crowdType: 2,
              copyErrorTag: false,
              idx: 0, // 保留运算池内被添加过的所有标签，防止idx错乱
              isTagDelete: false, // 该标签是否在运算池内被删除
              formValueStr: `人群名称：${recommendCrowdItem?.name}`,
              tagId: recommendCrowdItem.id,
              latelType: 3,
              scope: 2,
              name: recommendCrowdItem.id,
              crowdTagName: recommendCrowdItem.name,
            },
          ];
          const recommendContainerList = [
            {
              key: 0,
              isDeleted: false,
              status: 'AND',
              operator: 'AND',
              tagList: recommendCrowdList,
            },
          ];
          setOperationPoolChange(recommendContainerList);
        }
      });
    }
    // 复制进来场景
    if (crowdId) {
      crowdCopy({ crowdId: crowdId }).then((res) => {
        if (res && res.data) {
          setCopyBaseInformation(res.data.spVO);
          let copyContainerList = [];
          let myMap = new Map();
          const specifiedLevelFn = window.$BLMReleaseCenter.getSceneSwitch(
            'crowdValidity',
          )
            ? querySpecifiedLevelLabelNew
            : querySpecifiedLevelLabel;
          specifiedLevelFn({
            businessType: 1,
            entityType: 1,
            platformId: 11,
            createSource: 1,
            driverDimension: 2,
            pageSize: 1000,
            pageNum: 1,
          }).then((resTag) => {
            if (resTag.code === 1 && resTag.data) {
              resTag.data.items.forEach((item) => {
                myMap.set(item.fieldValue, item);
              });
              // 复制 - 人群包没有选中任何标签
              if (
                !res.data.containerSet.containerList ||
                res.data.containerSet.containerList.length === 0
              ) {
                copyContainerList = [
                  {
                    key: 0,
                    isDeleted: false,
                    status: 'AND',
                    operator: 'AND',
                    tagList: [],
                  },
                ];
              } else {
                copyContainerList = res.data.containerSet?.containerList?.map(
                  (item, index) => {
                    return {
                      ...item,
                      // ...myMap.get(item.tagId),
                      isDeleted: false,
                      key: index,
                      // 推荐人群(2)、圈选人群(1)，直接添加到运算池，若为标签(3)
                      // crowdType: [6, 12, 14].includes(item.showType) ? item.showType === 12 ? 2 : 1 : 3,
                      status: Number(item.seq === 1)
                        ? 'AND'
                        : res.data.containerSet.aop.split('_')[
                            res.data.containerSet.aop
                              .split('_')
                              .findIndex(
                                (seqItem) => item.seq === Number(seqItem),
                              ) - 1
                          ],
                      tagList: item.tagList.map((tagItem, index) => {
                        return {
                          ...tagItem,
                          ...myMap.get(tagItem.name),
                          crowdType: [6, 12, 14].includes(tagItem.showType)
                            ? tagItem.showType === 12
                              ? 2
                              : 1
                            : 3,
                          idx: index,
                          isTagDelete: false,
                          tagNameItem: myMap.get(tagItem.name)?.levelName,
                          formValueStr: handleCopyFormValueStr({
                            ...tagItem,
                            ...myMap.get(tagItem.name),
                          }),
                          crowdTagName: tagItem.tagDesc,
                          driverCnt: tagItem?.tagSampleSize,
                          mark: tagItem?.tagMark,
                          id: tagItem?.tagId,
                          // 枚举标签、复合标签、复合级联标签老人群包无运算符，新联路兼容标红处理，文本类型标签不允许复制，一律标红处理
                          copyErrorTag: res.data.containerSet?.aop
                            ? tagItem.showType === 5
                            : [2, 10, 13, 5].includes(tagItem.showType),
                          startValue:
                            !res.data.containerSet?.aop &&
                            [2, 10].includes(tagItem.showType)
                              ? ''
                              : tagItem.startValue,
                          scope: tagItem.showType === 5 ? '' : tagItem.scope,
                        };
                      }),
                    };
                  },
                );
              }
              setOperationPoolChange(copyContainerList);
              setCopyOperationPoolChange(copyContainerList);
            }
          });
        }
      });
    }
  }, []);
  return (
    <div className={styles['userPortrait']}>
      <div className={styles['userPortrait-content']}>
        <div className={styles['userPortrait-content_left']}>
          <TagSquare
            formInstance={formInstance}
            operationPoolChange={operationPoolChange}
            setOperationPoolChange={setOperationPoolChange}
          />
        </div>
        <div
          className={styles['userPortrait-content_right']}
          id="userPortrait-content_right"
        >
          <OperationPoolOrg
            ref={operationPoolOrgRef}
            operationPoolChange={operationPoolChange}
            setOperationPoolChange={setOperationPoolChange}
            copyBaseInformation={copyBaseInformation}
            copyOperationPoolChange={copyOperationPoolChange}
          />
        </div>
      </div>
    </div>
  );
};
export default UserPortrait;
