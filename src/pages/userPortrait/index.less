.userPortrait {
  // overflow: auto;
  // margin: 8px;
  height: calc(100vh - 54px);
  background: #f5f6f7;

  &-content {
    display: flex;
    padding: 16px 16px 0;
    height: 100%;
    min-width: 1176px;

    &_left {
      flex: auto;
      min-width: 750px;
      height: 100%;
      background-color: #ffffff;
      margin-right: 16px;
      border-radius: 8px;

      :global {
        .ant-tabs-nav {
          padding: 0 16px;
          // margin-bottom: 0px;
        }
      }
    }

    &_right {
      width: 404px;
      height: 100%;
      background-color: #ffffff;
      border-radius: 8px;
    }
  }
}

:global {
  .ant-cascader-menu:nth-child(2) {
    width: 110px;
    overflow: auto;

    .ant-cascader-menu-item {
      width: 110px;
      overflow: visible;
    }
  }

  .ant-tooltip-inner {
    max-height: 200px;
    overflow: auto;
  }

  .ant-select-multiple.BLMCascader_Antd .ant-select-selection-item {
    //background: transparent !important;
  }
}
