import React, { useState, useRef } from 'react';
import { Tabs, Input, Button } from '@blmcp/ui';
import { SearchOutlined } from '@ant-design/icons';
import TagSelection from './tagSelection/index';
import SelectedPeopleGroup from './selectedPeopleGroup/index';
import type { TabsProps } from '@blmcp/ui';

const TagSquare = ({
  formInstance,
  operationPoolChange,
  setOperationPoolChange,
}) => {
  // 是否需要展示标签搜索表格
  const [isShowSearchTable, setShowSearchTable] = useState(false);
  // 当前搜索项value
  const [tagInputValue, setTagInputValue] = useState('')
  // 当前tabs
  const [tabCurKey, setTabCurKey] = useState('1')
  const tagSearchTableRef = useRef<null | HTMLElement>(null);
  const crowdTableRef = useRef<null | HTMLElement>(null);

  const handleTagSearch = (e, isClear = false) => {
    setTagInputValue(e.target.value)
    if (tabCurKey === '1') {
      if (!isShowSearchTable) {
        setShowSearchTable(true);
      } else {
        tagSearchTableRef?.current?.refreshTableList({
          platformId: 11,
          createSource: 1,
          businessType: 1,
          entityType: 1,
          driverDimension: 2,
          labelName: e.target.value,
        }, true)
      }
    } else {
      crowdTableRef?.current?.refreshTableList({
        platformId: 1,
        businessType: 1,
        entityType: 1,
        fuzzyQueryByIdOrName: e.target.value,
      }, true)
    }

  };
  const handleInputChange = (e) => {
    if (!e.target.value?.length) {
      if (tabCurKey === '1') {
        setShowSearchTable(false);
      } else {
        crowdTableRef?.current?.refreshTableList({
          platformId: 1,
          businessType: 1,
          entityType: 1,
          fuzzyQueryByIdOrName: '',
        }, true)
      }
    }
  };

  const items: TabsProps['items'] = [
    {
      key: '1',
      label: '标签圈选',
      children: (
        <TagSelection
          showSearchTable={isShowSearchTable}
          tagInputValue={tagInputValue}
          formInstance={formInstance}
          operationPoolChange={operationPoolChange}
          setOperationPoolChange={setOperationPoolChange}
          tagSearchTableRef={tagSearchTableRef}
        />
      ),
    },
    {
      key: '2',
      label: '已圈选人群',
      children: (
        <SelectedPeopleGroup
          formInstance={formInstance}
          operationPoolChange={operationPoolChange}
          setOperationPoolChange={setOperationPoolChange}
          crowdTableRef={crowdTableRef}
          tagInputValue={tagInputValue}
        />
      ),
    },
  ];

  const operations = (
    <div style={{ display: 'flex' }}>
      <Input
        type={'search'}
        style={{ width: 260 }}
        placeholder={tabCurKey === '1' ? "搜索标签名称回车确认" : "搜索人群名称/人群ID回车确认"}
        prefix={<SearchOutlined />}
        onPressEnter={(e) => handleTagSearch(e)}
        onChange={(e) => handleInputChange(e)}
        maxLength={20}
        allowClear
      />
      <Button type="link" onClick={() => {
        window.open(
          '/school/doc/logs/details?id=21023#',
        );
      }}>操作手册</Button>
    </div>
  );

  // tabs切换函数
  const handleTabChange = (e) => {
    setShowSearchTable(false);
    setTabCurKey(e)
    setTagInputValue('')
  };
  return (
    <>
      <Tabs
        tabBarExtraContent={operations}
        items={items}
        defaultActiveKey="1"
        activeKey={tabCurKey}
        onChange={(e) => handleTabChange(e)}
      />
    </>
  );
};
export default TagSquare;
