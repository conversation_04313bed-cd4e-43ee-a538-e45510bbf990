import React, { useEffect, useState } from 'react'
import { Form, Space, Radio, Checkbox, Divider, DatePicker, InputNumber, Button, Popover, Input, BLMIconFont, message } from '@blmcp/ui'
import moment from 'moment'
import dayjs from 'dayjs';
import { cloneDeep } from "lodash";

const { RangePicker } = DatePicker;
import styles from './index.less';


const RenderCustomShow = ({ dateModalChange, timesText }) => {
  const [timesInputText, setTimesInputText] = useState(timesText)
  useEffect(() => {
    setTimesInputText(timesText)
  }, [timesText])
  return (
    <Input
      value={timesInputText}
      onClick={dateModalChange}
      placeholder="请选择日期"
      suffix={<BLMIconFont
        type={'BLM-ic-calendar'}
      />}
    />
  )
}

export const driverEditTag = {
  dateCancelType: [
    {
      name: '剔除周六',
      value: 1,
      disabled: true
    },
    {
      name: '剔除周日',
      value: 2,
      disabled: true
    },
    {
      name: '剔除工作日',
      value: 3,
      disabled: true
    }
  ],
  timeScope: [
    // { name: '不限', value: 1 }, 老数据
    { name: '固定时间', value: 2 },
    { name: '动态时间', value: 3 }
  ]
}

interface ISelfProps {
  // curLeafNodeInfo: ILevelTree;
  // form: Pick<FormInstance, 'getFieldValue' | 'setFieldsValue'>,
  // extra: any;
  /** 使用的是Form.Item传入的 */
  // value?: IRulesDetail;
  /** 使用的是Form.Item传入的 */
  // onChange?: (data: IRulesDetail, extra: any) => void;
}

const TimeRangePicker = (props: ISelfProps) => {
  const [modalForm] = Form.useForm()
  const [visible, setVisible] = useState<boolean>(false)
  const [curValue, setCurValue] = useState<IRulesDetail>({})
  const [curValueCopy, setCurValueCopy] = useState<IRulesDetail>({})

  // 时间卡片展示状态
  const [datePopoverOpen, setDatePopoverOpen] = useState(false)
  const [pickerOpen, setPickerOpen] = useState(false)
  // 数据回显
  const [timesText, setTimesText] = useState(props.timeTextCopy)
  const [tagItemInfo, setTagItemInfo] = useState()

  const [dateCancelTypeOption, setDateCancelTypeOption] = useState(cloneDeep(driverEditTag.dateCancelType))

  /** 判断日期类型禁用 */
  const judgeDateCancelTypeDisabled = () => {
    let dateCancelTypeOptionCopy = cloneDeep(dateCancelTypeOption)
    const dateCancelType = modalForm.getFieldValue('dateCancelType')
    // 判断是固定时间的时候 有时间才可选中剔除选项
    const valueType = modalForm.getFieldValue('valueType')
    const fixedRangeTime = modalForm.getFieldValue('fixedRangeTime')
    if (valueType === 2) { // 固定时间
      if (Array.isArray(fixedRangeTime) && fixedRangeTime.length === 2) {
        dateCancelTypeOptionCopy = dateCancelTypeOptionCopy.map(item => {
          // 固定时间下 长度超过两个第三个禁用
          if (Array.isArray(dateCancelType) && dateCancelType.length > 1) {
            if (dateCancelType.includes(item.value)) {
              item.disabled = false
              return item
            } else {
              item.disabled = true
              return item
            }
          } else {
            item.disabled = false
            return item
          }
        })
      } else {
        dateCancelTypeOptionCopy = dateCancelTypeOptionCopy.map(item => {
          item.disabled = true
          return item
        })
      }
    } else if (Array.isArray(dateCancelType) && dateCancelType.length > 1) { // 动态时间
      dateCancelTypeOptionCopy = dateCancelTypeOptionCopy.map(item => {
        if (dateCancelType.includes(item.value)) {
          item.disabled = false
          return item
        } else {
          item.disabled = true
          return item
        }
      })
    } else {
      dateCancelTypeOptionCopy = dateCancelTypeOptionCopy.map(item => {
        item.disabled = false
        return item
      })
    }
    setDateCancelTypeOption(dateCancelTypeOptionCopy)
  }

  /** 当前 */

  useEffect(() => {
    const data: IRulesDetail = JSON.parse(JSON.stringify(props.value || {}))
    setCurValue(data)
    setCurValueCopy(data)
    setTagItemInfo(props.tagItem)
  }, [props.value, props.tagItem])
  useEffect(() => {
    // 时间组件的下拉框关闭后再关闭Popover
    if (!pickerOpen) {
      setDatePopoverOpen(false)
    }
  }, [pickerOpen])
  useEffect(() => {
    // 时间组件展开的时候 做回显逻辑
    if (datePopoverOpen && tagItemInfo) {
      const { valueType, dateCancelType, startDate, endDate, subStart, subEnd, dyStartDateType } = tagItemInfo
      const itemInfo = {
        valueType: curValue?.valueType || valueType,
        dateCancelType: curValue?.dateCancelType || dateCancelType,
        startDate: curValue?.startDate || startDate,
        endDate: curValue?.endDate || endDate,
        subStart: curValue?.subStart || subStart,
        subEnd: curValue?.subEnd || subEnd,
        dyStartDateType: curValue?.dyStartDateType || dyStartDateType,
      }
      itemInfo.valueType && modalForm.setFieldValue('valueType', itemInfo.valueType) // 2:具体时间  3:动态时间
      itemInfo.dateCancelType && modalForm.setFieldValue('dateCancelType', itemInfo.dateCancelType?.split(',').map(Number)) // 1周六 2周日 3工作日
      if (itemInfo.valueType === 2 && itemInfo.startDate && itemInfo.endDate) {
        const times = []
        times[0] = dayjs.unix(itemInfo.startDate / 1000)
        times[1] = dayjs.unix(itemInfo.endDate / 1000)
        modalForm.setFieldValue('fixedRangeTime', times)
      } else if (itemInfo.valueType === 3 && itemInfo.subEnd) {
        if (itemInfo.dyStartDateType === 1) {
          const fixedStartDateCopy = itemInfo.startDate ? dayjs.unix(itemInfo.startDate / 1000) : dayjs().subtract(1, 'day')
          modalForm.setFieldValue('fixedStartDate', fixedStartDateCopy)
          modalForm.setFieldValue('dyStartDateType', 1) // 具体日期
          modalForm.setFieldValue('subEnd', itemInfo.subEnd)
          setCurValue({ ...curValue, fixedStartDate: fixedStartDateCopy})
        } else {
          modalForm.setFieldValue('subStart', itemInfo.subStart)
          modalForm.setFieldValue('dyStartDateType', 2)
          modalForm.setFieldValue('subEnd', itemInfo.subStart >= itemInfo.subEnd ? itemInfo.subEnd : 1)
        }
      }
      judgeDateCancelTypeDisabled()
    }
  }, [datePopoverOpen, tagItemInfo])

  /** 新 固定时间时间限制 */
  const fixedRangeDisabledTime = (current) => {
    // 可选时间为过去1天至过去90天
    const tooEarly =
      current < dayjs().subtract(91, 'days').endOf('day').valueOf();
    const tooLate =
      current > dayjs().subtract(1, 'days').endOf('day').valueOf();
    return tooEarly || tooLate;
  };




  /** 时间类型变动 */
  const onValueTypeChange = (e) => {
    const { target: { value } } = e
    const formValues: any = modalForm.getFieldsValue()
    const currentValue = { ...curValue }
    if (value === 3) {
      // dyStartDateType === 1 为选则固定动态时间 开始时间为具体日期
      if (formValues.dyStartDateType === 1 || currentValue.dyStartDateType === 1) {
        formValues.dyStartDateType = 1
        formValues.subEnd = curValue.subEnd || 1
        formValues.fixedStartDate = curValue.fixedStartDate || dayjs().subtract(1, 'day')
        currentValue.subEnd = curValue.subEnd || 1
        currentValue.dyStartDateType = 1
        currentValue.valueType = value
      } else {
        formValues.dyStartDateType = 2
        formValues.subEnd = curValue.subEnd || 1
        // 开始时间默认设置为1
        formValues.subStart = curValue.subStart || 1

        currentValue.valueType = value
        currentValue.dyStartDateType = 2
        currentValue.subEnd = curValue.subEnd || 1
        // 开始时间默认设置为1
        currentValue.subStart = curValue.subStart || 1
      }
    }
    setCurValue(currentValue)
    modalForm.setFieldsValue(formValues)
    judgeDateCancelTypeDisabled()
  }

  const renderTotalDaysNum = (values) => {
    const { valueType, subStart, subEnd, dyStartDateType, fixedStartDate } = values
    let dom: React.ReactNode = null
    if (valueType === 3) {
      if (dyStartDateType === 1) {
        // 具体时间
        dom = dayjs().subtract(subEnd, 'day').diff(dayjs(fixedStartDate), 'day') + 1
      } else if (curValue.dyStartDateType === 1) {
        dom = dayjs().subtract(curValue.subEnd, 'day').diff(dayjs(curValue.fixedStartDate), 'day') + 1
      } else {
        // 非具体时间
        // 肯定不是NaN 未了解决react报错进行判断
        if (!isNaN(Number(subStart)) && !isNaN(Number(subEnd))) {
          dom = subStart - subEnd + 1
        }
      }
    }
    return <span>{dom}</span>
  }
  const renderStartTime = ({ getFieldsValue }) => {
    const { valueType, subStart, dyStartDateType, fixedStartDate } = getFieldsValue()
    if (valueType === 3) {
      if (dyStartDateType === 1) {
        return <span className="ant-color-bule">{dayjs(fixedStartDate).format('YYYY年MM月DD日')}</span>
      } else if (curValue.dyStartDateType === 1) {
        return <span className="ant-color-bule">{dayjs(curValue.fixedStartDate).format('YYYY年MM月DD日')}</span>
      } else {
        return <span>
          过去 <span className="ant-color-bule" >{`${subStart}`.replace(/[^0-9]/g, '')}</span> 日
        </span>
      }
    } else {
      return null
    }
  }

  /** 设置固定开始默认,设置有值即为开启 ，没值则未关闭 */
  const onSetFixedStartDate = (value) => {
    // const { dyStartDateType } = modalForm.getFieldsValue()
    if (value === 1) {
      modalForm.setFieldsValue({
        dyStartDateType: 1,
        fixedStartDate: curValue.fixedStartDate || dayjs().subtract(1, 'day'),
        subEnd: 1
      })
    } else {
      modalForm.setFieldsValue({
        dyStartDateType: 2,
        subStart: curValue.subStart || 1,
        subEnd: 1
      })
    }
    setCurValue({
      ...curValue,
      subEnd: 1,
      dyStartDateType: value
    })
    modalForm.setFields([
      {
        name: 'subStart',
        errors: undefined
      },
      {
        name: 'fixedStartDate',
        errors: undefined
      }
    ])
  }

  /** 监听特殊日期变化 */
  const onDateCancelTypeChange = (val) => {
    setCurValue({
      ...curValue,
      dateCancelType: val
    })

    judgeDateCancelTypeDisabled()
  }
  /** 监听动态时间开始日期变化 */
  const onSubStartChange = (val) => {
    setCurValue({
      ...curValue,
      subStart: val
    })
  }
  /** 监听动态时间开始变化 */
  const onSubEndChange = (val) => {
    setCurValue({
      ...curValue,
      subEnd: val
    })
  }


  /** 动态时间 开始具体日期 禁用判断  */
  const fixedStartDateDisable = (date) => {
    const { subEnd } = modalForm.getFieldsValue()
    if (dayjs(date).isBefore(dayjs().subtract(91, 'days'))) {
      return true
    }
    // 时间选择器可选时间截止到最近一天
    if (dayjs(date).isAfter(dayjs().subtract(subEnd, 'days'), 'days')) {
      return true
    }
    return false
  }
  const onFixedStartDateChange = () => {
    const { fixedStartDate } = modalForm.getFieldsValue()
    setCurValue({
      ...curValue,
      fixedStartDate: fixedStartDate,
      startDate: dayjs(fixedStartDate).valueOf()
    })
  }

  /** 监听动态 开始日期 输入，如果小于动态结束日期动态设置成结束日期+1  */
  const getSubStartFormEvent = (value) => {
    const { subEnd } = modalForm.getFieldsValue()
    // const max = props.curLeafNodeInfo?.showType === 1 ? 999 : 90
    const max = 90
    let useValue = Number(`${value}`.replace(/[^0-9]/g, ''))
    if (value) {
      // 0511 修改开始时间可等于结束时间
      if (value < subEnd) {
        useValue = subEnd
      } else {
        useValue = value
      }
    } else {
      useValue = subEnd
    }
    if (useValue > max) {
      useValue = max
    }
    return useValue
  }
  const getSubEndFormEvent = (value) => {
    const { subStart, fixedStartDate, dyStartDateType } = modalForm.getFieldsValue()
    const max = 90
    let useValue = Number(`${value}`.replace(/[^0-9]/g, ''))
    if (value) {
      if (dyStartDateType === 1) {
        const diffDayFormCurDay = dayjs().diff(dayjs(fixedStartDate), 'day')
        // 0511 结束日期可等于开始日期
        if (value >= diffDayFormCurDay) {
          useValue = diffDayFormCurDay
        } else {
          useValue = value
        }
      } else {
        if (value >= subStart) {
          useValue = subStart
        } else {
          useValue = value
        }
      }
    } else {
      useValue = 1
    }
    if (useValue > max) {
      useValue = max
    }
    return useValue
  }

  // 固定时间组件选项改变
  const RangePickeraaBlur = (dates: [dayjs, dayjs], dateStrings: [string, string]) => {
    judgeDateCancelTypeDisabled()
  }

  // 点击确定按钮
  const saveTimePopver = () => {
    // valueType: 2固定时间， 3动态时间
    const { valueType, dateCancelType, fixedRangeTime, subStart, subEnd, fixedStartDate, dyStartDateType } = modalForm.getFieldsValue()
    let itemInfo = {
      valueType, // 时间类型
      dateCancelType: dateCancelType?.join(',') ?? '', // 剔除时间
      startDate: null, // 时间戳
      endDate: null, // 时间戳
      dyStartDateType: null, // 动态时间-具体/非具体时间
      subStart: null,
      subEnd: null,
      timesText: '',
    }

    // 拼接剔除字符串
    let dateCancelTypeNameArr = []
    driverEditTag.dateCancelType.forEach(item => {
      if (dateCancelType?.includes(item.value)) {
        dateCancelTypeNameArr.push(item.name)
      }
    })
    // 固定时间参数
    if (valueType === 2) {
      let times = [null, null]
      // let timesDFormat = [null, null]
      if (fixedRangeTime) {
        fixedRangeTime?.forEach((item: any, index: number) => {
          times[index] = dayjs(item).valueOf()
          // timesDFormat[index] = dayjs(item).unix()
        })
        const timesText = `${dayjs(times[0]).format('YYYY-MM-DD')} 至 ${dayjs(times[1]).format('YYYY-MM-DD')}${dateCancelTypeNameArr?.length ? ' | ' + dateCancelTypeNameArr.join(',') : ''}`
        itemInfo.startDate = times[0] // 时间戳
        itemInfo.endDate = times[1]
        itemInfo.timesText = timesText
        setTimesText(timesText)
      } else {
        message.warning('请选择时间')
        return false
      }

    } else if (valueType === 3) {
      let subStartTime = subStart
      let subStartTimeText = `过去${subStartTime}日`
      // 具体日期
      if (dyStartDateType === 1) {
        subStartTime = dayjs(fixedStartDate).format('YYYY-MM-DD')
        subStartTimeText = subStartTime
        itemInfo.startDate = dayjs(fixedStartDate).valueOf()
      } else {
        itemInfo.subStart = subStartTime
      }
      const timesText = `${subStartTimeText} 至 过去${subEnd}日 ${dateCancelTypeNameArr?.length ? ' | ' + dateCancelTypeNameArr.join(',') : ''}`
      // 动态时间参数
      itemInfo.dyStartDateType = dyStartDateType
      itemInfo.subEnd = subEnd
      itemInfo.timesText = timesText
      setTimesText(timesText)
    }

    // 回调
    props.onChange(itemInfo)
    setCurValueCopy({ ...curValueCopy, ...itemInfo })
    // 关闭弹窗
    setPickerOpen(false)
  }
  // 关闭弹窗事件
  const closePopover = () => {
    // 点击取消按钮重置curValue值
    setCurValue({ ...curValueCopy })
    // 关闭弹窗
    setPickerOpen(false)
  }


  const popoverContent = (
    <div className={styles['time-range-popover-content']}>
      <Form form={modalForm} initialValues={{ 'valueType': 2 }}>
        <div className="modal-left">
          <Form.Item name="valueType" rules={[{ required: true, message: '请选择' }]} style={{ marginBottom: 16 }}>
            <Radio.Group buttonStyle='solid' onChange={onValueTypeChange} optionType="button">
              <Space direction='vertical' >
                {driverEditTag.timeScope.map((item) => {
                  return <Radio style={{ textAlign: 'center', width: 105 }} value={item.value} key={item.value} >{item.name}</Radio>
                })}
              </Space>
            </Radio.Group>
          </Form.Item>
          {/* 时间取消类型 */}
          <Form.Item name="dateCancelType" >
            <Checkbox.Group onChange={onDateCancelTypeChange} >
              <Space direction='vertical' >
                {dateCancelTypeOption.map((item) => {
                  return <div key={item.value} className="check-box-btn">
                    <Checkbox disabled={item.disabled} value={item.value} >{item.name}</Checkbox>
                  </div>
                })}
              </Space>
            </Checkbox.Group>
          </Form.Item>
        </div>
        <Divider type='vertical' style={{ margin: '0 8px' }} />
        <div className='modal-right'>
          <Form.Item noStyle shouldUpdate={true} >
            {(form) => {
              const { valueType: curValueType, dyStartDateType = curValue.dyStartDateType, subEnd = 1 } = form.getFieldsValue()
              if (curValueType === 2) {
                // 固定时间
                return <Form.Item name="fixedRangeTime" rules={[{ required: true, message: '请选择' }]}>
                  <RangePicker
                    open={pickerOpen}
                    style={{ width: '540px' }} onChange={RangePickeraaBlur} disabledDate={fixedRangeDisabledTime} placeholder={['开始时间', '结束时间']} variant="borderless" allowClear={false} />
                </Form.Item>
              } else if (curValueType === 3) {
                // 动态时间
                return <div style={{ marginLeft: 8 }}>
                  {/* <div style={{ marginBottom: 24, fontWeight: 500, color: 'rgba(0, 0, 0, 0.6)' }} > */}
                  <div style={{ marginBottom: 24, fontWeight: 500 }} >
                    共 <span className="ant-color-bule" >{renderTotalDaysNum(form.getFieldsValue())}</span>日
                    <Divider type='vertical' style={{ height: 'auto' }} />
                    {renderStartTime(form)}
                    至过去 <span className="ant-color-bule"> {subEnd}</span>日
                  </div>
                  <Space className={styles['startTimeForm']}>
                    {/* 非具体时间 */}
                    <Form.Item hidden={dyStartDateType === 1} label="开始日期" required={true}>
                      <Space>
                        过去
                        <Form.Item name='subStart' noStyle getValueFromEvent={getSubStartFormEvent} rules={[{ required: dyStartDateType !== 1, message: '请输入' }]}>
                          {/* 开始时间最小值限制为结束时间 */}
                          <InputNumber placeholder="请输入" onChange={onSubStartChange} min={subEnd} max={90} disabled={(dyStartDateType === 1)} />
                        </Form.Item>
                        日
                      </Space>
                    </Form.Item>
                    {/* 具体时间 */}
                    <Form.Item hidden={dyStartDateType !== 1} label="开始日期" required={true}>
                      <Space>
                        <Form.Item name="fixedStartDate" noStyle rules={[{ required: dyStartDateType === 1, message: '请选择' }]}>
                          <DatePicker onChange={onFixedStartDateChange} disabledDate={fixedStartDateDisable} placeholder='请选择' allowClear={false} />
                        </Form.Item>
                      </Space>
                    </Form.Item>
                    <Form.Item name="dyStartDateType">
                      {
                        dyStartDateType === 1 ? (
                          <Button type='link' onClick={()=>onSetFixedStartDate(2)} >取消具体日期</Button>
                        ) : (
                          <Button type='link' onClick={()=>onSetFixedStartDate(1)} >选择具体日期</Button>
                        )
                      }

                      {/*<Button type='link' onClick={onSetFixedStartDate} >{dyStartDateType === 1 ? '取消' : '选择'}具体日期</Button>*/}
                    </Form.Item>
                  </Space>

                  <Form.Item label="结束日期" required={true} style={{ marginBottom: 16 }}>
                    <Space>
                      过去
                      <Form.Item name='subEnd'
                        getValueFromEvent={getSubEndFormEvent}
                        noStyle
                        rules={[
                          { required: true, message: '请输入' },
                          {
                            validator: () => {
                              const { subStart } = form.getFieldsValue()
                              return new Promise((resolve, reject) => {
                                if (dyStartDateType === 2) {
                                  if (+subStart < +subEnd) {
                                    reject('结束日期必须小于等于开始日期')
                                  } else {
                                    resolve(true)
                                  }
                                } else {
                                  resolve(true)
                                }
                              })
                            }
                          }
                        ]}>
                        <InputNumber onChange={onSubEndChange} placeholder="请输入" min={1} max={90} />
                      </Form.Item>
                      日
                    </Space>
                  </Form.Item>
                  <div style={{ marginBottom: 16 }}>
                    备注：开始日期要大于等于结束日期
                  </div>
                  <Form.Item noStyle shouldUpdate={true} >
                    {() => {
                      const { subStart, subEnd = curValue.subEnd, fixedStartDate = curValue.fixedStartDate } = form.getFieldsValue()
                      let startDateStr: React.ReactNode = null
                      let endDateStr: React.ReactNode = null
                      if (dyStartDateType === 1) {
                        startDateStr = dayjs(fixedStartDate).format('YYYY年MM月DD日')
                      } else {
                        startDateStr = dayjs().subtract(subStart, 'day').format('YYYY年MM月DD日')
                      }
                      endDateStr = dayjs().subtract(subEnd, 'day').format('YYYY年MM月DD日')
                      return <div style={{ marginBottom: 16 }}>
                        例如：当前日期<span className="ant-color-bule" >{dayjs().format('YYYY年MM月DD日')}</span>，
                        {renderStartTime(form)}
                        至过去
                        <span className="ant-color-bule">{form.getFieldValue('subEnd')}</span> 日，
                        <span>即<span className="ant-color-bule" >{startDateStr}</span> 至<span className="ant-color-bule" >{endDateStr}</span></span>
                      </div>
                    }}
                  </Form.Item>
                </div>
              } else {
                return null
              }
            }}
          </Form.Item>
        </div>
      </Form>
      <div className={styles['btn-wrap']} >
        <Button style={{ marginRight: 8 }} onClick={() => closePopover()} >取消</Button>
        {/* <Button type='primary' onClick={onOk} >确认</Button> */}
        <Button style={{ marginRight: 8 }} type='primary' onClick={() => { saveTimePopver() }} >确认</Button>
      </div>
    </div>
  )

  return <div className={styles['time-range-picker-wrap']}>
    <Popover
      trigger='click'
      content={popoverContent}
      placement="bottomLeft"
      arrow={false}
      open={datePopoverOpen}
      onOpenChange={(e) => setPickerOpen(e)}
    // style={{ width: 700 }}
    >
      <RenderCustomShow timesText={timesText} dateModalChange={() => { setDatePopoverOpen(true); setPickerOpen(true) }} />
    </Popover>
  </div>
}

export default TimeRangePicker
