import TreeSelectCom from '@/components/TreeSelectCom';
import { Form, Input, InputNumber, Radio, Select, Spin } from '@blmcp/ui';
import dayjs from 'dayjs';
import TimeRangePicker from './TimeRangePicker';

// input输入框,文本
const renderInputAuto = (tagItem) => {
  return (
    <>
      <Form.Item
        name={`startValueText`}
        rules={[
          {
            required: true,
            validator: (_, value) => {
              // 校验内容规则：
              //   1）司机端版本号为数字+英文句号【.】点分割，四部分组成，如：5.20.5.0000，可输入位数为11位
              //   2）第一位：单个数字，小于10
              //   3）第二位：英文句号【.】点分割
              //   4）第三四位：必须能被10整除
              //   5）第五位：英文句号【.】点分割
              //   6）第六位：单个数字，小于10
              //   7）第七位：英文句号【.】点分割
              //   8）第八至十一位：四位数字
              const p = value && value.split('.');
              if (
                p &&
                p.length === 4 &&
                /^\d+$/.test(p[0]) &&
                p[0] >= 0 &&
                p[0] < 10 &&
                p[0].length === 1 &&
                /^\d+$/.test(p[1]) &&
                p[1] % 10 === 0 &&
                p[1].length === 2 &&
                /^\d+$/.test(p[2]) &&
                p[2] >= 0 &&
                p[2] < 10 &&
                p[2].length === 1 &&
                /^\d+$/.test(p[3]) &&
                p[3].length === 4
              ) {
                return Promise.resolve();
              } else {
                return Promise.reject('版本号格式错误');
              }
            },
          },
        ]}
        extra="版本号格式：4.90.0.0967"
      >
        <Input style={{ minWidth: 240 }} placeholder="请输入版本号" />
      </Form.Item>
    </>
  );
};
// input输入框,List
const renderInputListAuto = (tagItem) => {
  return (
    <Form.Item
      name={'startValueList'}
      rules={[
        {
          required: true,
          validator: (_, value) => {
            if (!value || /，/.test(value))
              return Promise.reject('请输入正确的名称，最多支持20个');
            if (typeof value === 'string') {
              const list = value.split(',') || [];
              const flag = list.every((s) =>
                /^[A-Za-z\d\u4e00-\u9fa5]+$/.test(s),
              );
              if (!flag || list.length > 20)
                return Promise.reject('请输入正确的名称，最多支持20个');
            }
            return Promise.resolve();
          },
        },
      ]}
      extra="请输入名称，多个名称用逗号,隔开"
    >
      <Input style={{ minWidth: 310 }} placeholder="请输入" />
    </Form.Item>
  );
};
// 单选框
const renderRadioAuto = (tagItem) => {
  return (
    <Form.Item
      name="startValue"
      rules={[{ required: true, message: '请选择' }]}
    >
      <Radio.Group
        options={tagItem.enumValueParams}
        style={{ maxWidth: 528 }}
      ></Radio.Group>
    </Form.Item>
  );
};
const renderDateAuto = (tagItem, form, isEdit) => {
  const DaysEnum = {
    1: '周六',
    2: '周日',
    3: '工作日',
  };
  function generateExclusionString(daysString) {
    const daysArray = daysString.split(',').map(Number);
    let exclusionString = '剔除';

    daysArray.forEach((day, index) => {
      exclusionString += DaysEnum[day];
      if (index < daysArray.length - 1) {
        exclusionString += '、';
      }
    });

    return exclusionString;
  }
  const handleTimeTextCopy = () => {
    let timesText = '';
    if (isEdit) {
      if (tagItem.valueType === 2) {
        timesText = `${dayjs(tagItem.startDate).format(
          'YYYY-MM-DD',
        )} 至 ${dayjs(tagItem.endDate).format('YYYY-MM-DD')}${
          tagItem.dateCancelType?.length
            ? ' | ' + generateExclusionString(tagItem.dateCancelType)
            : ''
        }`;
      } else if (tagItem.valueType === 3) {
        if (tagItem.dyStartDateType === 1) {
          timesText = `${dayjs(tagItem.startDate).format(
            'YYYY-MM-DD',
          )} 至 过去${tagItem.subEnd}日 ${
            tagItem.dateCancelType?.length
              ? ' | ' + generateExclusionString(tagItem.dateCancelType)
              : ''
          }`;
        } else {
          timesText = `过去${tagItem.subStart}日 至 过去${tagItem.subEnd}日 ${
            tagItem.dateCancelType?.length
              ? ' | ' + generateExclusionString(tagItem.dateCancelType)
              : ''
          }`;
        }
      }
    }
    return timesText;
  };
  const timesValueChange = (item) => {
    const {
      valueType,
      dateCancelType,
      startDate,
      endDate,
      subStart,
      subEnd,
      timesText,
      dyStartDateType,
    } = item;
    let itemInfo = {
      valueType, // 时间类型
      dateCancelType, // 剔除时间
      startDate, // 时间戳
      endDate, // 时间戳
      dyStartDateType, // 非/具体时间
      subStart,
      subEnd,
      timesText: timesText ?? handleTimeTextCopy(),
    };
    form.setFields([
      {
        name: 'timesValue',
        value: { ...itemInfo },
      },
    ]);
  };

  return (
    <Form.Item
      name={'timesValue'}
      rules={[{ required: true, message: '请选择日期' }]}
    >
      <TimeRangePicker
        onChange={(item) => timesValueChange(item)}
        tagItem={tagItem}
        timeTextCopy={handleTimeTextCopy()}
      />
    </Form.Item>
  );
};
const renderInputNumberAuto = (tagItem, form, isInterval) => {
  const compareToFirstNumber = (_, value) => {
    const firstNumber = form.getFieldValue('numberStartValue');

    if (Number(value) <= firstNumber && isInterval) {
      return Promise.reject('第二个数字必须大于第一个数字');
    } else {
      form.setFields([
        {
          name: 'numberStartValue',
          errors: [],
        },
      ]);
      return Promise.resolve();
    }
  };
  return (
    <Form.Item
      name="numberEndValue"
      rules={[
        { required: true, message: '请选择' },
        { validator: compareToFirstNumber },
      ]}
    >
      <InputNumber
        addonAfter={tagItem.unit}
        style={{ maxWidth: 100 }}
        min={0}
        max={999999}
      />
    </Form.Item>
  );
};
// select选择器
const renderSelectTypeFomula = (tagItem) => {
  return (
    <Form.Item
      name={'calType'}
      rules={[{ required: true, message: '请选择计算符' }]}
    >
      <Select
        placeholder="请选择计算符"
        options={tagItem.fomulaRelationList}
        style={{ minWidth: 124 }}
      ></Select>
    </Form.Item>
  );
};
const renderSelectTypeCal = (tagItem, form, isInterval, setInterval) => {
  const handleChangeCal = (e) => {
    // 若选择区间，手动塞入值
    if (e === 1) {
      setInterval(true);
    } else {
      setInterval(false);
    }
  };
  const validateFirstNumber = (_, value) => {
    const secondNumber = form.getFieldValue('numberEndValue');

    if (Number(value) >= secondNumber) {
      return Promise.reject('第一个数字必须小于第二个数字');
    } else {
      form.setFields([
        {
          name: 'numberEndValue',
          errors: [],
        },
      ]);
      return Promise.resolve();
    }
  };
  return (
    <>
      <Form.Item
        // key={selectType}
        name={'scope'}
        style={{ minWidth: 124 }}
        rules={[{ required: true, message: '请选择运算符' }]}
      >
        <Select
          placeholder="请选择运算符"
          options={tagItem.calRelationList}
          style={{ minWidth: 124 }}
          onChange={(e) => handleChangeCal(e)}
        ></Select>
      </Form.Item>
      {isInterval && [3, 4].includes(tagItem.showType) && (
        <Form.Item
          name={'numberStartValue'}
          rules={[
            { required: true, message: '请选择' },
            { validator: validateFirstNumber },
          ]}
        >
          <InputNumber
            addonAfter={'至'}
            style={{ maxWidth: 100 }}
            min={0}
            max={999999}
          />
        </Form.Item>
      )}
    </>
  );
};
const renderSelectTypeEnum = (tagItem) => {
  return (
    <Form.Item
      name={'startValueEnum'}
      style={{ minWidth: 124 }}
      rules={[{ required: true, message: '请选择枚举' }]}
    >
      <Select
        placeholder="请选择枚举"
        options={tagItem.enumValueParams}
        style={{ minWidth: 124 }}
      ></Select>
    </Form.Item>
  );
};

// 复合级联标签
const renderTreeSelectCom = (
  tagItem,
  treeDataSource,
  form,
  isEdit,
  editCasderData,
) => {
  const handleTreeSelectChange = (treeInfo, checkedKeys) => {
    const treeSelectValueHandle = treeInfo.map((item) => {
      let valueList = [];
      let valueNameList = [];
      item?.childNodeList.map((item) => {
        valueList.push(item.nodeId);
        valueNameList.push(item.nodeName);
      });
      if (tagItem.subTagLevel === 1) {
        return [
          {
            field: item.labelKey,
            level: 1,
            scope: 8,
            value: item.nodeId,
            valueName: item.nodeName,
          },
        ];
      } else {
        return [
          {
            field: item.labelKey,
            level: 1,
            scope: 8,
            value: item.nodeId,
            valueName: item.nodeName,
          },
          {
            field: item.childNodeList[0]?.labelKey,
            level: 2,
            scope: 8,
            value: valueList.join(','),
            valueName: valueNameList.join(','),
          },
        ];
      }
    });
    form.setFields([
      {
        name: 'startValueTreeSelect',
        value: treeSelectValueHandle,
      },
    ]);
  };

  return (
    <Form.Item
      name={'startValueTreeSelect'}
      style={{ minWidth: 124 }}
      rules={[{ required: true, message: '请选择' }]}
    >
      {treeDataSource.length ? (
        <TreeSelectCom
          options={treeDataSource} // 绑定的源数据
          defaultCheckedKeys={
            !tagItem.copyErrorTag && isEdit ? editCasderData : []
          } // 源数据 默认选中节点
          pid={'parentId'} // 父级id的属性名 （默认为 pid 字段）
          defaultProps={{
            label: 'nodeName',
            value: 'nodeUniqueFlag',
            children: 'childNodeList',
          }} // 自定义节点 label、value、children 的字段
          onChange={(treeInfo, checkedKeys) =>
            handleTreeSelectChange(treeInfo, checkedKeys)
          } // 选项改变的change事件； treeInfo：选中的节点(树结构); checkedKeys:选中的节点的id集合
        ></TreeSelectCom>
      ) : (
        <Spin />
      )}
    </Form.Item>
  );
};

export {
  renderDateAuto,
  renderInputAuto,
  renderInputListAuto,
  renderInputNumberAuto,
  renderRadioAuto,
  renderSelectTypeCal,
  renderSelectTypeEnum,
  renderSelectTypeFomula,
  renderTreeSelectCom,
};
