import { Form, Modal } from '@blmcp/ui';
import dayjs from 'dayjs';
import { useEffect, useState } from 'react';
import { queryCombineLabelData, queryCombineLabelDataNew } from '../../api';
import {
  renderDateAuto,
  renderInputAuto,
  renderInputListAuto,
  renderInputNumberAuto,
  renderRadioAuto,
  renderSelectTypeCal,
  renderSelectTypeEnum,
  renderSelectTypeFomula,
  renderTreeSelectCom,
} from './components/labelFormControl';

const LabelBaseControls = ({
  tagItem,
  handleClickOk,
  isModalOpen,
  setModalOpen,
  operationPoolChange,
  setOperationPoolChange,
  isEdit,
  field,
}) => {
  const [form] = Form.useForm();
  // 当前弹窗是否展示区间类型
  const [isInterval, setInterval] = useState(false);
  // 符合级联标签数据源
  const [treeDataSource, setTreeDataSource] = useState([]);
  // 复合级联回填数据源
  const [editCasderData, setEditCasderData] = useState([]);
  // 标签类型对应展示枚举
  const tagTypeEnum = {
    1: ['date'],
    2: ['radio'],
    3: ['date', 'renderSelectTypeFomula', 'renderSelectTypeCal', 'number'],
    4: ['renderSelectTypeCal', 'number'],
    5: ['renderSelectTypeCal', 'inputText'],
    9: ['renderSelectTypeCal', 'inputList'],
    10: ['radio'],
    11: [
      'date',
      'renderSelectTypeFomula',
      'renderSelectTypeCal',
      'renderSelectTypeEnum',
    ],
    13: ['casder'],
  };

  // 处理组件映射
  const handleLabelControls = (type) => {
    if (type === 'renderSelectTypeFomula') {
      return renderSelectTypeFomula(tagItem);
    } else if (type === 'renderSelectTypeCal') {
      return renderSelectTypeCal(tagItem, form, isInterval, setInterval);
    } else if (type === 'renderSelectTypeEnum') {
      return renderSelectTypeEnum(tagItem);
    } else if (type === 'radio') {
      return renderRadioAuto(tagItem);
    } else if (type === 'date') {
      return renderDateAuto(tagItem, form, isEdit);
    } else if (type === 'number') {
      return renderInputNumberAuto(tagItem, form, isInterval);
    } else if (type === 'inputText') {
      return renderInputAuto(tagItem);
    } else if (type === 'inputList') {
      return renderInputListAuto(tagItem);
    } else if (type === 'casder') {
      return renderTreeSelectCom(
        tagItem,
        treeDataSource,
        form,
        isEdit,
        editCasderData,
      );
    } else {
      return;
    }
  };
  const handleTagFormOk = () => {
    form.validateFields().then(() => {
      if (tagItem.copyErrorTag === true) {
        const currOperationPoolChange = [...operationPoolChange];
        currOperationPoolChange[field.key]?.tagList.splice(tagItem.idx, 1, {
          ...operationPoolChange[field.key]?.tagList[tagItem.idx],
          copyErrorTag: false,
        });
        setOperationPoolChange(currOperationPoolChange);
      }
      setModalOpen(false);
      handleClickOk(isModalOpen.key, form.getFieldsValue(), {
        ...tagItem,
        copyErrorTag: false,
      });
    });
  };
  useEffect(() => {
    // 弹窗打开，重置form校验状态
    if (isModalOpen) {
      form.resetFields();
      if (isModalOpen.openStatus && tagItem.showType === 13 && !isEdit) {
        const combineLabelFn = window.$BLMReleaseCenter.getSceneSwitch(
          'crowdValidity',
        )
          ? queryCombineLabelDataNew
          : queryCombineLabelData;
        combineLabelFn({ labelId: tagItem.id }).then((res) => {
          if (res && res.data) {
            setTreeDataSource(res.data);
          }
        });
      }
      // 弹窗关闭，重置当前弹窗区间状态
    } else {
      setInterval(false);
    }
  }, [isModalOpen, tagItem]);

  const DaysEnum = {
    1: '周六',
    2: '周日',
    3: '工作日',
  };
  function generateExclusionString(daysString) {
    const daysArray = daysString.split(',').map(Number);
    let exclusionString = '剔除';

    daysArray.forEach((day, index) => {
      exclusionString += DaysEnum[day];
      if (index < daysArray.length - 1) {
        exclusionString += '、';
      }
    });

    return exclusionString;
  }
  // 点击编辑时 时间类型组件的回显数据
  const getTimesText = (tagItem) => {
    let timesText = '';
    if (tagItem.valueType === 2) {
      timesText = `${dayjs(tagItem.startDate).format('YYYY-MM-DD')} 至 ${dayjs(
        tagItem.endDate,
      ).format('YYYY-MM-DD')}${
        tagItem.dateCancelType?.length
          ? ' | ' + generateExclusionString(tagItem.dateCancelType)
          : ''
      }`;
    } else if (tagItem.valueType === 3) {
      if (tagItem.dyStartDateType === 1) {
        timesText = `${dayjs(tagItem.startDate).format('YYYY-MM-DD')} 至 过去${
          tagItem.subEnd
        }日 ${
          tagItem.dateCancelType?.length
            ? ' | ' + generateExclusionString(tagItem.dateCancelType)
            : ''
        }`;
      } else {
        timesText = `过去${tagItem.subStart}日 至 过去${tagItem.subEnd}日 ${
          tagItem.dateCancelType?.length
            ? ' | ' + generateExclusionString(tagItem.dateCancelType)
            : ''
        }`;
      }
    }
    const {
      valueType,
      dateCancelType,
      startDate,
      endDate,
      subStart,
      subEnd,
      dyStartDateType,
    } = tagItem;
    const itemInfo = {
      valueType, // 时间类型
      dateCancelType, // 剔除时间
      startDate, // 时间戳
      endDate, // 时间戳
      dyStartDateType,
      subStart,
      subEnd,
      timesText,
    };
    return itemInfo;
  };
  useEffect(() => {
    // 编辑场景下弹窗值回填
    if (isEdit && isModalOpen) {
      if (tagItem.showType === 1) {
        const timeInfo = getTimesText(tagItem);
        form.setFieldValue('timesValue', timeInfo);
      } else if ([2, 10].includes(tagItem.showType)) {
        form.setFieldValue('startValue', tagItem.startValue);
      } else if (tagItem.showType === 3) {
        if (tagItem.scope === 1) {
          setInterval(true);
          form.setFields([
            {
              name: 'scope',
              value: tagItem.scope,
            },
            {
              name: 'numberStartValue',
              value: tagItem.startValue,
            },
            {
              name: 'numberEndValue',
              value: tagItem.endValue,
            },
            {
              name: 'calType',
              value: tagItem.calType,
            },
          ]);
        } else {
          form.setFields([
            {
              name: 'scope',
              value: tagItem.scope,
            },
            {
              name: 'numberEndValue',
              value: tagItem.startValue,
            },
            {
              name: 'calType',
              value: tagItem.calType,
            },
          ]);
        }
        const timeInfo = getTimesText(tagItem);
        form.setFieldValue('timesValue', timeInfo);
      } else if (tagItem.showType === 5) {
        form.setFields([
          {
            name: 'scope',
            value: tagItem.scope,
          },
          {
            name: 'startValueText',
            value: tagItem.startValue,
          },
        ]);
      } else if (tagItem.showType === 9) {
        form.setFields([
          {
            name: 'scope',
            value: tagItem.scope,
          },
          {
            name: 'startValueList',
            value: tagItem.startValue,
          },
        ]);
      } else if (tagItem.showType === 13) {
        const combineLabelFn = window.$BLMReleaseCenter.getSceneSwitch(
          'crowdValidity',
        )
          ? queryCombineLabelDataNew
          : queryCombineLabelData;
        combineLabelFn({ labelId: tagItem.id })
          .then((res) => {
            if (res && res.data) {
              setTreeDataSource(res.data);
            }
          })
          .catch((e) => {
            console.log('请求失败', e);
          });
        // 车系id与父级id组合成唯一id
        const targetKeysList = [];
        // select输入框的值
        JSON.parse(tagItem.startValue).forEach((item) => {
          const secondLevelItem = item[item.length - 1] || {};
          const firstLevelItem = item[0] || {};
          // 复制 选中车系id集合
          const valueArray = String(secondLevelItem?.value)
            ?.split(',')
            ?.map((value) => value.trim());
          // 复制 选中车系父子id组合集合
          let targetKeysArray = [];
          if (tagItem.subTagLevel === 1) {
            targetKeysArray = valueArray.map((key) => `0_${key}`);
          } else {
            targetKeysArray = valueArray.map(
              (key) => `${firstLevelItem?.value}_${key}`,
            );
          }
          targetKeysList.push(...targetKeysArray);
        });
        setEditCasderData(targetKeysList);
      } else if (tagItem.showType === 11) {
        const timeInfo = getTimesText(tagItem);
        form.setFields([
          {
            name: 'scope',
            value: tagItem.scope,
          },
          {
            name: 'startValueEnum',
            value: tagItem.startValue,
          },
          {
            name: 'calType',
            value: tagItem.calType,
          },
          {
            name: 'timesValue',
            value: timeInfo,
          },
        ]);
      } else if (tagItem.showType === 4) {
        if (tagItem.scope === 1) {
          setInterval(true);
          form.setFields([
            {
              name: 'scope',
              value: tagItem.scope,
            },
            {
              name: 'numberStartValue',
              value: tagItem.startValue,
            },
            {
              name: 'numberEndValue',
              value: tagItem.endValue,
            },
          ]);
        } else {
          form.setFields([
            {
              name: 'scope',
              value: tagItem.scope,
            },
            {
              name: 'numberEndValue',
              value: tagItem.startValue,
            },
          ]);
        }
      }
    }
  }, [isModalOpen.openStatus]);
  return (
    <Modal
      title={tagItem?.levelName}
      width={[2, 10].includes(tagItem.showType) ? 560 : 720}
      open={isModalOpen.openStatus}
      onOk={(e) => handleTagFormOk(e)}
      onCancel={() => setModalOpen(false)}
      maskClosable={false}
    >
      <Form
        key={tagItem}
        form={form}
        colon={false}
        id="kf-form"
        layout="inline"
        initialValues={{}}
        style={{ display: 'flex', justifyContent: 'center' }}
      >
        {tagTypeEnum[tagItem.showType]?.map((item, index) =>
          handleLabelControls(item, index),
        )}
      </Form>
    </Modal>
  );
};

export default LabelBaseControls;
