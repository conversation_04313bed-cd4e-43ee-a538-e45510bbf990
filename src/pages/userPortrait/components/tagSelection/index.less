.tagSelection {
  &-option {
    padding: 0 16px 16px;

    &_common {
      display: flex;
      margin-bottom: 16px;
    }
  }
}

.tagCateGoryTitle {
  min-width: 70px;
  // margin: auto 0;
  margin-top: 12px;
  margin-right: 8px;
}

.tagOption {
  display: flex;
  align-items: center;
  margin-right: 16px;
  padding: 4px 16px;
  border-radius: 4px;
  background: #f7f7f9;
  margin-top: 8px;
  color: rgba(0, 0, 0, 0.9);
  border: 1px solid transparent;
  cursor: pointer;

  &:hover {
    background: #eeeef0;
  }

  &_selected {
    background: #ffffff;
    color: #366cfe;
    border: 1px solid #366cfe;

    &:hover {
      color: #598bfe;
      border: 1px solid #598bfe;
      background: #ffffff;
    }
  }

  &-icon {
    width: 16px;
    height: 16px;
    line-height: 10px;
    padding: 2px;
    background: #366cfe;
    border-radius: 2px;
    margin-right: 4px;
  }
}

// .tagOptionSelected {
//   display: flex;
//   align-items: center;
//   margin-right: 16px;
//   padding: 8px 16px;
//   border-radius: 4px;
//   background: #ffffff;
//   color: #366cfe;
//   border: 1px solid #366cfe;
//   cursor: pointer;
//   &-icon {
//     width: 16px;
//     height: 16px;
//     line-height: 10px;
//     padding: 2px;
//     background: #366cfe;
//     border-radius: 2px;
//     margin-right: 4px;
//   }
// }
.tagEnum {
  display: flex;
  margin-top: 8px;

  &-item {
    display: flex;
    flex-wrap: wrap;
  }
}

.tagSelectinTable {
  padding: 0 8px;
  :global {
    .ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container {
      border-inline-start: 0px;
    }
    .ant-table-wrapper table {
      border-inline-start: 1px solid #eeeef0;
    }
  }
}
