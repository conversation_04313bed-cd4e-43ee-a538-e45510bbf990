import { blmAnalysisModuleClick } from '@/utils/eventTracking';
import request from '@/utils/request';
import type { TemplatePageSchema } from '@blmcp/peento-businessComponents';
import { BLMTemplatePage } from '@blmcp/peento-businessComponents';
import { BLMIconFont, Divider } from '@blmcp/ui';
import { useEffect, useState } from 'react';
import { querySubjectTree, querySubjectTreeNew } from '../../api';
import { getSchemaTagSearch } from '../tagSelection/components/schemaTagSearch';
import TagSelectionTable from './components/tagSelectionTable';
import styles from './index.less';

const TagOptionItem = ({
  tagName,
  tagId,
  iconType = '',
  isSelected,
  handleChange,
  isShowIcon = false,
}) => {
  return (
    <div
      className={`${styles['tagOption']} ${
        isSelected ? styles['tagOption_selected'] : ''
      }`}
      onClick={() => handleChange(tagId)}
    >
      {isShowIcon && (
        <div className={styles['tagOption-icon']}>
          <BLMIconFont
            style={{ color: '#fff', fontSize: 12 }}
            type={iconType}
          />
        </div>
      )}
      {tagName}
    </div>
  );
};
const TagSelection = ({
  showSearchTable,
  tagInputValue,
  formInstance,
  operationPoolChange,
  setOperationPoolChange,
  tagSearchTableRef,
}) => {
  const tagOptionList = [
    {
      id: 'commonTag',
      name: '建议人群',
      iconType: 'BLM-ic-star',
    },
    {
      id: 'hotTag',
      name: '热门标签',
      iconType: 'BLM-ywIc-hotTag',
    },
    {
      id: 'newTag',
      name: '新上标签',
      iconType: 'BLM-ywIc-newText',
    },
  ];
  const [selectedTagItem, setSelectedTagItem] = useState({
    tagId: 'commonTag',
    tagName: '建议人群',
  });
  // 当前标签层级
  const [tagOptionTreeList, setTagOptionTreeList] = useState([]);
  // 表格区域高度
  const [tableHeight, setTableHeight] = useState(0);
  useEffect(() => {
    const subjectTreeFn = window.$BLMReleaseCenter.getSceneSwitch(
      'crowdValidity',
    )
      ? querySubjectTreeNew
      : querySubjectTree;
    // 获取当前标签层级列表
    subjectTreeFn({ id: 14 }).then((res) => {
      if (res.data && res.code === 1) {
        const newTagOptionTreeList = res.data?.filter(
          (item) =>
            item?.labelSubjectLevelInfos &&
            item?.labelSubjectLevelInfos?.length > 0,
        );
        setTagOptionTreeList(newTagOptionTreeList);
      }
    });
  }, []);
  useEffect(() => {
    const calculateMaxHeight = () => {
      // 获取某个 div 的高度，可以使用 ref 或者通过选择器获取
      const tagSelectionDiv = document.getElementById('optionContent');
      const tagSelectionNewDiv = tagSelectionDiv
        ? tagSelectionDiv.clientHeight
        : 0;
      // 更新状态
      setTableHeight(tagSelectionNewDiv);
    };
    calculateMaxHeight();
    window.addEventListener('resize', calculateMaxHeight);
    return () => {
      // 清除事件监听器
      window.removeEventListener('resize', calculateMaxHeight);
    };
  }, [tagOptionTreeList]);

  return (
    <div className="tagSelection">
      {/* 搜索条件表格 */}
      {showSearchTable ? (
        <BLMTemplatePage
          // key={selectedTagItem.tagId}
          ref={tagSearchTableRef}
          height="calc(100vh - 124px)"
          request={request}
          pageWrapperClassName={styles['tagSelectinTable']}
          tableComponentProps={{
            bordered: true,
          }}
          schema={
            getSchemaTagSearch(
              formInstance,
              operationPoolChange,
              setOperationPoolChange,
              tagInputValue,
            ) as TemplatePageSchema
          }
        />
      ) : (
        <>
          <div className={styles['tagSelection-option']} id="optionContent">
            <div className={styles['tagSelection-option_common']}>
              <div className={styles['tagCateGoryTitle']}>常用：</div>
              <div style={{ display: 'flex' }}>
                {tagOptionList.map((item, index) => (
                  <TagOptionItem
                    key={index}
                    tagId={item.id}
                    tagName={item.name}
                    iconType={item.iconType}
                    isSelected={selectedTagItem.tagId === item.id}
                    handleChange={() => {
                      blmAnalysisModuleClick({
                        eventId: 'e_leopard_cp_click_00001808',
                        pageId: 'p_leopard_cp_00000402',
                        ext: {
                          str0_e: item.name,
                        },
                      });
                      setSelectedTagItem({
                        tagId: item.id,
                        tagName: item.name,
                      });
                    }}
                    isShowIcon={true}
                  />
                ))}
              </div>
            </div>
            <Divider style={{ margin: 0 }} />
            <div className={styles['tagSelection-option_enum']}>
              {tagOptionTreeList?.map((item, index) => (
                <div className={styles['tagEnum']} key={index}>
                  <div className={styles['tagCateGoryTitle']}>
                    {`${item.name}` + ':'}
                  </div>
                  <div className={styles['tagEnum-item']}>
                    {item?.labelSubjectLevelInfos.map(
                      (itemChildren, indexChildren) => (
                        <TagOptionItem
                          key={indexChildren}
                          tagId={itemChildren.id}
                          tagName={itemChildren.name}
                          isSelected={selectedTagItem.tagId === itemChildren.id}
                          handleChange={() => {
                            blmAnalysisModuleClick({
                              eventId: 'e_leopard_cp_click_00001808',
                              pageId: 'p_leopard_cp_00000402',
                              ext: {
                                str0_e: itemChildren.name,
                              },
                            });
                            setSelectedTagItem({
                              tagId: itemChildren.id,
                              tagName: itemChildren.name,
                            });
                          }}
                        />
                      ),
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className={styles['tagSelection-table']}>
            <TagSelectionTable
              selectedTagItem={selectedTagItem}
              formInstance={formInstance}
              operationPoolChange={operationPoolChange}
              setOperationPoolChange={setOperationPoolChange}
              tableHeight={tableHeight}
            />
          </div>
        </>
      )}
    </div>
  );
};
export default TagSelection;
