import AddDropDown from '../../addDropDown';

export const getSchemaTagCrowd = (
  formInstance,
  operationPoolChange,
  setOperationPoolChange,
) => {
  const schema = {
    // 模版页面配置标识，确保唯一性，建议子应用名称-页面
    templatePageId: 'userPortrait-tagSelectionCrowd',

    /** 表格相关配置 */
    tableConfig: {
      // 请求接口
      url: window.$BLMReleaseCenter.getSceneSwitch('crowdValidity')
        ? '/bos/admin/v1/ai/crowd-tag/recommend/pageQueryRecommendCrowdInfoList'
        : '/admin/v1/weklin/crowdTags/crowd/Recommend/pageQueryRecommendCrowdInfoList',
      // 请求方式
      method: 'post',
      // 表格列配置
      tableColumns: [
        {
          title: '场景',
          dataIndex: 'themeName',
          key: 'themeName',
          width: 172,
          onCell: (record) => {
            if (record.numId === 1) {
              return {
                rowSpan: record.gpNum,
              };
            }
            if (record.numId !== 1) {
              return {
                rowSpan: 0,
              };
            }
          },
          onHeaderCell: () => ({
            style: { paddingLeft: 16 },
          }),
          render: (text) => <div style={{ paddingLeft: 8 }}>{text}</div>,
        },
        {
          title: '人群名称',
          dataIndex: 'name',
          key: 'name',
          width: 190,
        },
        {
          title: '人群特征',
          dataIndex: 'mark',
          key: 'mark',
          width: 390,
          render: (text) => (
            <div>
              {text.split('\n').map((item, index) => (
                <div key={index}>{item}</div>
              ))}
            </div>
          ),
        },
        {
          title: '使用说明',
          dataIndex: 'suggestDesc',
          key: 'suggestDesc',
          width: 272,
          render: (text) => (
            <div>
              {text.split('\n').map((item, index) => (
                <div key={index}>{item}</div>
              ))}
            </div>
          ),
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 82,
          //   render: (_, record) => <Button type="link">添加</Button>,
          // 若当前运算池数量大于1，则选择添加至某个运算池，若当前运算池数量等于1，则直接添加至当前运算池，不展示下拉icon
          render: (_, record) => (
            <AddDropDown
              tagItem={record}
              formInstance={formInstance}
              operationPoolChange={operationPoolChange}
              setOperationPoolChange={setOperationPoolChange}
              crowdType={2}
              isDisabled={false}
            />
          ),
        },
      ],
    },
  };
  return schema;
};
