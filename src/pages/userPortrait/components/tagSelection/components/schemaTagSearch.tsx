import AddDropDown from '../../addDropDown';

export const getSchemaTagSearch = (
  formInstance,
  operationPoolChange,
  setOperationPoolChange,
  tagInputValue,
) => {
  const schema = {
    // 模版页面配置标识，确保唯一性，建议子应用名称-页面
    templatePageId: 'userPortrait-tagSelectionSearch',

    /** 表格相关配置 */
    tableConfig: {
      // 请求接口
      url: window.$BLMReleaseCenter.getSceneSwitch('crowdValidity')
        ? '/bos/admin/v1/ai/crowd-tag/label/getLevelIndexByCategoryId'
        : '/admin/v1/weklin/crowdTags/crowd/querySpecifiedLevelLabel',
      // 请求方式
      method: 'post',
      params: {
        platformId: 11,
        createSource: 1,
        businessType: 1,
        entityType: 1,
        labelName: tagInputValue,
        driverDimension: 2,
      },
      paginationConfig: {
        defaultPageSize: 20,
      },
      // 表格列配置
      tableColumns: [
        {
          title: '标签名称',
          dataIndex: 'levelName',
          key: 'levelName',
          width: 172,
        },
        {
          title: '所属类目',
          dataIndex: 'parentName',
          key: 'parentName',
          width: 170,
          render: (_, row) => (
            <div>
              {row.parentName}/{row.nodeName}
            </div>
          ),
        },
        {
          title: '标签描述',
          dataIndex: 'remarks',
          key: 'remarks',
          width: 490,
          render: (text) => (
            <div>
              {text &&
                text
                  .split('\n')
                  .map((item, index) => <div key={index}>{item}</div>)}
            </div>
          ),
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 82,
          render: (_, record) => (
            <AddDropDown
              tagItem={record}
              formInstance={formInstance}
              operationPoolChange={operationPoolChange}
              setOperationPoolChange={setOperationPoolChange}
              crowdType={3}
              isDisabled={false}
            />
          ),
        },
      ],
    },
  };
  return schema;
};
