// 标签圈选-表格
import React, { useRef } from 'react';
import {
  BLMTemplatePage,
  ITemplatePageRef,
} from '@blmcp/peento-businessComponents';
import { Divider } from '@blmcp/ui';
import request from '@/utils/request';
import { getSchemaTag } from '../schemaTag';
import { getSchemaTagCrowd } from '../schemaTagCrowd';
import NewTagTable from '../newTagTable';
import styles from './index.less';
import type { TemplatePageSchema } from '@blmcp/peento-businessComponents';

const TagSelectionTable: React.FC<any> = ({
  selectedTagItem,
  formInstance,
  operationPoolChange,
  setOperationPoolChange,
  tableHeight,
}) => {
  const tagTableRef = useRef<ITemplatePageRef>(null);
  const tagCrowdTableRef = useRef<ITemplatePageRef>(null);

  return (
    <div>
      <div className={styles['selectedTag']}>{selectedTagItem.tagName}</div>
      <div>
        {/* 人群包推荐table表格 */}
        {selectedTagItem.tagId === 'commonTag' ? (
          <div className={styles['commonTagTable']}>
            <BLMTemplatePage
              key={selectedTagItem.tagId}
              ref={tagTableRef}
              height={`calc(100vh - 162px - ${tableHeight}px)`}
              request={request}
              tableComponentProps={{
                bordered: true,
              }}
              schema={
                getSchemaTagCrowd(
                  formInstance,
                  operationPoolChange,
                  setOperationPoolChange,
                ) as TemplatePageSchema
              }
              onBeforeFetch={(params) => {
                return {
                  ...params,
                  platformId: 1,
                  businessType: 1,
                  entityType: 1,
                };
              }}
            />
          </div>
        ) : ['hotTag', 'newTag'].includes(selectedTagItem.tagId) ? (
          <NewTagTable
            key={selectedTagItem.tagId}
            formInstance={formInstance}
            operationPoolChange={operationPoolChange}
            setOperationPoolChange={setOperationPoolChange}
            selectedTagItem={selectedTagItem}
            tagType={selectedTagItem.tagId}
            tableHeight={tableHeight}
          />
        ) : (
          // 标签表格
          <BLMTemplatePage
            key={selectedTagItem.tagId}
            ref={tagCrowdTableRef}
            height={`calc(100vh - 162px - ${tableHeight}px)`}
            request={request}
            pageWrapperClassName={styles['tagTableSelection']}
            tableComponentProps={{
              bordered: true,
            }}
            schema={
              getSchemaTag(
                formInstance,
                operationPoolChange,
                setOperationPoolChange,
                selectedTagItem,
              ) as TemplatePageSchema
            }
            onBeforeFetch={(params) => {
              const extParams = ['hotTag', 'newTag'].includes(
                selectedTagItem.tagId,
              )
                ? { type: selectedTagItem.tagId === 'hotTag' ? 1 : 2 }
                : { nodeId: selectedTagItem.tagId };
              return {
                ...params,
                ...extParams,
                platformId: 11,
                createSource: 1,
                businessType: 1,
                entityType: 1,
                driverDimension: 2,
              };
            }}
          />
        )}
      </div>
    </div>
  );
};
export default TagSelectionTable;
