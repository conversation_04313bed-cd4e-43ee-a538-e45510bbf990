import { Table } from '@blmcp/ui';
import { useEffect, useState } from 'react';
import {
  getLabelStatisticalList,
  getLabelStatisticalListNew,
} from '../../../api/index';
import AddDropDown from '../../addDropDown';

const NewTagTable = ({
  formInstance,
  operationPoolChange,
  setOperationPoolChange,
  selectedTagItem,
  tagType,
  tableHeight,
}) => {
  // 表格数据
  const [dataSource, setDataSource] = useState([]);
  const columns = [
    {
      title: '标签名称',
      dataIndex: 'levelName',
      key: 'levelName',
      width: 172,
    },
    {
      title: '标签描述',
      dataIndex: 'remarks',
      key: 'remarks',
      width: 670,
      render: (text) => (
        <div>
          {text &&
            text
              .split('\n')
              .map((item, index) => <div key={index}>{item}</div>)}
        </div>
      ),
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 82,
      render: (_, record) => (
        <AddDropDown
          tagItem={record}
          formInstance={formInstance}
          operationPoolChange={operationPoolChange}
          setOperationPoolChange={setOperationPoolChange}
          crowdType={3}
          isDisabled={false}
        />
      ),
    },
  ];

  useEffect(() => {
    const labelStatisticalFn = window?.$BLMReleaseCenter.getSceneSwitch(
      'crowdValidity',
    )
      ? getLabelStatisticalListNew
      : getLabelStatisticalList;
    labelStatisticalFn({
      businessType: 1,
      entityType: 1,
      platformId: 1,
      type: tagType === 'hotTag' ? 1 : 2,
    }).then((res) => {
      if (res.code === 1 && res.data) {
        setDataSource(res.data);
      }
    });
  }, []);

  return (
    <div
      style={{
        height: `calc(100vh - 178px - ${tableHeight}px)`,
        overflow: 'auto',
        padding: '0',
        margin: '16px  16px 0 16px',
      }}
    >
      <Table
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        bordered={true}
      />
    </div>
  );
};
export default NewTagTable;
