import AddDropDown from '../../addDropDown';

// 标签
export const getSchemaTag = (
  formInstance,
  operationPoolChange,
  setOperationPoolChange,
  selectedTagItem,
) => {
  const schema = {
    // 模版页面配置标识，确保唯一性，建议子应用名称-页面
    templatePageId: 'userPortrait-tagSelection',
    /** 表格相关配置 */
    tableConfig: {
      // 请求接口
      url: ['hotTag', 'newTag'].includes(selectedTagItem.tagId)
        ? window.$BLMReleaseCenter.getSceneSwitch('crowdValidity')
          ? '/bos/admin/v1/ai/crowd-tag/label/getLabelStatisticalList'
          : '/admin/v1/weklin/crowdTags/crowd/getLabelStatisticalList'
        : window.$BLMReleaseCenter.getSceneSwitch('crowdValidity')
        ? '/bos/admin/v1/ai/crowd-tag/label/getLevelIndexByCategoryId'
        : '/admin/v1/weklin/crowdTags/crowd/querySpecifiedLevelLabel',
      // 请求方式
      method: 'post',
      params: {
        // libraryId: params.libraryId,
        // batchNum: params.batchNum,
      },
      // 表格列配置
      tableColumns: [
        {
          title: '标签名称',
          dataIndex: 'levelName',
          key: 'levelName',
          width: 172,
        },
        {
          title: '标签描述',
          dataIndex: 'remarks',
          key: 'remarks',
          width: 670,
          render: (text) => (
            <div>
              {text &&
                text
                  .split('\n')
                  .map((item, index) => <div key={index}>{item}</div>)}
            </div>
          ),
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 82,
          render: (_, record) => (
            <AddDropDown
              tagItem={record}
              formInstance={formInstance}
              operationPoolChange={operationPoolChange}
              setOperationPoolChange={setOperationPoolChange}
              crowdType={3}
              isDisabled={false}
            />
          ),
        },
      ],
    },
  };
  return schema;
};
