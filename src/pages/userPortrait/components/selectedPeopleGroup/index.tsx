// 已圈选人群tab
import React from 'react';
import { BLMTemplatePage } from '@blmcp/peento-businessComponents';
import request from '@/utils/request';
import type { TemplatePageSchema } from '@blmcp/peento-businessComponents';
import { getSchema } from './schema';
import styles from './index.less';

const SelectedPeopleGroup = ({
  formInstance,
  operationPoolChange,
  setOperationPoolChange,
  crowdTableRef,
  tagInputValue,
}) => {
  return (
    <div className={styles['leftGroupTable']}>
      <div className={styles['leftGroupTable_list']}>
        <BLMTemplatePage
          height={'calc(100vh - 124px)'}
          ref={crowdTableRef}
          request={request}
          tableComponentProps={{
            bordered: true,
          }}
          schema={
            getSchema(
              formInstance,
              operationPoolChange,
              setOperationPoolChange,
              tagInputValue,
            ) as TemplatePageSchema
          }
        />
      </div>
    </div>
  );
};
export default SelectedPeopleGroup;
