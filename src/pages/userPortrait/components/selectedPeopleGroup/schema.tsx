import AddDropDown from '../addDropDown';

export const getSchema = (
  formInstance,
  operationPoolChange,
  setOperationPoolChange,
  tagInputValue
) => {
  const schema = {
    // 模版页面配置标识，确保唯一性，建议子应用名称-页面
    templatePageId: 'userPortrait-selectedTag',

    /** 表格相关配置 */
    tableConfig: {
      // 请求接口
      url: '/admin/v1/weklin/crowdTags/crowd/pageQueryCrowdList',
      // 请求方式
      method: 'post',
      params: {
        platformId: 1,
        businessType: 1,
        entityType: 1,
        fuzzyQueryByIdOrName: tagInputValue,
      },
      paginationConfig: {
        defaultPageSize: 20,
      },
      // 表格列配置
      tableColumns: [
        {
          title: '人群包ID',
          dataIndex: 'id',
          key: 'id',
          width: 72,
        },
        {
          title: '人群包名称',
          dataIndex: 'crowdTagName',
          key: 'crowdTagName',
          width: 272,
        },
        {
          title: '样本量',
          dataIndex: 'driverCnt',
          key: 'driverCnt',
          width: 96,
        },
        {
          title: '创建时间',
          key: 'gmtCreate',
          dataIndex: 'gmtCreate',
          width: 228,
        },
        {
          title: '操作',
          key: 'action',
          fixed: 'right',
          width: 82,
          render: (_, record) => (
            <AddDropDown
              tagItem={record}
              formInstance={formInstance}
              operationPoolChange={operationPoolChange}
              setOperationPoolChange={setOperationPoolChange}
              crowdType={1}
              isDisabled={_.isDepend === 1}
            />
          ),
        },
      ],
    },
  };
  return schema;
};
