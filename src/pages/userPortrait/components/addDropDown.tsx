import React, { useState, useEffect, useMemo } from 'react';
import { Button, BLMIconFont, Dropdown, Modal, Tooltip } from '@blmcp/ui';
import { blmAnalysisModuleClick } from '@/utils/eventTracking';
import LabelBaseControls from './labelBaseControls';

const AddDropDown = ({
  tagItem,
  formInstance,
  operationPoolChange,
  setOperationPoolChange,
  crowdType, // 当前是选中tag类型
  isDisabled, // 添加按钮是否置灰
}) => {
  // 添加按钮下拉icon状态
  const [dropDownOpenStatus, setDropDownOpenStatus] = useState(false);
  // 运算池下拉框数据源
  const [operationInfoList, setOperationInfoList] = useState([]);
  // 标签弹窗状态
  const [isModalOpen, setModalOpen] = useState({});

  // 查找对应名称转换
  const handleLabelStartValue = (data, str) => {
    return data?.find((item) => item.value === str).label;
  };
  // 获取埋自定义参数的值
  const getStrType = () => {
    // crowdType:(1)已圈选人群, (2)推荐人群, (3)标签
    // strType:   3:已圈选人群； 2:常用人群;  1:标签
    let strType;
    switch (crowdType) {
      case 1:
        strType = 3;
        break;
      case 2:
        strType = 2;
        break;
      case 3:
        strType = 1;
        break;
    }
    return strType;
  };
  // 下拉框选择,选择完触发添加到运算池操作 e：选中运算池key
  const handleOperationPoolClick = (e, tagItemHandled = tagItem) => {
    // 选中的运算池index,该index为operationPoolChange状态中index
    const operationIndex = operationPoolChange.findIndex(
      (item) => item.key === Number(e.key),
    );
    // 往当前item中添加标识用于区分是人群还是标签
    // 推荐人群(2)、圈选人群(1)，直接添加到运算池，若为标签(3)
    let newTagItem = {
      ...tagItemHandled,
      crowdType: crowdType,
      copyErrorTag: false,
      idx: operationPoolChange[operationIndex].tagList?.length, // 保留运算池内被添加过的所有标签，防止idx错乱
      isTagDelete: false, // 该标签是否在运算池内被删除
      formValueStr: [1, 2].includes(crowdType)
        ? `人群名称：${
            crowdType === 2 ? tagItemHandled.name : tagItemHandled.crowdTagName
          }`
        : tagItemHandled.formValueStr,
    };
    if ([1, 2].includes(crowdType)) {
      newTagItem = {
        ...newTagItem,
        tagId: tagItemHandled.id,
        latelType: 3,
        scope: 2,
        name: tagItemHandled.id,
        crowdTagName:
          crowdType === 2 ? tagItemHandled.name : tagItemHandled.crowdTagName,
      };
    }
    // 添加至运算池时由于运算池之间允许重复添加同一个标签，因此运算池内对标签和人群单独维护ID
    const currOperationTagList = [...operationPoolChange];
    // 如果为人群，添加前新增字段
    // 往运算池中添加本次新增的tagItem
    currOperationTagList.splice(operationIndex, 1, {
      ...operationPoolChange[operationIndex],
      tagList: operationPoolChange[operationIndex]?.tagList.concat(newTagItem),
    });
    setOperationPoolChange(currOperationTagList);
    // 添加成功的埋点事件
    blmAnalysisModuleClick({
      eventId: 'e_leopard_cp_click_00001820',
      pageId: 'p_leopard_cp_00000402',
      ext: { str0_e: getStrType() },
    });
  };
  // 弹窗确认函数，确认完成-添加至运算池
  const handleClickOk = (e, formValue, curTagItem) => {
    const commonParams = {
      ...curTagItem,
      name: curTagItem.fieldValue,
      latelType: curTagItem.latelType,
      datasourceId: curTagItem.dataSourceId,
      showType: curTagItem.showType,
      tagId: curTagItem.id,
      tagNameItem: curTagItem.levelName,
    };
    let extraParams = {};
    if (curTagItem.showType === 1) {
      const { timesValue } = formValue;
      if (timesValue?.valueType === 2) {
        const { startDate, endDate, dateCancelType, timesText } = timesValue;
        extraParams = {
          valueType: 2,
          startDate,
          endDate,
          dateCancelType,
          dyStartDateType: 2,
          scope: 1,
          calType: 2,
          formValueStr: `${curTagItem.levelName} ${timesText}`,
        };
      } else if (timesValue?.valueType === 3) {
        const {
          startDate,
          subStart,
          subEnd,
          dateCancelType,
          timesText,
          dyStartDateType,
        } = timesValue;
        extraParams = {
          valueType: 3,
          startDate,
          subStart,
          subEnd,
          dateCancelType,
          dyStartDateType,
          scope: 1,
          calType: 2,
          formValueStr: `${curTagItem.levelName} ${timesText}`,
        };
      }
    } else if ([2, 10].includes(curTagItem.showType)) {
      extraParams = {
        startValue: formValue.startValue,
        scope: 2,
        formValueStr: `${curTagItem.levelName} 等于 ${handleLabelStartValue(
          curTagItem.enumValueParams,
          formValue.startValue,
        )}`,
      };
    } else if (curTagItem.showType === 3) {
      let timeParams = {};
      let numberParams = {};
      const { timesValue } = formValue;
      if (timesValue?.valueType === 2) {
        const { startDate, endDate, dateCancelType } = timesValue;
        timeParams = {
          valueType: 2,
          startDate,
          endDate,
          dateCancelType,
          dyStartDateType: 2,
        };
      } else if (timesValue?.valueType === 3) {
        const { startDate, subStart, subEnd, dateCancelType, dyStartDateType } =
          timesValue;
        timeParams = {
          valueType: 3,
          startDate,
          subStart,
          subEnd,
          dateCancelType,
          dyStartDateType,
        };
      }
      if (formValue.scope === 1) {
        numberParams = {
          startValue: formValue.numberStartValue,
          endValue: formValue.numberEndValue,
          formValueStr: `${curTagItem.levelName} ${
            timesValue.timesText
          } ${handleLabelStartValue(
            curTagItem.fomulaRelationList,
            formValue.calType,
          )} ${handleLabelStartValue(
            curTagItem.calRelationList,
            formValue.scope,
          )} 在${formValue.numberStartValue}${curTagItem.unit}~${
            formValue.numberEndValue
          }${curTagItem.unit}之间`,
        };
      } else {
        numberParams = {
          startValue: formValue.numberEndValue,
          formValueStr: `${curTagItem.levelName} ${
            timesValue.timesText
          } ${handleLabelStartValue(
            curTagItem.fomulaRelationList,
            formValue.calType,
          )} ${handleLabelStartValue(
            curTagItem.calRelationList,
            formValue.scope,
          )} ${formValue.numberEndValue} ${curTagItem.unit}`,
        };
      }
      extraParams = {
        ...timeParams,
        ...numberParams,
        scope: formValue.scope,
        calType: formValue.calType,
      };
    } else if (curTagItem.showType === 5) {
      extraParams = {
        scope: formValue.scope,
        startValue: formValue.startValueText,
        formValueStr: `${curTagItem.levelName} ${handleLabelStartValue(
          curTagItem.calRelationList,
          formValue.scope,
        )} ${formValue.startValueText}`,
      };
    } else if (curTagItem.showType === 4) {
      let numberParams = {};
      if (formValue.scope === 1) {
        numberParams = {
          startValue: formValue.numberStartValue,
          endValue: formValue.numberEndValue,
          formValueStr: `${curTagItem.levelName} ${handleLabelStartValue(
            curTagItem.calRelationList,
            formValue.scope,
          )} 在${formValue.numberStartValue}${curTagItem.unit}~${
            formValue.numberEndValue
          }${curTagItem.unit}之间`,
        };
      } else {
        numberParams = {
          startValue: formValue.numberEndValue,
          formValueStr: `${curTagItem.levelName} ${handleLabelStartValue(
            curTagItem.calRelationList,
            formValue.scope,
          )} ${formValue.numberEndValue} ${curTagItem.unit}`,
        };
      }
      extraParams = {
        ...numberParams,
        scope: formValue.scope,
      };
    } else if (curTagItem.showType === 9) {
      let replaceValueTxt = formValue.startValueList?.replace(/,/g, '/');
      extraParams = {
        startValue: formValue.startValueList,
        scope: formValue.scope,
        formValueStr: `${
          curTagItem.levelName
        } 等于 ${replaceValueTxt} ${handleLabelStartValue(
          curTagItem.calRelationList,
          formValue.scope,
        )}`,
      };
    } else if (curTagItem.showType === 13) {
      const formValueStrTree = formValue.startValueTreeSelect
        .map((item) => item[item?.length - 1]?.valueName)
        .join(',');
      extraParams = {
        scope: 8,
        calType: formValue.calType,
        startValue: JSON.stringify(formValue.startValueTreeSelect),
        formValueStr: `${curTagItem.levelName} 包含 ${formValueStrTree}`,
      };
    } else if (curTagItem.showType === 11) {
      let timeParams = {};
      const { timesValue } = formValue;
      if (timesValue?.valueType === 2) {
        const { startDate, endDate, dateCancelType } = timesValue;
        timeParams = {
          valueType: 2,
          startDate,
          endDate,
          dateCancelType,
          dyStartDateType: 2,
        };
      } else if (timesValue?.valueType === 3) {
        const { startDate, subStart, subEnd, dateCancelType, dyStartDateType } =
          timesValue;
        timeParams = {
          valueType: 3,
          startDate,
          subStart,
          subEnd,
          dateCancelType,
          dyStartDateType,
        };
      }
      extraParams = {
        ...timeParams,
        scope: formValue.scope,
        startValue: formValue.startValueEnum,
        calType: formValue.calType,
        formValueStr: `${curTagItem.levelName} ${
          timesValue.timesText
        } ${handleLabelStartValue(
          curTagItem.fomulaRelationList,
          formValue.calType,
        )} ${handleLabelStartValue(
          curTagItem.calRelationList,
          formValue.scope,
        )} ${handleLabelStartValue(
          curTagItem.enumValueParams,
          formValue.startValueEnum,
        )}`,
      };
    }

    handleOperationPoolClick(e, { ...commonParams, ...extraParams });
  };
  // 下拉添加函数(处理推荐人群、常用人群、标签)
  const handleCrowdOrTagAdd = (e) => {
    blmAnalysisModuleClick({
      eventId: 'e_leopard_cp_click_00001810',
      pageId: 'p_leopard_cp_00000402',
      ext: { str0_e: getStrType() },
    });
    // 若为推荐人群(2)、圈选人群(1)，直接添加到运算池，若为标签(3)，打开弹窗进行操作编辑
    if (crowdType === 3) {
      setModalOpen({ openStatus: true, key: e });
    } else {
      handleOperationPoolClick(e);
    }
  };

  useEffect(() => {
    // 过滤掉已删除的运算池
    const operationPoolItemsMenu = operationPoolChange
      .filter((item) => item.isDeleted === false)
      .map((item, key) => {
        return { label: `标签组合池${key + 1}`, key: item.key };
      });
    // 用于处理当前下拉框运算池数据
    setOperationInfoList(operationPoolItemsMenu);
  }, [operationPoolChange]);
  // 计算总标签添加总数量
  const handleTagItemLength = useMemo(() => {
    let tagItemLength = 0;
    operationPoolChange.map((item) => {
      if (item.isDeleted === false) {
        item.tagList.map((itemChildren) => {
          if (itemChildren.isTagDelete === false) {
            tagItemLength = tagItemLength + 1;
          }
        });
      }
    });
    return tagItemLength;
  }, [operationPoolChange]);
  // 计算人群包添加总数量
  const handleCrowdItemLength = useMemo(() => {
    let crowdItemLength = 0;
    operationPoolChange.map((item) => {
      if (item.isDeleted === false) {
        item.tagList.map((itemChildren) => {
          if (
            itemChildren.isTagDelete === false &&
            [1, 2].includes(itemChildren.crowdType)
          ) {
            crowdItemLength = crowdItemLength + 1;
          }
        });
      }
    });
    return crowdItemLength;
  }, [operationPoolChange]);

  // 按钮禁止添加文案
  const disabledAddBtnText = () => {
    if (isDisabled) {
      return '该人群包嵌套层数超过3层，暂不支持添加到标签组合池';
    } else if (handleTagItemLength >= 21) {
      return '运算池内容超过21个，暂不支持继续添加';
    } else if (handleCrowdItemLength >= 5 && [1, 2].includes(crowdType)) {
      return '标签组合池内的人群总数超过5个，无法继续添加';
    } else {
      return '';
    }
  };

  return (
    <>
      {operationPoolChange.filter((item) => item.isDeleted === false)?.length >
      1 ? (
        <Dropdown
          menu={{
            items: operationInfoList,
            onClick: (e) => handleCrowdOrTagAdd(e),
          }}
          trigger={['click']}
          onOpenChange={(open) => setDropDownOpenStatus(open)}
        >
          <Tooltip title={disabledAddBtnText()}>
            <Button
              type="link"
              notspacelinkbtn
              disabled={
                isDisabled ||
                handleTagItemLength >= 21 ||
                ([1, 2].includes(crowdType) && handleCrowdItemLength >= 5)
              }
            >
              添加
              <BLMIconFont
                type={
                  dropDownOpenStatus ? 'BLM-ic-arrUp-o' : 'BLM-ic-arrDown-o'
                }
              />
            </Button>
          </Tooltip>
        </Dropdown>
      ) : (
        <Tooltip title={disabledAddBtnText()}>
          <Button
            type="link"
            notspacelinkbtn
            disabled={
              isDisabled ||
              handleTagItemLength >= 21 ||
              ([1, 2].includes(crowdType) && handleCrowdItemLength >= 5)
            }
            onClick={() =>
              // 当前未被删除的运算池的key
              handleCrowdOrTagAdd({
                key: operationPoolChange.find(
                  (item) => item.isDeleted === false,
                ).key,
              })
            }
          >
            添加
          </Button>
        </Tooltip>
      )}
      {crowdType === 3 && (
        <LabelBaseControls
          key={tagItem}
          tagItem={tagItem}
          handleClickOk={handleClickOk}
          isModalOpen={isModalOpen}
          setModalOpen={setModalOpen}
          operationPoolChange={operationPoolChange}
          setOperationPoolChange={setOperationPoolChange}
          isEdit={false}
        />
      )}
    </>
  );
};

export default AddDropDown;
