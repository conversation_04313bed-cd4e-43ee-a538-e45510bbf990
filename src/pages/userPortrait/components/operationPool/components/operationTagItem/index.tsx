import React, { useState } from 'react';
import { BLMIconFont, Tooltip } from '@blmcp/ui';
import LabelBaseControls from '../../../labelBaseControls';
import styles from './index.less';

const OperationTagItem = ({
  tagItem,
  handleOperationPoolClick,
  operationPoolChange,
  setOperationPoolChange,
  operationFieldKey,
  field,
}) => {
  // 标签弹窗编辑打开
  const [tagModalEditOpen, setTagModalEditOpen] = useState({});
  // 查找对应名称转换
  const handleLabelStartValue = (data, str) => {
    return data?.find((item) => item.value === str).label;
  };

  // 编辑完成后修改当前运算池对应标签标签信息
  const handleOperationPoolClickEdit = (e, tagItemHandled = tagItem) => {
    // 选中的运算池index,该index为operationPoolChange状态中index
    const operationIndex = operationPoolChange.findIndex(
      (item) => item.key === Number(e.key),
    );
    // 往当前item中添加标识用于区分是人群还是标签
    // 推荐人群(2)、圈选人群(1)，直接添加到运算池，若为标签(3)
    // 编辑场景idx自动保留之前
    let newTagItem = {
      ...tagItemHandled,
    };
    // 添加至运算池时由于运算池之间允许重复添加同一个标签，因此运算池内对标签和人群单独维护ID
    const currOperationTagList = [...operationPoolChange];
    const curTagList = [...operationPoolChange[operationIndex]?.tagList];
    curTagList.splice(tagItemHandled.idx, 1, newTagItem);
    // 如果为人群，添加前新增字段
    // 往运算池中添加本次新增的tagItem
    currOperationTagList.splice(operationIndex, 1, {
      ...operationPoolChange[operationIndex],
      tagList: curTagList,
    });
    setOperationPoolChange(currOperationTagList);
  };
  // 弹窗编辑确认函数
  const handleClickEdit = (e, formValue, curTagItem) => {
    const commonParams = {
      ...curTagItem,
      name: curTagItem.fieldValue,
      latelType: curTagItem.latelType,
      datasourceId: curTagItem.dataSourceId,
      showType: curTagItem.showType,
      tagId: curTagItem.id,
      tagNameItem: curTagItem.levelName,
    };
    let extraParams = {};
    if (curTagItem.showType === 1) {
      const { timesValue } = formValue;
      if (timesValue?.valueType === 2) {
        const { startDate, endDate, dateCancelType, timesText } = timesValue;
        extraParams = {
          valueType: 2,
          startDate,
          endDate,
          dateCancelType,
          dyStartDateType: 2,
          scope: 1,
          calType: 2,
          formValueStr: `${curTagItem.levelName} ${timesText}`,
        };
      } else if (timesValue?.valueType === 3) {
        const {
          startDate,
          subStart,
          subEnd,
          dateCancelType,
          timesText,
          dyStartDateType,
        } = timesValue;
        extraParams = {
          valueType: 3,
          subStart,
          startDate,
          subEnd,
          dateCancelType,
          dyStartDateType,
          scope: 1,
          calType: 2,
          formValueStr: `${curTagItem.levelName} ${timesText}`,
        };
      }
    } else if ([2, 10].includes(curTagItem.showType)) {
      extraParams = {
        startValue: formValue.startValue,
        scope: 2,
        formValueStr: `${curTagItem.levelName} 等于 ${handleLabelStartValue(
          curTagItem.enumValueParams,
          formValue.startValue,
        )}`,
      };
    } else if (curTagItem.showType === 3) {
      let timeParams = {};
      let numberParams = {};
      const { timesValue } = formValue;
      if (timesValue?.valueType === 2) {
        const { startDate, endDate, dateCancelType } = timesValue;
        timeParams = {
          valueType: 2,
          startDate,
          endDate,
          dateCancelType,
          dyStartDateType: 2,
        };
      } else if (timesValue?.valueType === 3) {
        const { startDate, subStart, subEnd, dateCancelType, dyStartDateType } =
          timesValue;
        timeParams = {
          valueType: 3,
          startDate,
          subStart,
          subEnd,
          dateCancelType,
          dyStartDateType,
        };
      }
      if (formValue.scope === 1) {
        numberParams = {
          startValue: formValue.numberStartValue,
          endValue: formValue.numberEndValue,
          formValueStr: `${curTagItem.levelName} ${
            timesValue.timesText
          } ${handleLabelStartValue(
            curTagItem.fomulaRelationList,
            formValue.calType,
          )} ${handleLabelStartValue(
            curTagItem.calRelationList,
            formValue.scope,
          )} 在${formValue.numberStartValue}${curTagItem.unit}~${
            formValue.numberEndValue
          }${curTagItem.unit}之间`,
        };
      } else {
        numberParams = {
          startValue: formValue.numberEndValue,
          formValueStr: `${curTagItem.levelName} ${
            timesValue.timesText
          } ${handleLabelStartValue(
            curTagItem.fomulaRelationList,
            formValue.calType,
          )} ${handleLabelStartValue(
            curTagItem.calRelationList,
            formValue.scope,
          )} ${formValue.numberEndValue} ${curTagItem.unit}`,
        };
      }
      extraParams = {
        ...timeParams,
        ...numberParams,
        scope: formValue.scope,
        calType: formValue.calType,
      };
    } else if (curTagItem.showType === 5) {
      extraParams = {
        scope: formValue.scope,
        startValue: formValue.startValueText,
        formValueStr: `${curTagItem.levelName} ${handleLabelStartValue(
          curTagItem.calRelationList,
          formValue.scope,
        )} ${formValue.startValueText}`,
      };
    } else if (curTagItem.showType === 4) {
      let numberParams = {};
      if (formValue.scope === 1) {
        numberParams = {
          startValue: formValue.numberStartValue,
          endValue: formValue.numberEndValue,
          formValueStr: `${curTagItem.levelName} ${handleLabelStartValue(
            curTagItem.calRelationList,
            formValue.scope,
          )} 在${formValue.numberStartValue}${curTagItem.unit}~${
            formValue.numberEndValue
          }${curTagItem.unit}之间`,
        };
      } else {
        numberParams = {
          startValue: formValue.numberEndValue,
          formValueStr: `${curTagItem.levelName} ${handleLabelStartValue(
            curTagItem.calRelationList,
            formValue.scope,
          )} ${formValue.numberEndValue} ${curTagItem.unit}`,
        };
      }
      extraParams = {
        ...numberParams,
        scope: formValue.scope,
      };
    } else if (curTagItem.showType === 9) {
      let replaceValueTxt = formValue.startValueList?.replace(/,/g, '/');
      extraParams = {
        startValue: formValue.startValueList,
        scope: formValue.scope,
        formValueStr: `${
          curTagItem.levelName
        } 等于 ${replaceValueTxt} ${handleLabelStartValue(
          curTagItem.calRelationList,
          formValue.scope,
        )}`,
      };
    } else if (curTagItem.showType === 13) {
      const formValueStrTree = formValue.startValueTreeSelect
        .map((item) => item[item?.length - 1]?.valueName)
        .join(',');
      extraParams = {
        scope: 8,
        calType: formValue.calType,
        startValue: JSON.stringify(formValue.startValueTreeSelect),
        formValueStr: `${curTagItem.levelName} 包含 ${formValueStrTree}`,
      };
    } else if (curTagItem.showType === 11) {
      let timeParams = {};
      const { timesValue } = formValue;
      if (timesValue?.valueType === 2) {
        const { startDate, endDate, dateCancelType } = timesValue;
        timeParams = {
          valueType: 2,
          startDate,
          endDate,
          dateCancelType,
          dyStartDateType: 2,
        };
      } else if (timesValue?.valueType === 3) {
        const { startDate, subStart, subEnd, dateCancelType, dyStartDateType } =
          timesValue;
        timeParams = {
          valueType: 3,
          startDate,
          subStart,
          subEnd,
          dateCancelType,
          dyStartDateType,
        };
      }
      extraParams = {
        ...timeParams,
        scope: formValue.scope,
        startValue: formValue.startValueEnum,
        calType: formValue.calType,
        formValueStr: `${curTagItem.levelName} ${
          timesValue.timesText
        } ${handleLabelStartValue(
          curTagItem.fomulaRelationList,
          formValue.calType,
        )} ${handleLabelStartValue(
          curTagItem.calRelationList,
          formValue.scope,
        )} ${handleLabelStartValue(
          curTagItem.enumValueParams,
          formValue.startValueEnum,
        )}`,
      };
    }
    handleOperationPoolClickEdit(e, { ...commonParams, ...extraParams });
  };

  return (
    <>
      {!tagItem.isTagDelete && (
        <div
          className={styles['operationTagItem-tagItem']}
          style={{ border: tagItem.copyErrorTag ? '1px solid red' : '' }}
        >
          <div className={styles['operationTagItem-tagItem_title']}>
            <div
              className={styles['operationTagItem-tagItem_title-txt']}
              style={{ fontWeight: 500 }}
            >
              {/*推荐人群(2)，圈选人群(1)，直接添加到运算池，若为标签(3) */}
              {tagItem.crowdType !== 3
                ? tagItem.crowdTagName
                : tagItem.tagNameItem}
            </div>
            <div className={styles['operationTagItem-tagItem_title-icon']}>
              <BLMIconFont
                style={{
                  marginLeft: 8,
                  color: '#606266',
                }}
                type="BLM-ic-delete-o"
                onClick={() => handleOperationPoolClick(tagItem)}
              />
              {![1, 2].includes(tagItem.crowdType) && (
                <BLMIconFont
                  style={{
                    marginLeft: 8,
                    color: '#606266',
                  }}
                  type="BLM-ic-edit-o"
                  onClick={() =>
                    setTagModalEditOpen({
                      openStatus: true,
                      key: operationFieldKey,
                    })
                  }
                />
              )}
            </div>
          </div>
          <Tooltip
            title={
              !tagItem.copyErrorTag
                ? tagItem.crowdType === 1
                  ? `样本量：${tagItem.driverCnt}`
                  : tagItem.crowdType === 2
                  ? tagItem.mark
                  : tagItem.formValueStr
                : ''
            }
            overlayInnerStyle={{ maxHeight: '200px', overflow: 'auto' }}
            placement="bottom"
          >
            <div className={styles['operationTagItem-tagItem_text']}>
              {!tagItem.copyErrorTag
                ? tagItem.crowdType === 1
                  ? `样本量：${tagItem.driverCnt}`
                  : tagItem.crowdType === 2
                  ? tagItem.mark
                  : tagItem.formValueStr
                : ''}
            </div>
          </Tooltip>
        </div>
      )}
      <LabelBaseControls
        key={tagItem}
        tagItem={tagItem}
        handleClickOk={handleClickEdit}
        isModalOpen={tagModalEditOpen}
        setModalOpen={setTagModalEditOpen}
        operationPoolChange={operationPoolChange}
        setOperationPoolChange={setOperationPoolChange}
        isEdit={true}
        field={field}
      />
    </>
  );
};
export default OperationTagItem;
