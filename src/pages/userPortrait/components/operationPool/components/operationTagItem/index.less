.operationTagItem-tagItem {
  flex-basis: calc(50% - 16px);
  min-width: 148px;
  height: 62px;
  background-color: #ffffff;
  border-radius: 6px;
  padding: 8px;
  margin-right: 8px;
  white-space: nowrap;

  &:hover {
    box-shadow: 0px 5px 10px 0px rgba(42, 48, 72, 0.1);

    .operationTagItem-tagItem_title-icon {
      display: block;
    }
  }

  &_title {
    display: flex;
    justify-content: space-between;

    &-txt {
      text-overflow: ellipsis;
      overflow: hidden;
    }

    &-icon {
      display: none;
    }
  }

  &_text {
    text-overflow: ellipsis;
    overflow: hidden;
    font-weight: 500;
    color: rgba(0, 0, 0, 0.4);
  }
}

.operationTagItem-tagItem:nth-child(2n) {
  margin-right: 0;
  /* 清除每行中最后一个元素的右边距 */
}

.operationTagItem-tagItem:not(:first-child):not(:nth-child(2)) {
  margin-top: 8px;
  /* 清除第一个和第二个子项的上边距 */
}