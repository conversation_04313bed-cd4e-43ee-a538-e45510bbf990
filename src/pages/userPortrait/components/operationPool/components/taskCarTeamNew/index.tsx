import React, { useEffect, useCallback, useState, useRef } from 'react';
import {
  Button,
  Checkbox,
  Divider,
  Tree,
  Input,
  BLMIconFont,
  Tooltip,
  Select,
  Popover,
  Modal,
  message,
} from '@blmcp/ui';
import { cloneDeep, divide } from 'lodash';
import VirtualList from 'rc-virtual-list';
import styles from './index.less';

const { TextArea } = Input;

interface TaskCarTeamNewProps {
  form: any;
  // 运力公司弹窗状态改变函数
  carTeams: Array<any>;
  // 运力公司中文名称拼接
  setTaskCarTeamIdList: (e) => void;
  taskCarTeamIdSaved: [];
  taskCarTeamIdList: [];
}

const TaskCarTeamNew: React.FC<TaskCarTeamNewProps> = ({
  form,
  carTeams,
  setTaskCarTeamIdList,
  taskCarTeamIdSaved,
  taskCarTeamIdList,
  cityCodeSelect,
}) => {
  const [messageApi, contextHolder] = message.useMessage();

  // 运力公司数据集合
  const [taskCarTeamData, setTaskCarTeamData] = useState(carTeams);
  // 弹窗内选中运力公司item集合
  const [taskCarTeamSelectList, setTaskCarTeamSelectList] = useState([]);
  // 右侧展示运力公司数据集合
  const [taskCarTeamSelectShowList, setTaskCarTeamSelectShowList] = useState(
    [],
  );
  // 弹窗内选中运力公司id集合
  const [taskCarTeamSelectIdList, setTaskCarTeamSelectIdList] = useState([]);
  // 左侧搜索框value值
  const [leftSearchValue, setLeftSearchValue] = useState('');
  // 右侧搜索框value值
  const [rightSearchValue, setRightSearchValue] = useState('');
  // 右侧添加以上全部按钮选中状态
  const [allCheckStatus, setAllCheckStatus] = useState(false);
  // 右侧全部按钮是否为半选状态
  const [isIndeterminateStatus, setIndeterminateStatus] = useState(false);
  // 当前左侧可见（搜索）已选item
  const [searchCheckedItemList, setSearchCheckedItemList] = useState([]);
  // 运力公司组件弹窗打开状态
  const [taskCarTeamPopoverOpen, setTaskCarTeamPopoverOpen] = useState(false);
  const [quickAddOpen, setQuickAddOpen] = useState(false);
  const [inputValue, setInputValue] = useState('');
  // 运力公司添加确认
  const handleAddClick = () => {
    setQuickAddOpen(false);
    setTaskCarTeamPopoverOpen(false);
    const handleSelectedTitleList = taskCarTeamSelectList.map(
      (obj) => obj.dictDesc,
    );
    const handleSelectedIdList = taskCarTeamSelectList.map(
      (obj) => obj.dictValue,
    );
    form.setFieldValue('taskCarTeamId', handleSelectedIdList);
    setTaskCarTeamIdList(handleSelectedTitleList);
  };
  // 左侧区域搜索
  const handleLeftSearch = (e: any) => {
    setLeftSearchValue(e.target.value);
    // 源数据中过滤
    const searchTaskCarTeamData = carTeams.filter((item) =>
      item.dictDesc.includes(e.target.value),
    );
    setTaskCarTeamData(searchTaskCarTeamData);
  };
  // 右侧区域搜索
  const handleRightSearch = (e: any, isChange = true, checkedNodes = []) => {
    if (isChange) {
      setRightSearchValue(e);
    }
    const sourceData = isChange ? taskCarTeamSelectList : checkedNodes;
    // 选中运力公司数据中过滤
    const searchTaskCarTeamData = sourceData.filter((item) =>
      item.dictDesc.includes(e),
    );
    setTaskCarTeamSelectShowList(searchTaskCarTeamData);
  };
  // 运力公司树组件选中
  const handleTaskCarTeamCheck = (checkedKeys, e, type) => {
    let isSelected;
    if (type === 'check') {
      isSelected = e.checked;
    } else if (type === 'select') {
      if (taskCarTeamSelectIdList?.length) {
        isSelected = !taskCarTeamSelectIdList.includes(e.node.dictValue);
      } else {
        isSelected = true;
      }
    }
    let filterTaskIdList = [];
    if (isSelected) {
      filterTaskIdList = taskCarTeamSelectIdList.concat(e.node.dictValue);
    } else {
      filterTaskIdList = taskCarTeamSelectIdList.filter(
        (item) => item !== e.node.dictValue,
      );
    }
    setTaskCarTeamSelectIdList(filterTaskIdList);
    let filterTaskList = [];
    if (isSelected) {
      filterTaskList = taskCarTeamSelectList.concat(e.node);
    } else {
      filterTaskList = taskCarTeamSelectList.filter(
        (item) => item.dictValue !== e.node.dictValue,
      );
    }
    // 选中运力item集合
    setTaskCarTeamSelectList(filterTaskList);
    // 选择之后针对右侧搜索项进行过滤
    handleRightSearch(rightSearchValue, false, filterTaskList);
  };
  // 删除选中的运力公司
  const handelDeleteItem = (deleteItem) => {
    // 删除后剩余的全量数据
    const taskCarTeamSelectListFilter = taskCarTeamSelectList.filter(
      (item) => item.dictValue !== deleteItem.dictValue,
    );
    // 删除后剩余的需要展示数据
    const taskCarTeamSelectListShowFilter = taskCarTeamSelectShowList?.filter(
      (item) => item.dictValue !== deleteItem.dictValue,
    );
    // 删除后运力公司ID集合
    const taskCarTeamSelectListIdFilter = taskCarTeamSelectIdList.filter(
      (item) => item !== deleteItem.dictValue,
    );
    console.log(
      taskCarTeamSelectListFilter,
      'taskCarTeamSelectListFilter',
      taskCarTeamSelectListShowFilter,
      taskCarTeamSelectListIdFilter,
    );
    setTaskCarTeamSelectList(taskCarTeamSelectListFilter);
    setTaskCarTeamSelectShowList(taskCarTeamSelectListShowFilter);
    setTaskCarTeamSelectIdList(taskCarTeamSelectListIdFilter);
  };
  // 右侧添加以上全部选项
  const handleAllCheckClick = (checkStatus) => {
    if (searchCheckedItemList.length) {
      setAllCheckStatus(true);
    } else {
      setAllCheckStatus(false);
    }
    setIndeterminateStatus(false);
    if (
      checkStatus ||
      (searchCheckedItemList.length &&
        searchCheckedItemList.length !== taskCarTeamData.length)
    ) {
      // 当前右侧未被选中的运力公司集合
      const unCheckedTaskList = taskCarTeamData.filter(
        (item) => !taskCarTeamSelectIdList.includes(item.dictValue),
      );
      const fieldValuesArray = unCheckedTaskList.map((obj) => obj.dictValue);
      const allCheckedTaskIdList =
        taskCarTeamSelectIdList.concat(fieldValuesArray);
      const allCheckedTaskList =
        taskCarTeamSelectList.concat(unCheckedTaskList);
      setTaskCarTeamSelectIdList(allCheckedTaskIdList);
      // 选中运力item集合
      setTaskCarTeamSelectList(allCheckedTaskList);
      // 选择之后针对右侧搜索项进行过滤
      handleRightSearch(rightSearchValue, false, allCheckedTaskList);
    } else {
      // 取消当前选中全部
      const taskCarTeamAllIdList = taskCarTeamData.map((obj) => obj.dictValue);
      const taskCarTeamCancelIdList = taskCarTeamSelectIdList.filter(
        (item) => !taskCarTeamAllIdList.includes(item),
      );
      setTaskCarTeamSelectIdList(taskCarTeamCancelIdList);
      const taskCarTeamCancelItemList = taskCarTeamSelectList.filter(
        (item) => !taskCarTeamAllIdList.includes(item.dictValue),
      );
      // 选中运力item集合
      setTaskCarTeamSelectList(taskCarTeamCancelItemList);
      // 选择之后针对右侧搜索项进行过滤
      handleRightSearch(rightSearchValue, false, taskCarTeamCancelItemList);
    }
  };
  // 处理右侧清空逻辑
  const handleCancelSelectedItem = () => {
    if (rightSearchValue) {
      const filterShowIdList = taskCarTeamSelectShowList.map(
        (obj) => obj.dictValue,
      );
      setTaskCarTeamSelectIdList(
        taskCarTeamSelectIdList.filter(
          (item) => !filterShowIdList.includes(item),
        ),
      );
      setTaskCarTeamSelectList(
        taskCarTeamSelectList.filter(
          (item) => !filterShowIdList.includes(item.dictValue),
        ),
      );
      setTaskCarTeamSelectShowList([]);
    } else {
      setTaskCarTeamSelectIdList([]);
      setTaskCarTeamSelectList([]);
      setTaskCarTeamSelectShowList([]);
    }
  };
  // 搜索高亮处理函数
  const handleHighLight = (searchValue, mainText) => {
    const escapedSearchValue = searchValue?.replace(/[()]/g, '\\$&');
    const regex = new RegExp(`(${escapedSearchValue})`, 'g');
    const parts = mainText.split(regex).map((part, index) => {
      if (regex.test(part)) {
        return (
          <span key={index} style={{ color: '#2761F3' }}>
            {part}
          </span>
        );
      }
      return part;
    });
    return parts;
  };
  // 弹窗取消函数
  const handleCancelClick = () => {
    setQuickAddOpen(false);
    setTaskCarTeamPopoverOpen(false);
  };

  useEffect(() => {
    setTaskCarTeamData(carTeams);
  }, [carTeams]);
  // 处理添加以上全部按钮状态
  useEffect(() => {
    if (taskCarTeamPopoverOpen) {
      if (searchCheckedItemList.length === 0) {
        setAllCheckStatus(false);
        setIndeterminateStatus(false);
      } else if (searchCheckedItemList.length === taskCarTeamData.length) {
        setAllCheckStatus(true);
        setIndeterminateStatus(false);
      } else {
        setIndeterminateStatus(true);
      }
    }
  }, [searchCheckedItemList, taskCarTeamData, taskCarTeamPopoverOpen]);
  useEffect(() => {
    if (!taskCarTeamPopoverOpen) {
      setLeftSearchValue('');
      setRightSearchValue('');
      setTaskCarTeamData(carTeams);
      const taskCarTeamIdSavedItem = carTeams?.filter((item) =>
        taskCarTeamIdSaved.includes(item.dictValue),
      );
      setTaskCarTeamSelectIdList(taskCarTeamIdSaved);
      setTaskCarTeamSelectList(taskCarTeamIdSavedItem);
      setTaskCarTeamSelectShowList(taskCarTeamIdSavedItem);
    }
  }, [taskCarTeamPopoverOpen, taskCarTeamIdSaved, carTeams]);
  // 处理当前筛选区域下所选择条数
  useEffect(() => {
    if (taskCarTeamPopoverOpen) {
      const taskCarTeamAllIdList = taskCarTeamData.map((obj) => obj.dictValue);
      const filterCheckedIdList = taskCarTeamSelectIdList?.filter((item) =>
        taskCarTeamAllIdList.includes(item),
      );
      setSearchCheckedItemList(filterCheckedIdList);
    }
  }, [
    leftSearchValue,
    taskCarTeamData,
    taskCarTeamSelectIdList,
    taskCarTeamPopoverOpen,
  ]);
  const refrain = (arr: any) => {
    // 定义tmp用于存储空数组
    let tmp: [] = [];
    if (Array.isArray(arr)) {
      // 找到重复的数据并保存到tmp中
      arr
        .concat()
        .sort()
        .sort((a, b) => {
          // a和b相等 && a元素不为空白字符串 && tmp中不存在
          if (a === b && !a.match(/^[ ]*$/) && tmp.indexOf(a) === -1) {
            tmp.push(a);
          }
        });
    }
    return tmp;
  };
  // 快捷添加确认
  const handleQuickAddOk = (e) => {
    // e.preventDefault();
    // e.stopPropagation();
    const inputList = cloneDeep(inputValue).split(/[\n]/);
    // 1.判断是否存在重复
    const refrainList = refrain(inputList);
    if (refrainList.length) {
      messageApi.open({
        type: 'warning',
        content: `存在重复运力公司， [${refrainList.toString()}]`,
      });
      return;
    }

    const unMatchList = [];
    const carTeamValue = [];
    const carTeamName = [];
    // 2、判断是否全在list里
    inputList.forEach((item) => {
      const teamName = item.trim();
      const carTeamInfo = carTeams.find((f) => f.dictDesc === teamName);
      // 批量添加不可以添加不限选项
      if (carTeamInfo && carTeamInfo?.dictValue !== 'ALL') {
        carTeamValue.push(carTeamInfo.dictValue);
        carTeamName.push(teamName);
      } else if (!teamName.match(/^[ ]*$/)) {
        unMatchList.push(teamName);
      }
    });
    if (unMatchList.length > 0) {
      messageApi.open({
        type: 'warning',
        content: `您输入的运力公司 [${unMatchList.toString()}] 不存在，请查看您选择的城市及司机业务类型下运力公司范围`,
      });
      return;
    }
    if (carTeamValue.length === 0) {
      messageApi.open({
        type: 'warning',
        content: `内容不能为空`,
      });
      return;
    }

    setQuickAddOpen(false);
    const carTeamAddList = carTeams.filter(
      (item) => carTeamValue?.includes(item.dictValue),
    );
    let checkList = [];
    let checkItemList = [];
    // 已有选中运力公司，添加后进行去重
    if (taskCarTeamSelectIdList?.length) {
      checkList = [...new Set([...taskCarTeamSelectIdList, ...carTeamValue])];
      const quickFilterItem = carTeamAddList.filter(
        (item) => !taskCarTeamSelectIdList?.includes(item?.dictValue),
      );
      checkItemList = [...taskCarTeamSelectList, ...quickFilterItem];
    } else {
      checkList = carTeamValue;
      checkItemList = carTeamAddList;
    }
    // 选中运力item集合
    setTaskCarTeamSelectList(checkItemList);
    // 选择之后针对右侧搜索项进行过滤
    handleRightSearch(rightSearchValue, false, checkItemList);
    // 选中运力id集合
    setTaskCarTeamSelectIdList(checkList);
    setInputValue('');
  };
  // 快捷添加取消
  const handleQuickAddCancel = () => {
    setQuickAddOpen(false);
  };
  const quickAddContent = (
    <div className={styles['quickAdd']}>
      <div className={styles['quickAdd-title']}>快捷输入</div>
      <div className={styles['quickAdd-content']}>
        <TextArea
          rows={4}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="请输入，需确保输入名称的完整性和正确性"
          style={{ height: 214, resize: 'none' }}
          value={inputValue}
        />
      </div>
      <div className={styles['quickAdd-footer']}>
        <Button
          size="small"
          style={{ marginRight: 8 }}
          onClick={handleQuickAddCancel}
        >
          取消
        </Button>
        <Button size="small" type="primary" onClick={handleQuickAddOk}>
          确定并添加
        </Button>
      </div>
    </div>
  );
  useEffect(() => {
    if (cityCodeSelect?.length === 0) {
      setTaskCarTeamPopoverOpen(false);
      setQuickAddOpen(false);
    }
  }, [cityCodeSelect]);
  useEffect(() => {
    if (!quickAddOpen) {
      setInputValue('');
    }
  }, [quickAddOpen]);

  const popoverContent = (
    <div className={styles['taskCarTeam']}>
      <div className={styles['taskCarTeam-content']}>
        <div className={styles['taskCarTeam-content_left']}>
          <div className={styles['taskCarTeam-content_left_top']}>
            运力公司选择（
            {searchCheckedItemList?.length + '/' + taskCarTeamData?.length}）
          </div>
          <Divider style={{ margin: 0 }} />
          <div className={styles['taskCarTeam-content_left_content']}>
            <Input
              placeholder="请输入运力公司名称"
              style={{ margin: '8px 16px', width: 248 }}
              prefix={<BLMIconFont type="BLM-ic-search-o" />}
              onChange={(e: any) => handleLeftSearch(e)}
              value={leftSearchValue}
            />
            {taskCarTeamData.length ? (
              <Tree
                checkable
                checkedKeys={taskCarTeamSelectIdList}
                onCheck={(checkedKeys, e) =>
                  handleTaskCarTeamCheck(checkedKeys, e, 'check')
                }
                onSelect={(selectedKeys, e) =>
                  handleTaskCarTeamCheck(selectedKeys, e, 'select')
                }
                fieldNames={{
                  title: 'dictDesc',
                  key: 'dictValue',
                }}
                treeData={taskCarTeamData}
                height={284}
                blockNode={true}
                titleRender={(nodeData) => {
                  return leftSearchValue.length
                    ? handleHighLight(leftSearchValue, nodeData.dictDesc)
                    : nodeData.dictDesc;
                }}
              />
            ) : (
              <div
                className={styles['taskCarTeam-content_right_content-noData']}
              >
                <div
                  className={
                    styles['taskCarTeam-content_right_content-noData_img']
                  }
                ></div>
                <p
                  className={
                    styles['taskCarTeam-content_right_content-noData_text']
                  }
                >
                  暂无数据
                </p>
              </div>
            )}
          </div>
          <Divider style={{ margin: 0 }} />
          <div className={styles['taskCarTeam-content_left_bottom']}>
            <Checkbox
              style={{ marginRight: 8 }}
              onChange={(e: any) => handleAllCheckClick(e.target.checked)}
              checked={allCheckStatus}
              indeterminate={isIndeterminateStatus}
              disabled={!taskCarTeamData.length}
            />
            添加以上全部
          </div>
        </div>
        <Divider style={{ margin: 0, height: '100%' }} type="vertical" />
        <div className={styles['taskCarTeam-content_right']}>
          <div className={styles['taskCarTeam-content_right_top']}>
            已添加{taskCarTeamSelectIdList?.length}个
            <Popover
              trigger="click"
              open={quickAddOpen}
              content={quickAddContent}
              placement="bottomRight"
              style={{ padding: 0 }}
              getPopupContainer={(targetNode: any) => {
                return targetNode?.parentNode;
              }}
              onOpenChange={(open) => {
                setQuickAddOpen(open);
              }}
            >
              <Button
                type="link"
                disabled={!carTeams.length}
                onClick={() => setQuickAddOpen(!quickAddOpen)}
              >
                快捷输入
              </Button>
            </Popover>
          </div>

          <Divider style={{ margin: 0 }} />
          <div className={styles['taskCarTeam-content_right_content']}>
            <Input
              placeholder="请输入运力公司名称"
              style={{ margin: '8px 16px', width: 248 }}
              prefix={<BLMIconFont type="BLM-ic-search-o" />}
              onChange={(e: any) => handleRightSearch(e.target.value)}
              value={rightSearchValue}
            />
            <div
              style={{
                maxHeight: 284,
                overflow: 'auto',
                padding: '0 16px',
              }}
            >
              {taskCarTeamSelectShowList.length ? (
                <VirtualList
                  data={taskCarTeamSelectShowList}
                  height={284}
                  itemHeight={44}
                  itemKey={(item) => item?.dictValue}
                >
                  {(item, index) => (
                    <div
                      key={index}
                      className={
                        styles['taskCarTeam-content_right_content-render']
                      }
                    >
                      <div
                        className={
                          styles['taskCarTeam-content_right_content-item']
                        }
                      >
                        <div
                          className={
                            styles[
                              'taskCarTeam-content_right_content-item-text'
                            ]
                          }
                        >
                          {rightSearchValue.length
                            ? handleHighLight(
                                rightSearchValue,
                                taskCarTeamSelectShowList[index]?.dictDesc,
                              )
                            : taskCarTeamSelectShowList[index]?.dictDesc}
                        </div>
                        <BLMIconFont
                          type="BLM-ic-close-o"
                          onClick={() => {
                            handelDeleteItem(taskCarTeamSelectShowList[index]);
                          }}
                        />
                      </div>
                    </div>
                  )}
                </VirtualList>
              ) : (
                <div
                  className={styles['taskCarTeam-content_right_content-noData']}
                >
                  <div
                    className={
                      styles['taskCarTeam-content_right_content-noData_img']
                    }
                  ></div>
                  <p
                    className={
                      styles['taskCarTeam-content_right_content-noData_text']
                    }
                  >
                    暂无数据
                  </p>
                </div>
              )}
            </div>
          </div>
          <Divider style={{ margin: 0 }} />
          <Button
            type="link"
            className={styles['taskCarTeam-content_right_bottom']}
            disabled={!taskCarTeamSelectShowList.length}
            onClick={handleCancelSelectedItem}
          >
            {rightSearchValue ? '清空检索项' : '清空'}
          </Button>
        </div>
      </div>
      <Divider style={{ margin: 0 }} />
      <div className={styles['taskCarTeam-footer']}>
        <Button style={{ marginRight: 8 }} onClick={() => handleCancelClick()}>
          取消
        </Button>
        <Button type="primary" onClick={handleAddClick}>
          确定
        </Button>
      </div>
    </div>
  );

  return (
    <>
      {contextHolder}
      <Popover
        overlayClassName={'taskCarTeamNewPop'}
        trigger="click"
        open={taskCarTeamPopoverOpen}
        content={popoverContent}
        placement="bottomRight"
        arrow={false}
        style={{ padding: 0 }}
        onOpenChange={() => {
          if (cityCodeSelect?.length) {
            setTaskCarTeamPopoverOpen(!taskCarTeamPopoverOpen);
          }
        }}
      >
        <Select
          mode="multiple"
          allowClear
          onClear={(e) => {
            form.setFieldValue('taskCarTeamId', []);
            setTaskCarTeamSelectIdList([]);
            setTaskCarTeamSelectList([]);
            setTaskCarTeamSelectShowList([]);
            setTaskCarTeamIdList([]);
          }}
          open={false}
          placeholder="不限"
          options={carTeams}
          value={form.getFieldValue('taskCarTeamId')}
          fieldNames={{
            label: 'dictDesc',
            value: 'dictValue',
          }}
          disabled={!cityCodeSelect?.length}
        />
      </Popover>
    </>
  );
};
export default TaskCarTeamNew;
