.taskCarTeam {
  width: 560px;
  height: 458px;
  &-content {
    display: flex;
    height: 410px;
    &_left {
      width: 280px;
      &_top {
        height: 42px;
        padding: 10px 16px;
        font-weight: 500;
      }
      &_content {
        height: 330px;
        padding-bottom: 8px;
      }
      &_bottom {
        padding: 8px 16px;
      }
    }
    &_right {
      width: 280px;
      &_top {
        height: 42px;
        padding: 10px 16px;
        font-weight: 500;
        display: flex;
        justify-content: space-between;
        :global {
          .ant-btn-link {
            padding: 0px !important;
            line-height: normal;
            height: auto;
          }
        }
      }
      &_content {
        height: 330px;
        width: 100%;
        &-render {
          padding: 8px 0 4px 0;
        }
        &-item {
          display: flex;
          justify-content: space-between;
          height: 32px;
          background-color: #f7f7f9;
          border-radius: 4px;
          padding: 4px 8px;
          line-height: 24px;
          &:hover {
            cursor: pointer;
          }
          &-text {
            white-space: nowrap; /* 让文本不换行 */
            overflow: hidden; /* 隐藏溢出的文本 */
            text-overflow: ellipsis;
          }
        }
        &-noData {
          &_img {
            background-image: url(../../../../../../assets/img/noData1x.png);
            width: 60px;
            height: 60px;
            margin: 88px auto 8px;
          }
          &_text {
            text-align: center;
            color: rgba(0, 0, 0, 0.6);
          }
        }
      }
      &_bottom {
        padding: 8px 16px;
        float: right;
      }
    }
  }
  &-footer {
    height: 48px;
    padding: 8px 16px;
    text-align: right;
  }
}
.quickAdd {
  width: 260px;
  height: 310px;
  padding: 8px 16px;
  &-title {
    font-size: 16px;
    font-weight: 500;
    padding: 8px 0;
  }
  &-footer {
    margin-top: 8px;
    float: right;
  }
}
.taskCarInput {
  border-radius: 4px;
  border: 1px solid transparent;
  height: 32px;
  background-color: rgba(37, 52, 79, 0.03);
  padding-left: 4px;
  line-height: 30px;
  display: flex;
  justify-content: space-between;
  // cursor: pointer;
  &:hover {
    background-color: rgba(244, 245, 246);
    .taskCarInput-icon_arrDown {
      display: none;
    }
    .taskCarInput-icon_empty {
      display: block;
    }
  }
  // &: {
  //   border: 1px solid #366cfe;
  //   background-color: #fff;
  // }
  &-icon {
    width: 24px;
    padding: 8px 4px;
    &_empty {
      display: none;
    }
    &_arrDown {
      display: block;
      color: rgba(0, 0, 0, 0.3);
    }
  }
}

:global {
  .taskCarTeamNewPop {
    .ant-tree-switcher {
      width: 0px !important;
    }
    .ant-tree-checkbox {
      margin-left: 16px !important;
    }
    .ant-tree-treenode {
      padding: 2px 0 2px 0 !important;
      &:hover {
        background-color: #f3f3f4;
      }
    }
    .ant-select-dropdown {
      z-index: 1000 !important;
    }
    .ant-tree-node-content-wrapper {
      white-space: nowrap; /* 让文本不换行 */
      overflow: hidden; /* 隐藏溢出的文本 */
      text-overflow: ellipsis;
      padding: 0px !important;
      &:hover {
        background-color: transparent !important;
      }
    }
    .ant-tree-node-selected {
      background-color: transparent !important;
    }
    .ant-popover-inner {
      padding: 0 !important;
    }
  }
}
