import React, { useState, useEffect } from 'react';
import {
  Button,
  Modal,
  Alert,
  BLMIconFont,
  message,
  Descriptions,
  Divider,
  Spin,
} from '@blmcp/ui';
import { previewNewCrowdPower } from '@/pages/userPortrait/api';
import { LoadingOutlined } from '@ant-design/icons';
import styles from './index.less';

const PreCalibration = ({
  isCheck,
  preCalibrationOpen,
  preViewStatusNumber, // 预估人数
  crowdRuleScopeData, // 圈选粒度
  activeStatusName,
  operationOtherStrShow, // 数据池展示部分
  preCalibrationData,
  adcodeThreeLevelList, // 选中的城运车树级结构
  preViewCallback,
  callBackChange,
}) => {
  const [open, setOpen] = useState(false);

  const [dataList, setDataList] = useState([]);
  // 有权限的数据
  const [cityList, setCityList] = useState([]);
  const [carTeamList, setCarTeamList] = useState([]);
  const [fleetList, setFleetList] = useState([]);
  // 预估人数
  const [estimateNumber, setEstimateNumber] = useState('--');
  const [loadingEstimate, setLoadingEstimate] = useState(false);

  // const messageText =
  //   '注：以下圈选范围不包含无权限范围字段，无权限范围字段在圈选时不生效。';

  // 点击预估人数
  const estimateClick = (open = true, isOky) => {
    // 再次校验
    previewNewCrowdPower({ adcodeThreeLevelList })
      .then((res) => {
        if (res?.code === 1 && res?.data) {
          const hasPermissionCheckVO = res.data?.hasPermissionCheckVO;
          const noPermissionCheckVO = res.data?.noPermissionCheckVO;
          // 判断本次和上次权限是否存在不同, 如果存在不同，提示用户刷新页面
          const hasPowerList = dataList?.hasPermissionCheckVO;
          const noPowerList = dataList?.noPermissionCheckVO;

          const hasCityList =
            hasPermissionCheckVO?.adCodeListVOS
              .map((i) => i.adcode)
              ?.join(',') ?? '';
          const hasCityList2 =
            hasPowerList?.adCodeListVOS?.map((i) => i.adcode)?.join(',') ?? '';
          const hasCarTeamList =
            hasPermissionCheckVO?.transportCompanyListVOS
              .map((i) => i.transportCompanyId)
              ?.join(',') ?? '';
          const hasCarTeamList2 =
            hasPowerList?.transportCompanyListVOS
              ?.map((i) => i.transportCompanyId)
              ?.join(',') ?? '';
          const hasFleetList =
            hasPermissionCheckVO?.fleetListVOS
              .map((i) => i.fleetId)
              ?.join(',') ?? '';
          const hasFleetList2 =
            hasPowerList?.fleetListVOS?.map((i) => i.fleetId)?.join(',') ?? '';
          // 先判断 城市权限是否变更
          if (hasCityList === hasCityList2) {
            // 判断运力公司权限是否变更
            if (hasCarTeamList === hasCarTeamList2) {
              // 判断车队权限是否变更
              if (hasFleetList === hasFleetList2) {
                // 判断是否存在无权限的数据
                if (!noPermissionCheckVO?.length) {
                  // 剔除不符合的数据，将正确的参数传给服务
                  setDataList(res.data);
                  callBackChange(open, res.data, isOky);
                } else {
                  // 不存在无权限数据时直接调应获取预估人数的方法
                  callBackChange(open, res.data, isOky);
                }
              } else {
                message.error('【车队】权限校验未通过，请刷新页面后重试');
                setLoadingEstimate(false);
              }
            } else {
              message.error('【运力公司】权限校验未通过，请刷新页面后重试');
              setLoadingEstimate(false);
            }
          } else {
            message.error('【城市】权限校验未通过，请刷新页面后重试');
            setLoadingEstimate(false);
          }
        }
      })
      .catch((e) => {
        setLoadingEstimate(false);
      });
  };

  const handleOnCancel = () => {
    setLoadingEstimate(false);
    callBackChange(false);
  };
  const handleModalOK = () => {
    setLoadingEstimate(false);
    estimateClick(true, false);
  };

  useEffect(() => {
    if (preCalibrationOpen) {
      setDataList(preCalibrationData);
      if (preCalibrationData) {
        const { hasPermissionCheckVO } = preCalibrationData;
        if (hasPermissionCheckVO) {
          const { adCodeListVOS, transportCompanyListVOS, fleetListVOS } =
            hasPermissionCheckVO;
          let cityName = adCodeListVOS?.map((i) => i.cityName) ?? [];
          let carTeamName =
            transportCompanyListVOS?.map((i) => i.transportCompanyName) ?? [];
          let fleetName = fleetListVOS?.map((i) => i.fleetName) ?? [];
          setCityList(cityName);
          setCarTeamList(carTeamName);
          setFleetList(fleetName);
        }
      }
    }
    setOpen(preCalibrationOpen);
  }, [preCalibrationOpen, isCheck, JSON.stringify(preCalibrationData)]);
  useEffect(() => {
    if (!preViewCallback) {
      setLoadingEstimate(false);
    }
  }, [preViewCallback]);

  const getItems = () => {
    let arr = [{ key: '1', label: '城市', children: cityList.join('、') }];
    if (crowdRuleScopeData === 2) {
      arr = [
        { key: '1', label: '城市', children: cityList.join('、') },
        { key: '2', label: '运力公司', children: carTeamList.join('、') },
      ];
    } else if (crowdRuleScopeData === 3) {
      arr = [
        { key: '1', label: '城市', children: cityList.join('、') },
        { key: '2', label: '运力公司', children: carTeamList.join('、') },
        { key: '3', label: '车队', children: fleetList.join('、') },
      ];
    }
    if (activeStatusName) {
      arr.push({
        key: 'activeStatusValue',
        label: '账号状态',
        children: activeStatusName.join('、'),
      });
    }
    return arr;
  };

  useEffect(() => {
    //
  }, []);

  return (
    <Modal
      title="圈选范围"
      width={560}
      open={open}
      centered={true}
      okText={'创建人群'}
      onOk={() => handleModalOK()}
      onCancel={() => {
        handleOnCancel();
      }}
    >
      {/*<Alert message={messageText} type="info" showIcon />*/}
      <div className={styles['contentText']}>
        <Descriptions
          column={1}
          labelStyle={{ width: '90px' }}
          items={getItems()}
        />
        {operationOtherStrShow ? <Divider /> : null}

        <div dangerouslySetInnerHTML={{ __html: operationOtherStrShow }} />
      </div>
      {isCheck ? (
        <div className={styles['footerContent']}>
          预估司机人数
          <span style={{ margin: '0 10px' }}>{preViewStatusNumber}人</span>
          <div
            style={{ display: 'inline-block', color: '#366CFE' }}
            onClick={() => {
              setLoadingEstimate(true);
              estimateClick(true, true);
            }}
          >
            <Spin
              indicator={<LoadingOutlined spin />}
              size="small"
              spinning={loadingEstimate}
            >
              <BLMIconFont style={{ color: '#366CFE' }} type="BLM-ic-loop-o" />
              点击预估
            </Spin>
          </div>
        </div>
      ) : null}
    </Modal>
  );
};

export default PreCalibration;
