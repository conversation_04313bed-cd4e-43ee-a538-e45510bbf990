export interface ICurInfo {
  key: string;
  value: string | string[] | null;
  options: any[];
  multiple: boolean;
}

export interface IDepInfo {
  key: string;
  value: string | string[] | null;
  multiple: boolean;
}

export const getCurByDep = (curInfo: ICurInfo, depInfo: IDepInfo) => {
  let ret: string | string[] | null = null;
  if (!(curInfo.value && curInfo.options && curInfo.options.length > 0)) {
    return curInfo.value;
  }
  if (depInfo.multiple) {
    if (
      depInfo.value &&
      Array.isArray(depInfo.value) &&
      depInfo.value.length > 0
    ) {
      if (curInfo.multiple) {
        if (Array.isArray(curInfo.value) && curInfo.value.length > 0) {
          const retFilter = curInfo.value.filter((id: string) => {
            const info = curInfo.options.find(
              (item: any) => item[curInfo.key] === id,
            );
            return (
              info && (depInfo.value as string[]).includes(info[depInfo.key])
            );
          });
          ret = retFilter.length > 0 ? retFilter : null;
        }
      } else {
        const info = curInfo.options.find(
          (item: any) => item[curInfo.key] === curInfo.value,
        );
        ret =
          info && depInfo.value.includes(info[depInfo.key])
            ? curInfo.value
            : null;
      }
    }
  } else if (depInfo.value) {
    if (curInfo.multiple) {
      if (Array.isArray(curInfo.value) && curInfo.value.length > 0) {
        const retFilter = curInfo.value.filter((id: string) => {
          const info = curInfo.options.find(
            (item: any) => item[curInfo.key] === id,
          );
          return info && depInfo.value === info[depInfo.key];
        });
        ret = retFilter.length > 0 ? retFilter : null;
      }
    } else {
      const info = curInfo.options.find(
        (item: any) => item[curInfo.key] === curInfo.value,
      );
      ret = info && depInfo.value === info[depInfo.key] ? curInfo.value : null;
    }
  }

  return ret;
};
