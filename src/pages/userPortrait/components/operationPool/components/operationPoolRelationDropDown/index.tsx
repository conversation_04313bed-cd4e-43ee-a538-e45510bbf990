import React, { useCallback, useState } from 'react';
import {
  Input,
  BLMIconFont,
  Dropdown,
} from '@blmcp/ui';
import type { MenuProps } from '@blmcp/ui';

const OperationPoolRelationDropDown = ({ form, fieldIndex, field, operationPoolChange, setOperationPoolChange }) => {
  const [selectedStatusItem, setSelectedStatusItem] = useState(
    form.getFieldValue(['containerList', fieldIndex, 'status']),
  );
  // 组合池运算符改变
  const clickUpDownType = (type) => {
    setSelectedStatusItem(type.key);
    const handleOperationPoolRadio = operationPoolChange.map(item => {
      // 检查是否是目标元素
      if (item.key === field.fieldKey) {
        // 创建新对象并更新指定字段
        return {
          ...item,
          status: type.key,
        };
      }
      // 对于其他元素，保持不变
      return item;
    });
    setOperationPoolChange(handleOperationPoolRadio)
    form.setFields([
      {
        name: ['containerList', fieldIndex, 'status'],
        value: type.key,
      },
    ]);
  };
  const statusEnumValues = [
    {
      label: '交集',
      value: 'AND',
      iconValue: 'BLM-ywIc-intersection',
    },
    {
      label: '并集',
      value: 'OR',
      iconValue: 'BLM-ywIc-union',
    },
    {
      label: '差集',
      value: 'MINUS',
      iconValue: 'BLM-ywIc-differenceSet-o',
    },
  ];
  const items: MenuProps['items'] = [
    {
      label: <span>交集</span>,
      key: 'AND',
      icon: (
        <BLMIconFont
          style={{ color: '#366CFE', fontSize: 16 }}
          type="BLM-ywIc-intersection"
        />
      ),
    },
    {
      label: <span>并集</span>,
      key: 'OR',
      icon: (
        <BLMIconFont
          style={{ color: '#366CFE', fontSize: 16 }}
          type="BLM-ywIc-union"
        />
      ),
    },
    {
      label: <span>差集</span>,
      key: 'MINUS',
      icon: (
        <BLMIconFont
          style={{ color: '#366CFE', fontSize: 16 }}
          type="BLM-ywIc-differenceSet-o"
        />
      ),
    },
  ];
  // 查找当前运算池信息
  const handleStatusOperationPool = useCallback(() => {
    if (selectedStatusItem) {
      return statusEnumValues.find(
        (statusItem) => statusItem.value === selectedStatusItem,
      );
    } else return;
  }, [selectedStatusItem]);
  return (
    // {fieldName === 0 ?
    <Dropdown
      menu={{
        items: items,
        onClick: (e) => clickUpDownType(e),
      }}
      trigger={['click']}
      overlayStyle={{ minWidth: 'auto' }}
    >
      <div style={{ color: '#366CFE', cursor: 'pointer' }}>
        <BLMIconFont
          style={{ marginRight: 4 }}
          type={handleStatusOperationPool()?.iconValue}
        />
        {handleStatusOperationPool()?.label}
        <BLMIconFont style={{ marginLeft: 4 }} type="BLM-ic-arrDown-o" />
      </div>
    </Dropdown>
    // : null
    // }
  );
};
export default OperationPoolRelationDropDown;
