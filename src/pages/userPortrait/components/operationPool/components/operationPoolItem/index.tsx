import React, { useEffect, useState } from 'react';
import { Input, Radio, Form, BLMIconFont, Tooltip } from '@blmcp/ui';
import OperationTagItem from '../operationTagItem/index';
import styles from './index.less';

const OperationPoolItem = ({
  operationPoolItemForm,
  field,
  remove,
  isShowDelete,
  operationPoolChange,
  setOperationPoolChange,
}) => {
  // 运算池内标签list
  const [tagItemList, setTagItemList] = useState(
    operationPoolChange.find((item) => item.key === field.key)?.tagList,
  );
  // 运算池删除
  const handleOperationPoolDelete = (key) => {
    remove(field.name);

    const updatedData = operationPoolChange.map((item) => {
      if (item.key === field.key) {
        // 找到匹配项，修改isdeleted字段
        return { ...item, isDeleted: true };
      }
      // 保持不变的项
      return item;
    });
    setOperationPoolChange(updatedData);
  };
  // 删除tag
  const handleOperationPoolClick = (item) => {
    const currOperationPoolChange = [...operationPoolChange];
    currOperationPoolChange[field.key]?.tagList.splice(item.idx, 1, {
      ...operationPoolChange[field.key]?.tagList[item.idx],
      isTagDelete: true,
    });
    setOperationPoolChange(currOperationPoolChange);
  };
  // 仅在当前运算池有标签改动触发
  useEffect(() => {
    setTagItemList(
      operationPoolChange.find((item) => item.key === field.key)?.tagList,
    );
  }, [operationPoolChange.find((item) => item.key === field.key)]);
  const handleRadioChange = (e, field) => {
    const handleOperationPoolRadio = operationPoolChange.map(item => {
      // 检查是否是目标元素
      if (item.key === field.fieldKey) {
        // 创建新对象并更新指定字段
        return {
          ...item,
          operator: e.target.value
        };
      }
      // 对于其他元素，保持不变
      return item;
    });
    setOperationPoolChange(handleOperationPoolRadio)
  }

  return (
    <div className={styles['operationPoolItem']}>
      <div className={styles['operationPoolItem-header']}>
        <div className={styles['operationPoolItem-header_title']}>
          {'标签组合池' + (field.name + 1)}
        </div>
        <div className={styles['operationPoolItem-header_status']}>
          <Form.Item
            name={[field.name, 'operator']}
            initialValue={'AND'}
            noStyle
          >
            <Radio.Group size="small" onChange={(e) => handleRadioChange(e, field)}>
              <Radio.Button value="AND">
                池内取交集
                <Tooltip title={'同时包含以下特征的司机'}>
                  <BLMIconFont
                    style={{
                      fontSize: 14,
                      marginLeft: 4,
                    }}
                    type="BLM-ic-information"
                  // onClick={() => remove(field.name)}
                  />
                </Tooltip>
              </Radio.Button>
              <Radio.Button value="OR">
                池内取并集
                <Tooltip title={'包含以下任一特征的司机'}>
                  <BLMIconFont
                    style={{
                      fontSize: 14,
                      marginLeft: 4,
                    }}
                    type="BLM-ic-information"
                  // onClick={() => remove(field.name)}
                  />
                </Tooltip>
              </Radio.Button>
            </Radio.Group>
          </Form.Item>
          {isShowDelete && (
            <BLMIconFont
              style={{ color: '#606266', fontSize: 14, marginLeft: 8 }}
              type="BLM-ic-delete-o"
              onClick={() => handleOperationPoolDelete(field.key)}
            />
          )}
        </div>
      </div>
      <div className={styles['operationPoolItem-content']}>
        <div className={styles['operationPoolItem-content_left']}></div>
        <div className={styles['operationPoolItem-content_rightBox']}>
          {tagItemList?.filter(item => item.isTagDelete === false)?.length ?
            tagItemList?.map(
              (item, index) => (
                <OperationTagItem
                  key={item.idx}
                  tagItem={item}
                  handleOperationPoolClick={handleOperationPoolClick}
                  operationPoolChange={operationPoolChange}
                  setOperationPoolChange={setOperationPoolChange}
                  operationFieldKey={{ key: field.key }}
                  field={field}
                />
              ),
            ) :
            <div style={{ color: 'rgba(0, 0, 0, 0.26)' }}>请从左侧列表中添加标签或人群</div>}
        </div>
      </div>
    </div>
  );
};
export default OperationPoolItem;
