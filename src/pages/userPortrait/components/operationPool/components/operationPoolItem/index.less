.operationPoolItem {
  &-header {
    width: 372px;
    display: flex;
    justify-content: space-between;
    align-items: baseline;

    &_status {
      // padding-right: 16px;
    }

    :global {
      .ant-radio-button-wrapper {
        color: rgba(0, 0, 0, 0.6);
      }

      .ant-radio-button-wrapper-checked {
        color: #366cfe;
      }
    }
  }

  &-content {
    display: flex;
    margin-top: 8px;

    &_left {
      margin: 0 16px;
      border-left: 1px solid #e7e8eb;
    }

    &_rightBox {
      width: 340px;
      height: 168px;
      border-radius: 6px;
      background: #f7f7f9;
      padding: 16px 0 16px 16px;
      overflow: auto;
      display: flex;
      flex-wrap: wrap;
      justify-content: flex-start;
    }
  }
}