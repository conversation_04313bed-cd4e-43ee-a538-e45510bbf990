import React, { useEffect, useState } from 'react';
import { Modal, Input } from '@blmcp/ui';
import { cloneDeep } from 'lodash';

const { TextArea } = Input;

const CityBatchAdd: React.FC = ({
  isCityOpen = false,
  allCityList = [],
  cityCancel = () => {},
}) => {
  const [isModalOpen, setIsModalOpen] = useState(true);
  const [inputValue, setInputValue] = useState('');

  const refrain = (arr: any) => {
    // 定义tmp用于存储空数组
    let tmp: [] = [];
    if (Array.isArray(arr)) {
      // 找到重复的数据并保存到tmp中
      arr
        .concat()
        .sort()
        .sort((a, b) => {
          // a和b相等 && a元素不为空白字符串 && tmp中不存在
          if (a === b && !a.match(/^[ ]*$/) && tmp.indexOf(a) === -1) {
            tmp.push(a);
          }
        });
    }
    return tmp;
  };
  const handleOk = () => {
    const cityList = cloneDeep(inputValue).split(/[\n]/);
    // 1.判断是否存在重复
    const refrainList = refrain(cityList);
    if (refrainList.length) {
      Modal.warning({
        title: '提示',
        content: `填写数据存在错误， [${refrainList.toString()}]重复`,
        okText: '确定',
      });
      return;
    }
    // 2、判断是否全在list里
    const unCity = [];
    const cityCodes = [];
    const cityNames = [];
    cityList.forEach((city) => {
      const cityObj = allCityList.find((item) => {
        return item.name === city.replace(/^\s+|\s+$/g, '');
      });
      if (cityObj) {
        cityCodes.push(cityObj.code);
        cityNames.push(city);
      } else if (!city.match(/^[ ]*$/)) {
        unCity.push(city);
      }
    });
    if (unCity.length > 0) {
      Modal.warning({
        title: '提示',
        content: `不存在[${cloneDeep(unCity).toString()}]`,
        okText: '确定',
      });
      return;
    }
    if (cityCodes.length === 0) {
      Modal.warning({
        title: '提示',
        content: `内容不能为空`,
        okText: '确定',
      });
      return;
    }

    setIsModalOpen(false);
    cityCancel({ cityCodes, cityNames });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    cityCancel();
  };

  const onChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    setInputValue(e.target.value);
  };

  useEffect(() => {
    setIsModalOpen(isCityOpen); // 控制Modal的打开和关闭
  }, [isCityOpen]);

  const contextText = {
    margin: '16px 0 8px 0',
    color: '#a9a9a9',
    fontSize: '14px',
  };

  return (
    <Modal
      title="批量添加城市"
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={false}
      destroyOnClose
      width={560}
    >
      <div style={contextText}>
        <span>填写说明：</span>
        <br />
        <span>
          1、城市名称必须完整输入并回车确定，如：北京市，西南布依族苗族自治州;
        </span>
        <br />
        <span>2、城市名称不能重复。</span>
      </div>
      <TextArea rows={4} onChange={onChange} placeholder="请输入城市名称" />
    </Modal>
  );
};

export default CityBatchAdd;
