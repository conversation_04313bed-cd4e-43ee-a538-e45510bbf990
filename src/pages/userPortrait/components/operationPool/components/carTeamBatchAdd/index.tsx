import React, { useEffect, useState } from 'react';
import { Modal, Input, Spin } from '@blmcp/ui';
import { cloneDeep } from 'lodash';
import { getQueryTransportCompanyByNames } from '@/pages/userPortrait/api/index';

const { TextArea } = Input;

const CarTeamBatchAdd: React.FC = ({
  isCarTeamOpen,
  selectAdcodeList = [],
  carTeamCancel = () => {},
}) => {
  const [isModalOpen, setIsModalOpen] = useState(true);
  const [inputValue, setInputValue] = useState('');
  const [loading, setLoading] = React.useState<boolean>(false);

  const refrain = (arr: any) => {
    // 定义tmp用于存储空数组
    let tmp: [] = [];
    if (Array.isArray(arr)) {
      // 找到重复的数据并保存到tmp中
      arr
        .concat()
        .sort()
        .sort((a, b) => {
          // a和b相等 && a元素不为空白字符串 && tmp中不存在
          if (a === b && !a.match(/^[ ]*$/) && tmp.indexOf(a) === -1) {
            tmp.push(a);
          }
        });
    }
    return tmp;
  };
  const handleOk = () => {
    setLoading(true);
    const inputList = cloneDeep(inputValue).split(/[\n]/);
    // 先过滤无效项，再处理有效项的前后空格
    const filteredArr = inputList
      ?.filter((item) => item.trim())
      .map((item) => item.trim());
    //  判断输入框是否为空
    if (filteredArr?.length === 0) {
      Modal.warning({
        title: '提示',
        content: `内容不能为空`,
        okText: '确定',
        onOk: () => {
          setLoading(false);
        },
        onCancel: () => {
          setLoading(false);
        },
      });
      return;
    } else if (filteredArr?.length > 500) {
      // 判断数据是否超过500条
      Modal.warning({
        title: '提示',
        content: `批量添加最多支持500条，已超出，请调整后重试`,
        okText: '确定',
        onOk: () => {
          setLoading(false);
        },
        onCancel: () => {
          setLoading(false);
        },
      });
      return;
    }
    // 判断是否存在重复
    const refrainList = refrain(inputList);
    if (refrainList.length) {
      Modal.warning({
        title: '提示',
        content: `存在重复运力公司， [${refrainList.toString()}]`,
        okText: '确定',
        onOk: () => {
          setLoading(false);
        },
        onCancel: () => {
          setLoading(false);
        },
      });
      return;
    }
    // 将过滤后的数据进行去重处理，用去重后的有效数据作为入参传给服务
    const setFilteredArr = [...new Set(filteredArr)];
    // 调取接口，根据接口返回数据判断用户输入的数据是否正确
    getQueryTransportCompanyByNames({
      authType: 2,
      selectAdcodeList: selectAdcodeList,
      selectTransportCompanyNameList: setFilteredArr,
    })
      .then((res) => {
        if (res?.code === 1 && res?.data) {
          if (res.data?.notExistCompanyList?.length) {
            let unMatchList: any = [];
            res.data?.notExistCompanyList?.forEach((item: any) => {
              item?.transportCompanyName &&
                unMatchList.push(item.transportCompanyName);
            });
            Modal.warning({
              title: '提示',
              content: `您输入的运力公司 [${unMatchList.toString()}] 不存在，请查看您选择的城市及司机业务类型下运力公司范围`,
              okText: '确定',
              onOk: () => {
                setLoading(false);
              },
              onCancel: () => {
                setLoading(false);
              },
            });
            return;
          } else {
            const carTeamValue: any = [];
            res.data?.existCompanyList?.forEach((item: any) => {
              item?.transportCompanyId &&
                carTeamValue.push(item.transportCompanyId);
            });
            // 结束按钮loading
            setLoading(false);
            // 接口正确返回才关闭弹窗并将对应的数据返回给运力公司组件
            setIsModalOpen(false);
            carTeamCancel({ carTeamValue });
          }
        } else {
          setLoading(false);
        }
      })
      .catch(() => {
        setLoading(false);
      });
  };

  const handleCancel = () => {
    setIsModalOpen(false);
    carTeamCancel();
  };

  const onChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
  ) => {
    setInputValue(e.target.value);
  };

  useEffect(() => {
    setIsModalOpen(isCarTeamOpen); // 控制Modal的打开和关闭
  }, [isCarTeamOpen]);

  const contextText = {
    margin: '16px 0 8px 0',
    color: '#a9a9a9',
    fontSize: '14px',
  };

  return (
    <Modal
      title="批量添加运力公司"
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      maskClosable={false}
      destroyOnClose
      width={560}
      confirmLoading={loading}
    >
      <Spin spinning={loading}>
        <div style={contextText}>
          <span>填写说明：</span>
          <br />
          <span>1、运力公司名称必须完整输入并回车确定。</span>
          <br />
          <span>2、运力公司名称不能重复。</span>
        </div>
        <TextArea
          rows={4}
          onChange={onChange}
          placeholder="请输入运力公司名称"
        />
      </Spin>
    </Modal>
  );
};

export default CarTeamBatchAdd;
