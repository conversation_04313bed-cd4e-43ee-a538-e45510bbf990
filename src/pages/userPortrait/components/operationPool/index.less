.userPortrait {
  width: 404px;
  display: flex;
  height: 100%;
  flex-direction: column;
}

.userPortraitNew {
  width: 404px;
  display: flex;
  height: 100%;
  flex-direction: column;

  .calculationText {
    font-size: 14px;
    font-width: 500;
    color: rgba(0, 0, 0, 0.9);
    margin-top: 0px;
    margin-bottom: 8px;
  }
  .estimatedText {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.6);
    margin-top: 8px;
    margin-bottom: 8px;
  }
  .estimatedNum {
    display: flex;
    justify-content: left;
    margin-bottom: 16px;
    font-size: 14px;
    flex-wrap: wrap;

    &-people {
      font-weight: 500;
      margin-right: 16px;
    }

    &-btn {
      color: rgba(39, 97, 243, 1);
      cursor: pointer;
    }
  }
}
