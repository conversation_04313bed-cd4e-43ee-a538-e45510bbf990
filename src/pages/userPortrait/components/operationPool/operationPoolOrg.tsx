import { LoadingOutlined } from '@ant-design/icons';
import {
  BLMFleetSelect,
  BLMOrgBtCarTeam,
  BLMOrgBtCity,
} from '@blmcp/peento-businessComponents';
import {
  BLMIconFont,
  Button,
  Checkbox,
  Col,
  DatePicker,
  Divider,
  Dropdown,
  Form,
  Input,
  message,
  Modal,
  Radio,
  Row,
  Select,
  Spin,
  Tooltip,
} from '@blmcp/ui';
import dayjs from 'dayjs';
import React, { useEffect, useImperativeHandle, useRef, useState } from 'react';
import { blmAnalysisModuleClick } from '@/utils/eventTracking';
import PreCalibration from '@/pages/userPortrait/components/operationPool/components/preCalibrationModal';
import {
  createNewCrowd,
  previewCallback,
  previewNewCrowd,
  previewNewCrowdPower,
} from '../../api/index';
import CarTeamBatchAdd from './components/carTeamBatchAdd';
import CityBatchAdd from './components/cityBatchAdd/index';
import OperationPoolItem from './components/operationPoolItem/index';
import OperationPoolRelationDropDown from './components/operationPoolRelationDropDown/index';
import { getCurByDep } from './components/utils';
import styles from './index.less';

const { TextArea } = Input;

const OperationPool = React.forwardRef(function (
  {
    operationPoolChange,
    setOperationPoolChange,
    copyBaseInformation,
    copyOperationPoolChange,
  },
  ref,
) {
  const [form] = Form.useForm();
  const [createCrowdForm] = Form.useForm();
  const formRef = useRef();
  const cityRef = useRef(null);
  const carTeamRef = useRef(null);
  const fleetRef = useRef(null);

  // 运算池可滚动高度
  const [maxHeight, setMaxHeight] = useState(0);
  // 当前可选城市所有数据源
  const [allCityList, setAllCityList] = useState([]);
  // 所有城市code
  const [dictValueList, setDictValueList] = useState([]);
  // 所有城市名称
  const [dictDescList, setDictDescList] = useState([]);
  // 已选城市options
  const [citySelectedOptions, setCitySelectedOptions] = useState([]);
  // 已选城市名称
  const [cityNameList, setCityNameList] = useState([]);
  // 已选城市id
  const [registerCityCode, setRegisterCityCode] = useState([]);
  // 当前可选运力公司所有数据源
  const [allCarTeamList, setAllCarTeamList] = useState([]);
  // 已选运力公司数据options
  const [carTeamSelectedOptions, setCarTeamSelectedOptions] = useState([]);
  // 已选运力公司名称
  const [taskCarTeamIdList, setTaskCarTeamIdList] = useState([]);
  // 已选运力公司id
  const [registerCarTeamCode, setRegisterCarTeamCode] = useState([]);
  // 可选车队数据源
  const [fleetIdOptions, setFleetIdOptions] = useState([]);
  // 已选车队数据options
  const [fleetSelectedOptions, setFleetSelectedOptions] = useState([]);
  // 已选车队名称
  const [fleetNameList, setFleetNameList] = useState([]);
  // 已选车队id
  const [registerFleetCode, setRregisterFleetCode] = useState([]);
  // 城市&运力公司&车队的层级结构
  const [adcodeThreeLevelList, setAdcodeThreeLevelList] = useState([]);
  // 已选账号状态值
  const [activeStatusValue, setActiveStatus] = useState([]);
  // 已选账号状态名称
  const [activeStatusName, setActiveStatusName] = useState([]);
  // 创建人群弹窗开启
  const [createModal, setCreateModal] = useState(false);
  // 批量添加城市
  const [isCityOpen, setIsCityOpen] = useState(false);
  // 批量添加运力公司
  const [isCarTeamOpen, setIsCarTeamOpen] = useState(false);
  // 运算结果与服务交互话术
  const [operationResult, setOperationResult] = useState('');
  // 运算结果展示话术
  const [operationResultShow, setOperationResultShow] = useState('');
  // 运算结果 预校验弹窗 展示话术
  const [operationOtherStrShow, seOperationOtherStrShow] = useState('');
  // 圈选力度
  const [crowdRuleScopeData, setCrowdRuleScopeData] = useState(1);
  // 是否预览模式
  const [isCheck, setIsCheck] = useState(false);
  // 预校验提示文案
  const [preCalibrationOpen, setPreCalibrationOpen] = useState(false);
  // 校验后得到的数据
  const [preCalibrationData, setPreCalibrationData] = useState(null);

  // 预估unid
  const [preViewStatus, setPreViewStatus] = useState(false);
  // 预估司机人数
  const [preViewStatusNumber, setPreViewStatusNumber] = useState('--');
  //
  const [containerSetHandleData, setContainerSetHandleData] = useState(null);
  // 是否执行预估人数
  const [isStart, setIsStart] = useState(false);
  // 预估轮训结束
  const [preViewCallback, setPreViewCallback] = useState(false);
  // 接口轮训次数
  const [pollingCount, setPollingCount] = useState(0);
  // 是否开始执行创建逻辑
  const [isStartCreate, setIsStartCreate] = useState(false);
  const [loading, setLoading] = useState(false);
  const [loadingCreate, setLoadingCreate] = useState(false);
  const maxDate = dayjs().add(30, 'day').endOf('day');
  // 人群有效期开关
  const crowdValidityStatus =
    window.$BLMReleaseCenter.getSceneSwitch('crowdValidity');
  // 账号状态
  const accountStatusList = [
    { label: '未激活', value: '2' },
    { label: '正常', value: '3' },
    { label: '停用', value: '4' },
    { label: '封号', value: '10' },
  ];
  const statusEnum = {
    AND: '且',
    OR: '或',
    MINUS: '排除',
  };
  const operatorEnum = {
    AND: '同时满足以下特征司机',
    OR: '满足以下任一特征司机',
  };
  const crowdRuleScopeEnum = [
    { label: '城市', value: 1 },
    { label: '运力公司', value: 2 },
    { label: '车队', value: 3 },
  ];

  // 拼接树结构
  const getOptionsTree = (cityVal, carTeamVal, fleetVal) => {
    let info = [];
    // 1.将选中的城市按特定结构放入info中
    cityVal?.forEach((item, index) => {
      info.push({
        adcode: item.code ?? null,
        adcodeName: item.name ?? '',
        transportCompanyList: [],
      });
    });
    // 2.将选中的运力公司数据放入到对应的城市下
    carTeamVal?.forEach((item) => {
      const x = info?.map((i) => i.adcode)?.indexOf(item?.adcode);
      // 运力公司对应的车队存在，在对应的城市下添加运力公司数据
      if (x > -1) {
        info[x].transportCompanyList.push({
          transportCompanyId: item?.transportCompanyId ?? null,
          transportCompanyName: item?.transportCompanyName ?? '',
          fleetList: [],
        });
      } else {
        // 运力公司对应的车队不存在 将运力公司连同城市一起新增
        info.push({
          adcode: item?.adcode ?? null,
          adcodeName: item?.cityName ?? '',
          transportCompanyList: [
            {
              transportCompanyId: item?.transportCompanyId ?? null,
              transportCompanyName: item?.transportCompanyName ?? '',
              fleetList: [],
            },
          ],
        });
      }
    });
    // 3.将选中的车队放入对应的运力公司下
    fleetVal.forEach((item) => {
      const index = info.map((i) => i.adcode)?.indexOf(item?.adcode);
      // 车队所在城市存在
      if (index > -1) {
        // 判断车队所在运力公司是否存在
        const index2 = info[index]?.transportCompanyList
          ?.map((a) => a.transportCompanyId)
          ?.indexOf(item?.transportCompanyId);
        if (index2 > -1) {
          info[index]?.transportCompanyList[index2].fleetList.push({
            fleetId: item?.fleetId ?? null,
            fleetName: item?.fleetName ?? '',
          });
        } else {
          // 车队所在运力公司不存在， 将车队&运力公司一起新增到对应的城市下
          info[index]?.transportCompanyList.push({
            transportCompanyId: item?.transportCompanyId ?? null,
            transportCompanyName: item?.transportCompanyName ?? '',
            fleetList: [
              {
                fleetId: item?.fleetId ?? null,
                fleetName: item?.fleetName ?? '',
              },
            ],
          });
        }
      } else {
        // 车队所在城市不存在，将车队&运力公司&城市一起新增
        info.push({
          adcode: item?.adcode ?? null,
          adcodeName: item?.cityName ?? '',
          transportCompanyList: [
            {
              transportCompanyId: item?.transportCompanyId ?? null,
              transportCompanyName: item?.transportCompanyName ?? '',
              fleetList: [
                {
                  fleetId: item?.fleetId ?? null,
                  fleetName: item?.fleetName ?? '',
                },
              ],
            },
          ],
        });
      }
    });
    setAdcodeThreeLevelList(info);
    return info;
  };

  const validateInput = (rule, value, callback) => {
    // 使用正则表达式校验输入
    const isValid = /^[a-zA-Z0-9\u4e00-\u9fa5]+$/.test(value);

    if (value && !isValid) {
      callback('只能输入字母、数字或汉字');
    } else {
      callback();
    }
  };
  // 圈选粒度变更
  const crowdRuleScopeChange = (e) => {
    const val = e?.target?.value ?? 1;
    setCrowdRuleScopeData(val);
    // 粒度变更 清空城市数据
    form.setFieldValue('registerCityCode', []);
    setCityNameList([]);
    setDictValueList([]);
    setRegisterCityCode([]);
    // 清除运力公司 & 车队数据
    form.setFieldValue('taskCarTeamId', []);
    setTaskCarTeamIdList([]);
    setCarTeamSelectedOptions([]);
    setRegisterCarTeamCode([]);
    form.setFieldValue('fleetIds', []);
    setFleetNameList([]);
    setFleetIdOptions([]);
    setFleetSelectedOptions([]);
    setRregisterFleetCode([]);
  };
  const handleFormChange = (field, value) => {
    setRegisterCityCode(value);
    // 根据城市计算符合要求的运力公司
    const newCarTeamvalue: any =
      getCurByDep(
        {
          key: 'transportCompanyId',
          value: registerCarTeamCode,
          options: allCarTeamList,
          multiple: true,
        },
        {
          key: 'adcode',
          value: value,
          multiple: true,
        },
      ) ?? [];
    form.setFieldValue('taskCarTeamId', newCarTeamvalue);
    setRegisterCarTeamCode(newCarTeamvalue);
    // 根据运力公司计算符合要求的车队
    const newFleetValue: any =
      getCurByDep(
        {
          key: 'fleetId',
          value: registerFleetCode,
          options: fleetIdOptions,
          multiple: true,
        },
        {
          key: 'transportCompanyId',
          value: newCarTeamvalue,
          multiple: true,
        },
      ) ?? [];
    form.setFieldValue('fleetIds', newFleetValue);
    setRregisterFleetCode(newFleetValue);
  };
  // 组织模型-运力公司组件筛选项变更
  const catTeamChange = (val) => {
    setRegisterCarTeamCode(val);
    // 运力公司变更，筛选出选中运力公司中对应的车队id
    const newFleetValue: any =
      getCurByDep(
        {
          key: 'fleetId',
          value: registerFleetCode,
          options: fleetIdOptions,
          multiple: true,
        },
        {
          key: 'transportCompanyId',
          value: val,
          multiple: true,
        },
      ) ?? [];
    setRregisterFleetCode(newFleetValue);
    form.setFieldValue('fleetIds', newFleetValue);
  };

  const fleetChange = (val) => {
    setRregisterFleetCode(val);
  };
  useEffect(() => {
    // 当前城市code集合
    const dictValueList = allCityList?.map((item) => item?.code);
    // 当前城市名称集合
    const dictDescList = allCityList?.map((item) => item?.name);
    setDictValueList(dictValueList);
    setDictDescList(dictDescList);
  }, [allCityList]);
  const operationPoolItemsMenu = [
    {
      label: '添加交集标签组合池',
      key: 'AND',
      icon: (
        <BLMIconFont
          style={{ color: '#366CFE', fontSize: 16 }}
          type="BLM-ywIc-intersection"
        />
      ),
    },
    {
      label: '添加并集标签组合池',
      key: 'OR',
      icon: (
        <BLMIconFont
          style={{ color: '#366CFE', fontSize: 16 }}
          type="BLM-ywIc-union"
        />
      ),
    },
    {
      label: '添加差集标签组合池',
      key: 'MINUS',
      icon: (
        <BLMIconFont
          style={{ color: '#366CFE', fontSize: 16 }}
          type="BLM-ywIc-differenceSet-o"
        />
      ),
    },
  ];
  const handleOperationPoolClick = (e, add, fields) => {
    blmAnalysisModuleClick({
      eventId: 'e_leopard_cp_click_00001812',
      pageId: 'p_leopard_cp_00000402',
    });
    add();
    // 维护当前元算池和标签关系，用于渲染
    setOperationPoolChange([
      ...operationPoolChange,
      {
        key: operationPoolChange[operationPoolChange.length - 1].key + 1,
        tagList: [],
        isDeleted: false,
        status: e.key,
        operator: 'AND',
      },
    ]);
    // 表单项添加对应item
    form.setFieldValue(
      [
        'containerList',
        form.getFieldValue('containerList').length - 1,
        'status',
      ],
      e.key,
    );
  };
  // 当前已有运算池，维护到最外层，与表格操作列联动
  useEffect(() => {
    const calculateMaxHeight = () => {
      // const windowHeight = document.querySelector('#app')?.clientHeight ?? 0;
      const topFixedHeight =
        document.querySelector('.basicInformation')?.clientHeight ?? 0;
      const bottomFixedHeight =
        document.querySelector('.informationFooter')?.clientHeight ?? 0;
      const basicFormElement = document.querySelector('.basicInformationForm');
      const observer = new ResizeObserver(() => {
        const basicFixedHeight = basicFormElement?.clientHeight ?? 0;
        // const dynamicFormMaxHeight = windowHeight - topFixedHeight - bottomFixedHeight - basicFixedHeight - 48;
        const dynamicFormMaxHeight =
          topFixedHeight + bottomFixedHeight + basicFixedHeight;
        setMaxHeight(dynamicFormMaxHeight);
      });

      if (basicFormElement) {
        observer.observe(basicFormElement);
      }

      return () => {
        observer.disconnect(); // 清除观察器
      };
    };
    // 初始计算
    calculateMaxHeight();
    // 监听窗口大小变化
    window.addEventListener('resize', calculateMaxHeight);
    return () => {
      // 清除事件监听器
      window.removeEventListener('resize', calculateMaxHeight);
    };
  }, []); // 空数组确保只在组件挂载和卸载时执行

  useImperativeHandle(ref, () => ({
    // 返回需要暴露给父组件的方法或属性
    getFormInstance: () => formRef?.current,
    // 可以添加其他方法或属性
  }));
  const generateOperationResult = () => {
    const { activeStatus, taskCarTeamId } = form.getFieldsValue();
    const activeStatusStr = accountStatusList
      .filter((item) => activeStatus?.includes(item.value))
      .map((item) => item.label);
    let commonStr = '';
    let commonStrShow = '';
    let operationItemStr = '';
    let commonList = [];
    let commonListShow = [];
    let commonListOther = [];

    if (cityNameList.length) {
      commonList.push(`城市范围：${cityNameList.join(' ')}`);
      if (cityNameList.length > 2) {
        const cityNameListNew = cityNameList.slice(0, 2).join(' ');
        commonListShow.push(`城市范围：${cityNameListNew}...`);
      } else {
        commonListShow.push(`城市范围：${cityNameList.join(' ')}`);
      }
    }
    // 运力公司有值
    if (
      taskCarTeamIdList.length &&
      taskCarTeamIdList?.[0] !== '' &&
      cityNameList?.length &&
      [2, 3].includes(crowdRuleScopeData)
    ) {
      commonList.push(`运力公司：${taskCarTeamIdList.join(' ')}`);
      if (taskCarTeamIdList.length > 2) {
        const taskCarTeamIdListNew = taskCarTeamIdList.slice(0, 2).join(' ');
        commonListShow.push(`运力公司：${taskCarTeamIdListNew}...`);
      } else {
        commonListShow.push(`运力公司：${taskCarTeamIdList.join(' ')}`);
      }
      // 兼容复制链路运力公司没有名称场景
    } else if (
      copyBaseInformation &&
      taskCarTeamId?.[0] !== 'ALL' &&
      taskCarTeamId?.length &&
      cityNameList?.length &&
      taskCarTeamIdList?.[0] === '' &&
      [2, 3].includes(crowdRuleScopeData)
    ) {
      let copyTaskCarTeamIdList = [];
      allCarTeamList.map((item) => {
        if (taskCarTeamId.includes(item.dictValue)) {
          copyTaskCarTeamIdList.push(item.dictDesc);
        }
      });
      commonList.push(`运力公司：${copyTaskCarTeamIdList.join(' ')}`);
      if (copyTaskCarTeamIdList.length > 2) {
        const copyTaskCarTeamIdListNew = copyTaskCarTeamIdList
          .slice(0, 2)
          .join(' ');
        commonListShow.push(`运力公司：${copyTaskCarTeamIdListNew}...`);
      } else {
        commonListShow.push(`运力公司：${copyTaskCarTeamIdList.join(' ')}`);
      }
    } else if ([2, 3].includes(crowdRuleScopeData)) {
      commonList.push('运力公司：不限');
      commonListShow.push('运力公司：不限');
    }
    // 车队字段文案拼接
    if (fleetNameList?.length && crowdRuleScopeData === 3) {
      commonList.push(`车队：${fleetNameList.join(' ')}`);
      if (fleetNameList.length > 2) {
        const taskCarTeamIdListNew = fleetNameList.slice(0, 2).join(' ');
        commonListShow.push(`车队：${taskCarTeamIdListNew}...`);
      } else {
        commonListShow.push(`车队：${fleetNameList.join(' ')}`);
      }
    } else if (crowdRuleScopeData === 3) {
      commonList.push('车队：不限');
      commonListShow.push('车队：不限');
    }
    if (activeStatusStr.length) {
      commonList.push(`账号状态：${activeStatusStr.join(' ')}`);
      commonListShow.push(`账号状态：${activeStatusStr.join(' ')}`);
      // 校验展示用
      commonListOther.push(`账号状态：${activeStatusStr.join(' ')}`);
    }
    commonStr = commonList && commonList.join('<br/>');
    commonStrShow = commonListShow && commonListShow.join('<br/>');
    const curOperationPoolItem = operationPoolChange.filter(
      (item) => item.isDeleted === false,
    );
    operationItemStr = curOperationPoolItem.map((item, index) => {
      // 提取tagList中的formValueStr字段
      let formValueArray = item.tagList
        .filter(
          (item) => item.isTagDelete === false && item.copyErrorTag === false,
        )
        .map((tag) => tag?.formValueStr);
      // 将字段用'且'连接成字符串
      // return formValueArray.join('且');
      if (formValueArray.length) {
        if (index === 0) {
          // return `${formValueArray.join(operatorEnum[item.operator])}`
          return `${operatorEnum[item.operator]}<br/>${formValueArray.join(
            '<br/>',
          )}`;
        } else {
          // return `${statusEnum[item.status]}<br/>${formValueArray.join(operatorEnum[item.operator])}`;
          return `${statusEnum[item.status]}${
            operatorEnum[item.operator]
          }<br/>${formValueArray.join('<br/>')}`;
        }
      }
    });
    let finallyStr = '';
    let finallyStrShow = '';
    let otherStrShow = '';
    let operationItemStrFilter = operationItemStr?.filter((item) => item);
    if (operationItemStrFilter?.length) {
      finallyStr = `${commonStr}<br/>在以上范围内<br/>${operationItemStrFilter.join(
        '<br/>',
      )}`;
      finallyStrShow = `${commonStrShow}<br/>在以上范围内<br/>${operationItemStrFilter.join(
        '<br/>',
      )}`;
      otherStrShow = `在以上范围内<br/>${operationItemStrFilter.join('<br/>')}`;
    } else {
      finallyStr = `${commonStr}`;
      finallyStrShow = `${commonStrShow}`;
    }
    setActiveStatusName(activeStatusStr);
    setOperationResult(finallyStr);
    setOperationResultShow(finallyStrShow);
    seOperationOtherStrShow(otherStrShow);
    return {
      finallyStr,
      finallyStrShow,
      otherStrShow,
    };
  };
  useEffect(() => {
    generateOperationResult();
  }, [
    operationPoolChange,
    cityNameList,
    taskCarTeamIdList,
    activeStatusValue,
    allCarTeamList,
    fleetNameList,
  ]);
  // 创建人群
  const handleCreateCrowd = () => {
    blmAnalysisModuleClick({
      pageId: 'p_leopard_cp_00000402',
      eventId: 'e_leopard_cp_click_00001806',
    });
    form.validateFields().then((fieldsValue) => {
      const currOperationPoolChange = operationPoolChange.filter(
        (item) =>
          item.isDeleted === false &&
          item.tagList.filter((item) => item.isTagDelete === false).length > 0,
      );
      const currEmptyOperationPool = operationPoolChange.filter(
        (item) =>
          item.isDeleted === false &&
          item.tagList.filter((item) => item.isTagDelete === false).length ===
            0,
      );
      // const errTagCopy = currOperationPoolChange.map(item => item.tagList.filter(item => item.copyErrorTag === true))
      const errTagCopy = currOperationPoolChange.some((item) =>
        item.tagList.some(
          (tag) => tag.copyErrorTag === true && tag.isTagDelete === false,
        ),
      );
      if (
        !errTagCopy &&
        (currEmptyOperationPool?.length === 0 ||
          currOperationPoolChange?.length === 0)
      ) {
        setPreCalibrationOpen(false);
        setCreateModal(true);
      } else if (errTagCopy) {
        message.error('请重新设置标签信息');
      } else {
        message.error(
          '运算池为空不能创建，请在运算池中添加标签或删除空运算池后，重新创建',
        );
      }
    });
  };
  const commonParams = () => {
    const { registerCityCode, activeStatus, taskCarTeamId } =
      form.getFieldsValue();

    let containerSetHandle;
    const currOperationPoolChange = operationPoolChange.filter(
      (item) =>
        item.isDeleted === false &&
        item.tagList.filter((item) => item.isTagDelete === false).length > 0,
    );
    const result = currOperationPoolChange
      .map((item, index) => {
        const currentStatus = index === 0 ? '' : item.status;
        if (index === 0) {
          return '';
        } else {
          return `${currentStatus}_${index + 1}`;
        }
      })
      .join('_');
    const curContainerList = currOperationPoolChange.map((item, index) => {
      const filterTagList = item.tagList
        .filter((item) => item.isTagDelete === false)
        ?.map((tagItem, index) => {
          const commonTagItemParams = {
            tagId: tagItem.tagId,
            latelType: tagItem.latelType,
            name: tagItem.name,
            datasourceId: tagItem.datasourceId,
            showType: tagItem.showType,
          };
          const dateTagItemParams = {
            valueType: tagItem.valueType,
            startDate: tagItem?.startDate,
            endDate: tagItem?.endDate,
            dateCancelType: tagItem?.dateCancelType,
            dyStartDateType: tagItem?.dyStartDateType,
            subStart: tagItem?.subStart,
            subEnd: tagItem?.subEnd,
          };
          if (tagItem.showType === 1) {
            return {
              ...commonTagItemParams,
              ...dateTagItemParams,
              scope: tagItem?.scope,
              calType: tagItem?.calType,
            };
          } else if ([2, 5, 9, 10, 13].includes(tagItem.showType)) {
            return {
              ...commonTagItemParams,
              startValue: tagItem.startValue,
              scope: tagItem.scope,
            };
            // 模版数值类型
          } else if (tagItem.showType === 3) {
            return {
              ...commonTagItemParams,
              ...dateTagItemParams,
              scope: tagItem?.scope,
              calType: tagItem?.calType,
              startValue: tagItem.startValue,
              endValue: tagItem?.endValue,
            };
          } else if (tagItem.showType === 4) {
            return {
              ...commonTagItemParams,
              scope: tagItem.scope,
              startValue: tagItem.startValue,
              endValue: tagItem?.endValue,
            };
          } else if (tagItem.showType === 11) {
            return {
              ...commonTagItemParams,
              ...dateTagItemParams,
              startValue: tagItem.startValue,
              scope: tagItem.scope,
              calType: tagItem.calType,
            };
          } else if ([6, 12, 14].includes(tagItem.showType)) {
            return {
              ...commonTagItemParams,
              scope: tagItem.scope,
            };
          }
        });
      return {
        seq: index + 1,
        tagList: filterTagList,
        containerType: 'RULE_CONTAINER',
        operator: item.operator,
      };
    });
    if (currOperationPoolChange.length) {
      containerSetHandle = {
        aop: `1${result}`,
        containerList: curContainerList,
      };
    }
    const commonParams = {
      platformID: 1,
      entityType: 1,
      businessType: 1,
      tagType: 2,
      generationType: 1,
      activeStatus: activeStatus.join(','),
      transAbilityCompanyId: taskCarTeamId
        ? taskCarTeamId.length === 0
          ? null
          : taskCarTeamId.join(',')
        : null,
      transAbilityCompanyName: taskCarTeamId
        ? taskCarTeamId.length === 0
          ? null
          : taskCarTeamIdList
        : null,
      cityLimit:
        registerCityCode[0] === 'ALL'
          ? dictValueList.join(',')
          : registerCityCode.join(','),
      cityLimitDesc: cityNameList,
      rulesDetail: generateOperationResult().finallyStr,
      containerSet: containerSetHandle,
    };
    return commonParams;
  };
  // 创建人群弹窗确认函数
  const handleCrowdModalOK = () => {
    createCrowdForm.validateFields().then((fieldsValue) => {
      const {
        availableRange,
        crowdTagName,
        tagChannel,
        desc,
        availableEnd = null,
      } = fieldsValue;
      setCreateModal(false);
      let createParams;
      if (crowdValidityStatus) {
        createParams = {
          ...commonParams(),
          availableRange,
          crowdTagName,
          tagChannel,
          desc,
          crowdRuleScope: crowdRuleScopeData,
          adcodeThreeLevelList: adcodeThreeLevelList,
          availableEnd: availableEnd?.valueOf(),
        };
      } else {
        createParams = {
          ...commonParams(),
          availableRange,
          crowdTagName,
          tagChannel,
          desc,
          crowdRuleScope: crowdRuleScopeData,
          adcodeThreeLevelList: adcodeThreeLevelList,
        };
      }
      createNewCrowd(createParams).then((res) => {
        if (res.code === 1) {
          window.location.href = '/bi/tag/driverManageNew';
          createCrowdForm.resetFields();
        } else {
          message.error('创建人群失败');
        }
        createCrowdForm.resetFields();
      });
    });
  };
  // loading只针对复制链路时城运车树结构未拼接完成导致传参错误
  useEffect(() => {
    if (copyBaseInformation) {
      setLoading(true);
      setLoadingCreate(true);
    }
  }, [copyBaseInformation]);
  useEffect(() => {
    if (copyBaseInformation) {
      let crowdRuleScopeCopy = copyBaseInformation?.crowdRuleScope;
      // 复制时缺少圈选力度参数
      if (!crowdRuleScopeCopy) {
        crowdRuleScopeCopy = 1;
        copyBaseInformation?.transAbilityCompanyId?.length &&
          (crowdRuleScopeCopy = 2);
        copyBaseInformation?.fleedIdList?.length && (crowdRuleScopeCopy = 3);
      }
      // 圈选力度回填
      setCrowdRuleScopeData(crowdRuleScopeCopy);
      setRegisterCityCode(
        copyBaseInformation?.cityLimit?.length &&
          !['ALL', 'all', 'All'].includes(copyBaseInformation.cityLimit)
          ? copyBaseInformation.cityLimit?.split(',')
          : [],
      );
      setRegisterCarTeamCode(
        copyBaseInformation?.transAbilityCompanyId &&
          !['ALL', 'all', 'All'].includes(copyBaseInformation.cityLimit)
          ? copyBaseInformation.transAbilityCompanyId
              ?.split(',')
              .map((item) => Number(item))
          : [],
      );
      setRregisterFleetCode(copyBaseInformation?.fleedIdList ?? []);
      form.setFieldsValue({
        crowdRuleScope: crowdRuleScopeCopy,
        registerCityCode:
          copyBaseInformation?.cityLimit?.length &&
          !['ALL', 'all', 'All'].includes(copyBaseInformation.cityLimit)
            ? copyBaseInformation.cityLimit?.split(',')
            : [],
        taskCarTeamId:
          copyBaseInformation?.transAbilityCompanyId &&
          !['ALL', 'all', 'All'].includes(copyBaseInformation?.cityLimit)
            ? copyBaseInformation.transAbilityCompanyId
                ?.split(',')
                .map((item) => Number(item))
            : [],
        fleetIds: copyBaseInformation?.fleedIdList ?? [],
        activeStatus: copyBaseInformation?.activeStatus?.length
          ? copyBaseInformation.activeStatus
              ?.split(',')
              ?.filter((item) => item !== '999')
          : [],
        containerList: copyOperationPoolChange?.map((item, index) => {
          return {
            operator: item?.operator,
            status: item?.status,
          };
        }),
      });
    }
  }, [copyBaseInformation, copyOperationPoolChange]);

  const getCityOptions = (cityOptions) => {
    let list = [];
    if (Array.isArray(cityOptions)) {
      cityOptions.forEach((item) => {
        if (Array.isArray(item.childList)) {
          item.childList.forEach((i) => {
            list.push(i);
          });
        }
      });
    }

    setAllCityList(list);
  };
  // 关闭城市批量添加弹窗
  const cityHandleOk = (item) => {
    if (item) {
      let checkList = [];
      const { cityCodes } = item;
      const { registerCityCode } = form.getFieldsValue();
      if (registerCityCode) {
        // 将批量添加的数据和已选项的数据进行合并
        // checkList = registerCityCode.concat(cityCodes)
        checkList = [...new Set([...registerCityCode, ...cityCodes])];
      } else {
        checkList = cityCodes;
      }
      // 将批量添加的城市code赋值给城市选择框
      form.setFieldValue('registerCityCode', checkList);
      // 回填城市选项后更新运力公司下拉数据
      handleFormChange('adcode', checkList);
      form?.validateFields(['registerCityCode']);
    }
    setIsCityOpen(false);
  };
  // 关闭运力公司批量添加弹窗
  const carTeamHandleOk = (item) => {
    if (item) {
      let checkList = [];
      const { carTeamValue } = item;
      const { taskCarTeamId } = form.getFieldsValue();
      if (taskCarTeamId) {
        // 将批量添加的数据和已选项的数据进行合并
        checkList = [...new Set([...taskCarTeamId, ...carTeamValue])];
      } else {
        checkList = carTeamValue;
      }
      // 将批量添加的运力公司name赋值给运力公司选择框
      form.setFieldValue('taskCarTeamId', checkList);
      // 回填运力公司选项后更新车队下拉数据
      catTeamChange(checkList);
      form?.validateFields(['taskCarTeamId']);
    }
    setIsCarTeamOpen(false);
  };

  // 获取预估Id
  const getHandleEstimatedNum = (val) => {
    const { registerCityCode, activeStatus, taskCarTeamId } =
      form.getFieldsValue();
    const commonParams = {
      platformID: 1,
      entityType: 1,
      businessType: 1,
      tagType: 2,
      generationType: 1,
      activeStatus: activeStatus?.join(',') ?? '',
      transAbilityCompanyId: taskCarTeamId
        ? taskCarTeamId?.length === 0
          ? null
          : taskCarTeamId?.join(',')
        : null,
      transAbilityCompanyName:
        taskCarTeamId && taskCarTeamId?.length === 0 ? null : taskCarTeamIdList,
      cityLimit: registerCityCode?.join(',') ?? '',
      cityLimitDesc: cityNameList,
      rulesDetail: generateOperationResult().finallyStr,
      containerSet: val,
      adcodeThreeLevelList: adcodeThreeLevelList,
    };
    previewNewCrowd(commonParams).then((res) => {
      if (res?.code === 1 && res.data) {
        setPreViewStatus(res.data);
        setPreViewCallback(true);
      }
    });
  };
  // 关闭圈选结果弹窗，更新城运车数据
  const deleteBackChange = (info, type) => {
    // 获取到合规的数据更新城/运/车数据
    const { hasPermissionCheckVO, noPermissionCheckVO } = info;
    const { adCodeListVOS, transportCompanyListVOS, fleetListVOS } =
      hasPermissionCheckVO;
    const cityVOS = noPermissionCheckVO?.adCodeListVOS ?? [];
    const carTeamVOS = noPermissionCheckVO?.transportCompanyListVOS ?? [];
    const fleetVOS = noPermissionCheckVO?.fleetListVOS ?? [];
    // 存在不合规的数据，更新层级结构
    if (cityVOS?.length || carTeamVOS?.length || fleetVOS?.length) {
      if (type) {
        // 预估人数
        setIsStart(true);
      } else {
        // 创建链路
        setIsStartCreate(true);
      }
      if (cityRef?.current) {
        // 城市
        const adcodeIdList = adCodeListVOS?.map((i) => i?.adcode);
        const adcodeNameList = adCodeListVOS?.map((i) => i?.cityName);
        form.setFieldValue('registerCityCode', adcodeIdList);
        setRegisterCityCode(adcodeIdList);
        setCityNameList(adcodeNameList);
      }
      if (carTeamRef?.current) {
        // 运力公司 transportCompanyId  transportCompanyName
        const transportCompanyIdList = transportCompanyListVOS?.map(
          (i) => i?.transportCompanyId,
        );
        form.setFieldValue('taskCarTeamId', transportCompanyIdList);
        setRegisterCarTeamCode(transportCompanyIdList);
      }
      if (fleetRef?.current) {
        // 车队 fleetId fleetName
        const fleetIdList = fleetListVOS?.map((i) => i?.fleetId);
        form.setFieldValue('fleetIds', fleetIdList);
        setRregisterFleetCode(fleetIdList);
      }
      // 更新
      setPreCalibrationData(info);
    } else {
      // 数据合规，直接走预估/创建，不需要更新层级结构
      // 先判断一下层级结构是否正确
      if (adcodeThreeLevelList?.length === registerCityCode?.length) {
        setLoading(false);
        setLoadingCreate(false);
        if (type) {
          // 开始预估
          getHandleEstimatedNum(containerSetHandleData);
        } else {
          // 创建链路
          handleCreateCrowd();
        }
      }
    }
  };
  // 点击预估司机数/创建人群
  const handleEstimatedNum = (val: boolean) => {
    // val= true: 点击预估进入；  val=false: 创建
    form.validateFields().then((fieldsValue) => {
      // 复制场景下存在标签不兼容场景，必须重新设置才能预估或创建人群
      // 当前有标签的运算池
      const currOperationPoolChange = operationPoolChange.filter(
        (item) =>
          item.isDeleted === false &&
          item.tagList.filter((item) => item.isTagDelete === false).length > 0,
      );
      // 判断当前是否有空运算池
      const currEmptyOperationPool = operationPoolChange.filter(
        (item) =>
          item.isDeleted === false &&
          item.tagList.filter((item) => item.isTagDelete === false).length ===
            0,
      );
      const errTagCopy = currOperationPoolChange.some((item) =>
        item.tagList.some(
          (tag) => tag.copyErrorTag === true && tag.isTagDelete === false,
        ),
      );
      // 运算池内容为空不允许预估或创建人群
      if (
        !errTagCopy &&
        (currEmptyOperationPool?.length === 0 ||
          currOperationPoolChange?.length === 0)
      ) {
        let containerSetHandle;
        const result = currOperationPoolChange
          .map((item, index) => {
            const currentStatus = index === 0 ? '' : item.status;
            if (index === 0) {
              return '';
            } else {
              return `${currentStatus}_${index + 1}`;
            }
          })
          .join('_');
        // 标签参数过滤
        const curContainerList = currOperationPoolChange.map((item, index) => {
          const filterTagList = item.tagList
            .filter((item) => item.isTagDelete === false)
            ?.map((tagItem, index) => {
              const commonTagItemParams = {
                tagId: tagItem.tagId,
                latelType: tagItem.latelType,
                name: tagItem.name,
                datasourceId: tagItem.datasourceId,
                showType: tagItem.showType,
              };
              const dateTagItemParams = {
                valueType: tagItem.valueType,
                startDate: tagItem?.startDate,
                endDate: tagItem?.endDate,
                dateCancelType: tagItem?.dateCancelType,
                dyStartDateType: tagItem?.dyStartDateType,
                subStart: tagItem?.subStart,
                subEnd: tagItem?.subEnd,
              };
              if (tagItem.showType === 1) {
                return {
                  ...commonTagItemParams,
                  ...dateTagItemParams,
                  scope: tagItem?.scope,
                  calType: tagItem?.calType,
                };
              } else if ([2, 5, 9, 10, 13].includes(tagItem.showType)) {
                return {
                  ...commonTagItemParams,
                  startValue: tagItem.startValue,
                  scope: tagItem.scope,
                };
                // 模版数值类型
              } else if (tagItem.showType === 3) {
                return {
                  ...commonTagItemParams,
                  ...dateTagItemParams,
                  scope: tagItem?.scope,
                  calType: tagItem?.calType,
                  startValue: tagItem.startValue,
                  endValue: tagItem?.endValue,
                };
              } else if (tagItem.showType === 4) {
                return {
                  ...commonTagItemParams,
                  scope: tagItem.scope,
                  startValue: tagItem.startValue,
                  endValue: tagItem?.endValue,
                };
              } else if (tagItem.showType === 11) {
                return {
                  ...commonTagItemParams,
                  ...dateTagItemParams,
                  startValue: tagItem.startValue,
                  scope: tagItem.scope,
                  calType: tagItem.calType,
                };
              } else if ([6, 12, 14].includes(tagItem.showType)) {
                return {
                  ...commonTagItemParams,
                  scope: tagItem.scope,
                };
              }
            });
          return {
            seq: index + 1,
            tagList: filterTagList,
            containerType: 'RULE_CONTAINER',
            operator: item.operator,
          };
        });
        if (currOperationPoolChange.length) {
          containerSetHandle = {
            aop: `1${result}`,
            containerList: curContainerList,
          };
        }
        setContainerSetHandleData(containerSetHandle);

        setTimeout(() => {
          if (val) {
            setLoading(true);
          } else {
            setLoadingCreate(true);
          }
        }, 0);
        previewNewCrowdPower({ adcodeThreeLevelList })
          .then((res) => {
            setLoading(false);
            setLoadingCreate(false);
            if (res?.code === 1 && res?.data) {
              setPreCalibrationData(res.data);
              setIsCheck(val);
              if (val) {
                // 打开弹窗
                setPreCalibrationOpen(true);
              } else {
                // 创建的时候判断 校验是否通过
                const { adCodeListVOS, transportCompanyListVOS, fleetListVOS } =
                  res.data?.noPermissionCheckVO;
                if (
                  adCodeListVOS?.length ||
                  transportCompanyListVOS?.length ||
                  fleetListVOS.length
                ) {
                  // 存在无权限的数据 打开弹窗
                  setPreCalibrationOpen(true);
                } else {
                  // 不存在无权限的数据， 直接执行创建
                  handleCreateCrowd();
                }
              }
            }
          })
          .catch((e) => {
            setLoading(false);
            setLoadingCreate(false);
          });
      } else if (errTagCopy) {
        message.error('请重新设置标签信息');
        setLoading(false);
        setLoadingCreate(false);
      } else {
        message.error(
          '运算池为空不能预估，请在运算池中添加标签或删除空运算池后，重新创建',
        );
        setLoading(false);
        setLoadingCreate(false);
      }
    });
  };

  // 有效期时间限制
  const disabledDate = (current: any) => {
    return (
      current &&
      (current < dayjs().endOf('day') ||
        current > dayjs().add(30, 'day').endOf('day'))
    );
  };

  useEffect(() => {
    setTimeout(() => {
      if (cityRef?.current) {
        // 获取选中的城市数据
        const selectedOptions = cityRef.current?.getSelectedOptions() ?? [];
        setCitySelectedOptions(selectedOptions);
        // 获取选中的城市名称
        const selectedLabelName = cityRef.current?.getSelectedLabelName() ?? [];
        setCityNameList(selectedLabelName);
      }
      if (carTeamRef.current) {
        // 获取选中的运力公司所有数据
        const selectedOptions = carTeamRef?.current?.getSelectedOptions() ?? [];
        setCarTeamSelectedOptions(selectedOptions);
        // 获取选中的运力公司名称
        const selectedLabelName =
          carTeamRef?.current?.getSelectedLabelName() ?? [];
        setTaskCarTeamIdList(selectedLabelName);
      }
      // 解决Ref.current获取车队数据错误问题暂定的短解，等组件库修改可删除setTimeout逻辑。
      setTimeout(() => {
        if (fleetRef.current) {
          // 获取车队选中的数据名称，用于标签信息展示
          const selectedLabelName =
            fleetRef.current.getSelectedLabelName() ?? [];
          setFleetNameList(selectedLabelName);
          // 获取选中的车队完整信息，用于给服务拼接层级结构
          const selectedOptions = fleetRef.current.getSelectedOptions() ?? [];
          setFleetSelectedOptions(selectedOptions);
        }
      }, 0);
    }, 0);
  }, [
    Form.useWatch('registerCityCode', form),
    Form.useWatch('taskCarTeamId', form),
    Form.useWatch('fleetIds', form),
    JSON.stringify(allCityList),
    JSON.stringify(allCarTeamList),
    JSON.stringify(fleetIdOptions),
  ]);
  // 城市&运力公司&车队选中数据变更时执行
  useEffect(() => {
    const treeInfo = getOptionsTree(
      citySelectedOptions,
      carTeamSelectedOptions,
      fleetSelectedOptions,
    );
    console.log('🌈🌲结构', treeInfo);
  }, [citySelectedOptions, carTeamSelectedOptions, fleetSelectedOptions]);

  // 开始执行预估逻辑
  useEffect(() => {
    // 先判断一下层级结构是否正确
    if (adcodeThreeLevelList?.length === registerCityCode?.length) {
      setLoading(false);
      setLoadingCreate(false);
      if (isStart) {
        // 开始预估
        getHandleEstimatedNum(containerSetHandleData);
        setIsStart(false);
      }
      // 执行创建逻辑
      if (isStartCreate) {
        handleCreateCrowd();
        setIsStartCreate(false);
      }
    }
  }, [adcodeThreeLevelList, containerSetHandleData]);
  // 获取预估司机数逻辑
  useEffect(() => {
    const timer = setInterval(() => {
      previewCallback({ unionId: preViewStatus })
        .then((res) => {
          if (res.code === 1 && res.data && res.data.count !== '-999') {
            setPreViewStatusNumber(res.data?.count);
            setPreViewCallback(false);
          } else if (res.code !== 1) {
            setPreViewCallback(false);
          }
        })
        .catch(() => {
          setPreViewCallback(false);
        });
      setPollingCount((prevCount) => prevCount + 1);
    }, 1000);
    if (!preViewCallback) {
      clearInterval(timer);
      setPollingCount(0);
    }
    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [preViewStatus, preViewCallback]);
  // 预估轮询
  useEffect(() => {
    if (pollingCount >= 50) {
      setPreViewCallback(false); // 停止轮询
      message.error('暂未请求到结果，您可以重新点击预估或直接创建人群。');
      return; // 不再设置定时器
    }
    // 达到阀值
    if (pollingCount === 20) {
      message.error('您可以继续等待，也可以直接创建人群。');
    }
  }, [pollingCount]);
  // 基础信息或运算池有变动，带动预估司机人数变动
  useEffect(() => {
    setPreViewCallback(false);
    setPollingCount(0);
    setPreViewStatus(false);
    setPreViewStatusNumber('--');
  }, [
    operationPoolChange,
    registerCityCode,
    crowdRuleScopeData,
    form.getFieldValue('activeStatus'),
    form.getFieldValue('taskCarTeamId'),
    form.getFieldValue('fleetIds'),
  ]);

  return (
    <div className={styles['userPortraitNew']}>
      <div style={{ padding: '16px 0 0 16px', flexGrow: 1 }}>
        <div
          className="basicInformation"
          style={{ fontSize: 14, fontWeight: 500 }}
        >
          基础信息
        </div>
        <Form
          form={form}
          ref={formRef}
          colon={false}
          id="kf-form"
          initialValues={{
            crowdRuleScope: 1,
            activeStatus: ['3'],
            taskCarTeamId: [],
            containerList: [
              {
                operator: 'AND',
                status: 'AND',
              },
            ],
          }}
        >
          <div className="basicInformationForm">
            <Form.Item
              name="crowdRuleScope"
              label="圈选粒度"
              rules={[{ required: true, message: '请选择圈选粒度' }]}
              style={{ marginBottom: 8 }}
            >
              <Radio.Group
                options={crowdRuleScopeEnum}
                onChange={(value) => crowdRuleScopeChange(value)}
              />
            </Form.Item>
            <Row justify="space-between">
              <Col>
                <Form.Item
                  name="registerCityCode"
                  label="城市范围"
                  required
                  style={{ margin: '8px 0' }}
                  rules={[{ required: true, message: '请选择司机注册城市' }]}
                >
                  <BLMOrgBtCity
                    key={'city1'}
                    style={{ width: 228 }}
                    ref={cityRef}
                    multiple
                    addonbefore={''}
                    changeOnMultiple
                    showAll={true}
                    placeholder="请选择司机注册城市"
                    structureType={2} // 省市级联
                    kind={'authOpen'}
                    onChange={(value) => {
                      value = value ?? [];
                      handleFormChange('adcode', value);
                    }}
                    getOptions={(cityOptions) => getCityOptions(cityOptions)}
                  ></BLMOrgBtCity>
                </Form.Item>
              </Col>
              <Col>
                <Form.Item style={{ margin: '8px 0' }}>
                  <Button
                    type="link"
                    notspacelinkbtn
                    style={{ marginRight: 16 }}
                    onClick={() => {
                      setIsCityOpen(true);
                    }}
                  >
                    批量添加
                  </Button>
                </Form.Item>
              </Col>
            </Row>
            {[2, 3].includes(crowdRuleScopeData) ? (
              <Row justify="space-between">
                <Col>
                  <Form.Item
                    name="taskCarTeamId"
                    label="运力公司"
                    style={{ margin: '8px 0' }}
                    rules={[{ required: true, message: '请选择运力公司' }]}
                  >
                    <BLMOrgBtCarTeam
                      key={'carTeam1'}
                      ref={carTeamRef}
                      style={{ width: 228 }}
                      multiple
                      addonbefore={''}
                      adcodeList={registerCityCode}
                      placeholder={'请选择运力公司'}
                      disabled={!registerCityCode?.length}
                      authType={2}
                      changeOnMultiple
                      showAll={true}
                      onChange={(val, options) => {
                        val = val ?? [];
                        catTeamChange(val);
                      }}
                      getOptions={(val) => {
                        setAllCarTeamList(val);
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col>
                  <Form.Item style={{ margin: '8px 0' }}>
                    <Button
                      type="link"
                      notspacelinkbtn
                      style={{ marginRight: 16 }}
                      disabled={!registerCityCode?.length}
                      onClick={() => setIsCarTeamOpen(true)}
                    >
                      批量添加
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            ) : null}

            {crowdRuleScopeData === 3 ? (
              <Form.Item
                name="fleetIds"
                label="车队"
                labelAlign={'right'}
                style={{ margin: '0 16px 8px 28px' }}
                rules={[{ required: true, message: '请选择车队' }]}
              >
                <BLMFleetSelect
                  style={{ width: 288 }}
                  ref={fleetRef}
                  multiple
                  addonbefore={''}
                  authType={2}
                  changeOnMultiple
                  showAll={true}
                  placeholder={'请选择车队'}
                  adcodeList={registerCityCode}
                  transportCompanyIdList={registerCarTeamCode}
                  disabled={
                    !(registerCityCode?.length && registerCarTeamCode?.length)
                  }
                  onChange={(val) => fleetChange(val)}
                  getOptions={(val) => {
                    setFleetIdOptions(val);
                  }}
                ></BLMFleetSelect>
              </Form.Item>
            ) : null}
            <Form.Item
              name="activeStatus"
              label="账号状态"
              rules={[{ required: true, message: '请选择账号状态' }]}
              style={{ marginBottom: 24 }}
            >
              <Checkbox.Group
                options={accountStatusList}
                onChange={(value) => setActiveStatus(value)}
              />
            </Form.Item>
          </div>
          <Form.List name="containerList">
            {(fields, { add, remove }, { errors }) => (
              <div
                style={{
                  overflow: 'auto',
                  maxHeight: `calc(100vh - ${maxHeight}px - 104px)`,
                }}
              >
                {fields.map((field, index) => (
                  <Form.Item required={false} key={field.key}>
                    <>
                      {field.name !== 0 && (
                        // 交并差集选择
                        <OperationPoolRelationDropDown
                          form={form}
                          fieldIndex={index}
                          field={field}
                          operationPoolChange={operationPoolChange}
                          setOperationPoolChange={setOperationPoolChange}
                        />
                      )}
                      {/* 运算池 */}
                      <OperationPoolItem
                        operationPoolItemForm={form}
                        field={field}
                        remove={remove}
                        isShowDelete={fields.length > 1}
                        operationPoolChange={operationPoolChange}
                        setOperationPoolChange={setOperationPoolChange}
                      />
                    </>
                  </Form.Item>
                ))}
                <Dropdown
                  menu={{
                    items: operationPoolItemsMenu,
                    onClick: (e) => handleOperationPoolClick(e, add, fields),
                  }}
                  trigger={['click']}
                  disabled={fields.length === 5}
                >
                  <Tooltip
                    title={fields.length === 5 ? '最多可添加5个标签组合池' : ''}
                  >
                    <Button
                      type="dashed"
                      style={{ marginBottom: 16 }}
                      disabled={fields.length === 5}
                      icon={<BLMIconFont type="BLM-ic-plus-o" />}
                    >
                      添加标签组合池
                    </Button>
                  </Tooltip>
                </Dropdown>
              </div>
            )}
          </Form.List>
        </Form>
      </div>
      <div className="informationFooter">
        <Divider style={{ margin: 0 }} />
        <div style={{ padding: 16 }}>
          <div className={styles['estimatedNum']}>
            {Number(preViewStatusNumber) >= 0 && (
              <div className={styles['estimatedNum-people']}>
                预估{preViewStatusNumber}人
              </div>
            )}
            <Spin
              indicator={<LoadingOutlined spin />}
              size="small"
              spinning={loading}
            >
              <div
                className={styles['estimatedNum-btn']}
                onClick={() => {
                  handleEstimatedNum(true);
                }}
              >
                <BLMIconFont
                  style={{ color: '#366CFE' }}
                  type="BLM-ic-loop-o"
                />
                点击预估 & 查看圈选范围
              </div>
            </Spin>
          </div>
          <div>
            <Button
              style={{ width: 176, marginRight: 16, display: 'inline-block' }}
              onClick={() => (window.location.href = '/bi/tag/driverManageNew')}
            >
              取消
            </Button>
            <div style={{ display: 'inline-block' }}>
              <Spin
                indicator={<LoadingOutlined spin />}
                size="small"
                spinning={loadingCreate}
              >
                <Button
                  style={{ width: 178 }}
                  type="primary"
                  onClick={() => {
                    handleEstimatedNum(false);
                  }}
                >
                  创建人群
                </Button>
              </Spin>
            </div>
          </div>
        </div>
      </div>
      <Modal
        title="创建人群"
        width={720}
        open={createModal}
        centered={true}
        onOk={() => handleCrowdModalOK()}
        onCancel={() => {
          setCreateModal(false);
          createCrowdForm.resetFields();
        }}
      >
        <Form
          form={createCrowdForm}
          name="createCrowd"
          labelCol={{ span: 5 }}
          wrapperCol={{ span: 19 }}
          labelAlign="right"
          initialValues={{
            availableRange: 0,
            availableEnd: crowdValidityStatus ? maxDate : null,
          }}
          autoComplete="off"
          style={{ padding: '16px 120px' }}
        >
          <Form.Item
            label="人群名称"
            name="crowdTagName"
            rules={[
              { required: true, message: '请输入人群名称' },
              { validator: validateInput },
            ]}
            extra="备注：长度1-20位字母、数字或者汉字"
          >
            <Input maxLength={20} placeholder="请输入人群名称" />
          </Form.Item>
          {crowdValidityStatus && (
            <Form.Item
              label="人群更新至"
              name="availableEnd"
              required
              rules={[
                ({}) => ({
                  validator(_, value) {
                    if (!value) {
                      return Promise.reject(new Error('请选择日期'));
                    }
                    if (
                      value < dayjs().endOf('day') ||
                      value > dayjs().add(30, 'day').endOf('day')
                    ) {
                      return Promise.reject(
                        new Error('人群更新截止日期必须大于当前日期'),
                      );
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
              extra="备注：创建成功后次日起，于每日凌晨更新"
            >
              <DatePicker
                defaultValue={maxDate}
                disabledDate={disabledDate}
                style={{ width: '100%' }}
              />
            </Form.Item>
          )}
          <Form.Item
            label="应用场景"
            name="tagChannel"
            rules={[{ required: true, message: '请选择应用场景' }]}
            extra="备注：请正确选择人群应用场景，避免后续无法正常应用"
          >
            <Select
              options={[
                { value: '04', label: '触达' },
                { value: '03', label: '营销' },
                { value: '06', label: '监管' },
                { value: '05', label: '其他' },
              ]}
            />
          </Form.Item>

          <Form.Item
            label="可用范围"
            name="availableRange"
            rules={[{ required: true, message: '请选择可用范围' }]}
          >
            <Radio.Group>
              <Radio value={0}>公开可用</Radio>
              <Radio value={1}>仅自己可用</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            label="描述"
            name="desc"
            extra="备注：人群备注不得超过30个字符"
          >
            <TextArea maxLength={30} />
          </Form.Item>
        </Form>
      </Modal>
      {isCityOpen ? (
        <CityBatchAdd
          isCityOpen={isCityOpen}
          allCityList={allCityList}
          cityCancel={(item) => cityHandleOk(item)}
        ></CityBatchAdd>
      ) : null}
      {isCarTeamOpen ? (
        <CarTeamBatchAdd
          isCarTeamOpen={isCarTeamOpen}
          selectAdcodeList={registerCityCode}
          carTeamCancel={(item) => carTeamHandleOk(item)}
        ></CarTeamBatchAdd>
      ) : null}

      {/*预校验页面*/}
      <PreCalibration
        key={'PreCalibration'}
        isCheck={isCheck}
        preCalibrationOpen={preCalibrationOpen}
        preViewStatusNumber={preViewStatusNumber}
        crowdRuleScopeData={crowdRuleScopeData}
        activeStatusName={activeStatusName}
        operationOtherStrShow={operationOtherStrShow}
        preCalibrationData={preCalibrationData}
        adcodeThreeLevelList={adcodeThreeLevelList}
        preViewCallback={preViewCallback}
        callBackChange={(isOkey, val, type) => {
          setPreCalibrationOpen(isOkey);
          if (isOkey) {
            deleteBackChange(val, type);
          }
        }}
      ></PreCalibration>
    </div>
  );
});
export default OperationPool;
