import request from '@/utils/request';

// 获取推荐人群包
export const pageQueryRecommendCrowdInfoList = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/admin/v1/weklin/crowdTags/crowd/Recommend/pageQueryRecommendCrowdInfoList',
    method: 'POST',
    data: params,
    bindRoutePK: ['tagCircleSelectionBI'],
  });
};

// 获取推荐人群包bos新接口
export const pageQueryRecommendCrowdInfoListNew = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/bos/admin/v1/ai/crowd-tag/recommend/pageQueryRecommendCrowdInfoList',
    method: 'POST',
    data: params,
    bindRoutePK: ['tagCircleSelectionBI'],
  });
};

// 获取热门/最新标签
export const getLabelStatisticalList = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/admin/v1/weklin/crowdTags/crowd/getLabelStatisticalList',
    method: 'POST',
    data: params,
  });
};

// 获取热门/最新标签bos新接口
export const getLabelStatisticalListNew = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/bos/admin/v1/ai/crowd-tag/label/getLabelStatisticalList',
    method: 'POST',
    data: params,
  });
};

// 获取圈选人群包
export const pageQueryCrowdList = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/admin/v1/weklin/crowdTags/crowd/pageQueryCrowdList',
    method: 'POST',
    data: params,
    bindRoutePK: ['tagCircleSelectionBI'],
  });
};

// 获取当前标签层级列表
export const querySubjectTree = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/admin/v1/weklin/crowdTags/full/subject/tree',
    method: 'POST',
    data: params,
    bindRoutePK: ['tagCircleSelectionBI'],
  });
};

// 获取当前标签层级列表
export const querySubjectTreeNew = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/bos/admin/v1/ai/crowd-tag/label/queryFullSubjectTree',
    method: 'POST',
    data: params,
    bindRoutePK: ['tagCircleSelectionBI'],
  });
};

// 查询指定层级下标签列表
export const querySpecifiedLevelLabel = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/admin/v1/weklin/crowdTags/crowd/querySpecifiedLevelLabel',
    method: 'POST',
    data: params,
    bindRoutePK: ['tagCircleSelectionBI'],
  });
};

// 查询指定层级下标签列表
export const querySpecifiedLevelLabelNew = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/bos/admin/v1/ai/crowd-tag/label/getLevelIndexByCategoryId',
    method: 'POST',
    data: params,
    bindRoutePK: ['tagCircleSelectionBI'],
  });
};

// 复合级联标签数据源
export const queryCombineLabelData = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/admin/v1/weklin/crowdTags/crowd/queryCombineLabelData',
    method: 'POST',
    data: params,
  });
};

// 复合级联标签数据源
export const queryCombineLabelDataNew = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/bos/admin/v1/ai/crowd-tag/label/queryCombineLabelByLabelId',
    method: 'POST',
    data: params,
  });
};
// 获取运力公司列表
export function getCarTeamList(params = {}) {
  return request({
    url: '/bos/admin/v1/common/dict/getCommon',
    method: 'get',
    params,
  });
}
// 人群圈选预校验权限接口
export const previewNewCrowdPower = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/bos/admin/v1/ai/crowd-tag/crowdCreatePermissionCheck',
    method: 'POST',
    data: params,
  });
};

// 预估司机数
export const previewNewCrowd = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/admin/v1/weklin/crowdTags/crowd/previewNewCrowd',
    method: 'POST',
    data: params,
  });
};
export const previewCallback = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/admin/v1/weklin/crowdTags/crowd/previewCallback',
    method: 'POST',
    data: params,
  });
};

export const createNewCrowd = (params: unknown) => {
  //@ts-ignore
  return request({
    url: '/admin/v1/weklin/crowdTags/crowd/createNewCrowd',
    method: 'POST',
    data: params,
  });
};

export const crowdCopy = (params: unknown) => {
  return request({
    url: '/admin/v1/weklin/crowdTags/crowd/copy',
    method: 'POST',
    data: params,
  });
};

// 人群标签-运力公司名称匹配接口
export const getQueryTransportCompanyByNames = (params: unknown) => {
  return request({
    url: '/bos/admin/v2/base/common/dropDown/queryTransportCompanyByNames',
    method: 'POST',
    data: params,
  });
};
