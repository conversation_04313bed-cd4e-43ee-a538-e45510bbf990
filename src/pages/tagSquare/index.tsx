import React, { useState, useEffect, useRef } from 'react';
import * as api from '../lego/api';

const TagSquare = () => {
  const [wdData, setWdData] = useState([]);
  const [dlData, setDlData] = useState([]);
  const compRef = useRef();

  useEffect(() => {
    api.getIndexData({}).then((res) => {
      setWdData(res.data.dimList);
      setDlData(res.data.indexList);
    });
  }, []);

  return (
    <>
      <button
        onClick={() => {
          compRef.current.update(1);
        }}
      >
        修改
      </button>
    </>
  );
};
export default TagSquare;
