import React, { useState, useRef } from 'react';
import { Form, Button, Divider, Radio, Select } from '@blmcp/ui';
import { BLMCrowdSelect } from '@blmcp/peento-businessComponents';
import './index.less';

const CrowdComponentDemo = () => {
  const crowdComponentRef = useRef();

  const [disabled, setDisabled] = useState(false);
  const [clearable, setClearable] = useState(true);
  const [requestParams, setRequestParams] = useState(null);
  const [multiple, setMultiple] = useState(false);
  const [useType, setUseType] = useState<'search' | 'detail'>('detail');
  const [showRefreshBtn, setShowRefreshBtn] = useState(true);
  const [showCrowdLink, setShowCrowdLink] = useState(true);
  const [showExtraText, setShowExtraText] = useState(true);
  const [showCrowdInfo, setShowCrowdInfo] = useState(true);
  const [showDefaultOption, setShowDefaultOption] = useState(null);
  const [showCrowdOptionID, setShowCrowdOptionID] = useState(true);
  const [showAll, setShowAll] = useState(true);
  const [optionDisableFn, setOptionDisableFn] = useState('value3');
  const [crowdComKey, setCrowdComKey] = useState(0);
  // 当前选中key值
  const [crowdItemKey, setCrowdItemKey] = useState(null);
  const [crowdItemOption, setCrowdItemOption] = useState(null);
  const [curGetSelectedLabelName, setCurGetSelectedLabelName] = useState(null);
  const [curGetSelectedOptions, setCurGetSelectedOptions] = useState(null);

  const handlers: Record<string, ((option: object) => boolean) | null> = {
    value1: (option) => {
      return option?.driverCnt > 100;
    },
    value2: (option) => {
      return option?.userSetType === 2;
    },
    value3: null,
  };
  const onValuesChange = (changedValues, allValues) => {
    console.log('onValuesChange', changedValues, allValues);
    // 获取“disabled”项的值
    if (changedValues.disabled !== undefined) {
      setDisabled(changedValues.disabled);
    }
    if (changedValues.clearable !== undefined) {
      setClearable(changedValues.clearable);
    }
    if (changedValues.multiple !== undefined) {
      setMultiple(changedValues.multiple);
    }
    if (changedValues.optionDisableFn !== undefined) {
      setOptionDisableFn(changedValues.optionDisableFn);
    }
    if (changedValues.requestParams !== undefined) {
      setRequestParams(changedValues.requestParams);
      if (changedValues.requestParams) {
        setRequestParams({ generationType: 1 });
      } else {
        setRequestParams(null);
      }
    }
    if (changedValues.showCrowdInfo !== undefined) {
      setShowCrowdInfo(changedValues.showCrowdInfo);
    }
    if (changedValues.showCrowdLink !== undefined) {
      setShowCrowdLink(changedValues.showCrowdLink);
    }
    if (changedValues.showCrowdOptionID !== undefined) {
      setShowCrowdOptionID(changedValues.showCrowdOptionID);
    }
    if (changedValues.showAll !== undefined) {
      setShowAll(changedValues.showAll);
    }
    if (changedValues.showDefaultOption !== undefined) {
      if (changedValues.showDefaultOption) {
        setShowDefaultOption({ crowdTagName: '全部人群', id: 'ALL' });
      } else {
        setShowDefaultOption(null);
      }
    }
    if (changedValues.showExtraText !== undefined) {
      setShowExtraText(changedValues.showExtraText);
    }
    if (changedValues.showRefreshBtn !== undefined) {
      setShowRefreshBtn(changedValues.showRefreshBtn);
    }
    if (changedValues.useType !== undefined) {
      setUseType(changedValues.useType);
    }
  };
  const ChangeEvent = (val, option) => {
    console.log('Change Event:', val, option);
    setCrowdItemKey(val);
    setCrowdItemOption(option);
  };
  return (
    <div
      style={{
        padding: 16,
        backgroundColor: '#fff',
        height: '100%',
        overflow: 'auto',
      }}
    >
      <h3>人群包组件4.1demo</h3>
      <Divider />
      <div className="crowdComponent">
        <div className="crowdComponent-left">
          <div>组件参数</div>
          <Form
            labelCol={{ span: 8 }}
            wrapperCol={{ span: 10 }}
            layout="horizontal"
            style={{ maxWidth: 600 }}
            initialValues={{
              disabled: false,
              clearable: true,
              multiple: false,
              requestParams: false,
              useType: 'detail',
              showRefreshBtn: true,
              showCrowdLink: true,
              showExtraText: true,
              showCrowdInfo: true,
              showDefaultOption: false,
              showCrowdOptionID: true,
              showAll: true,
              optionDisableFn: 'value3',
            }}
            onValuesChange={onValuesChange}
          >
            <Form.Item name="disabled" label="是否禁用">
              <Radio.Group>
                <Radio value={false}> 不禁用 </Radio>
                <Radio value={true}> 禁用 </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="clearable" label="是否可清空">
              <Radio.Group>
                <Radio value={true}> 可清空 </Radio>
                <Radio value={false}> 不可清空 </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="requestParams" label="是否传入额外请求参数">
              <Radio.Group>
                <Radio value={true}>传入（示例获取系统圈选人群包</Radio>
                <Radio value={false}> 不传入额外参数 </Radio>
              </Radio.Group>
            </Form.Item>
            <Divider />
            <div>分割线（以下参数组件内不动态监听，由初次传入组件为准）</div>
            <Form.Item name="multiple" label="模式">
              <Radio.Group>
                <Radio value={true}> 多选 </Radio>
                <Radio value={false}> 单选 </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="useType" label="使用场景">
              <Radio.Group>
                <Radio value="detail"> 编辑区 </Radio>
                <Radio value="search"> 搜索区 </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="showRefreshBtn" label="是否展示重新加载按钮">
              <Radio.Group>
                <Radio value={true}> 是（编辑区默认） </Radio>
                <Radio value={false}> 否 </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="showCrowdLink" label="是否展示跳转人群列表按钮">
              <Radio.Group>
                <Radio value={true}> 是（编辑区默认） </Radio>
                <Radio value={false}> 否 </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="showExtraText" label="是否展示人群包额外提示">
              <Radio.Group>
                <Radio value={true}> 是（编辑区默认） </Radio>
                <Radio value={false}> 否 </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="showCrowdInfo" label="是否展示人群包基础信息">
              <Radio.Group>
                <Radio value={true}> 是（编辑区默认） </Radio>
                <Radio value={false}> 否 </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="showDefaultOption" label="是否配置全部选项">
              <Radio.Group>
                <Radio value={true}>含有全部</Radio>
                <Radio value={false}> 不含全部 </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="showCrowdOptionID" label="是否展示静/动人群ID">
              <Radio.Group>
                <Radio value={true}> 是 </Radio>
                <Radio value={false}> 否 </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="showAll" label="多选场景是否展示全部选项">
              <Radio.Group>
                <Radio value={true}> 是 </Radio>
                <Radio value={false}> 否 </Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item name="optionDisableFn" label="选项置灰逻辑">
              <Select>
                <Select.Option value="value3">无条件</Select.Option>
                <Select.Option value="value1">
                  司机人群大于100条置灰
                </Select.Option>
                <Select.Option value="value2">动态人群包置灰</Select.Option>
              </Select>
            </Form.Item>
            <Button
              onClick={() => {
                setCrowdComKey(crowdComKey + 1);
              }}
            >
              刷新组件配置（用于不动态监听参数）
            </Button>
          </Form>
        </div>
        <Divider type="vertical" />
        <div className="crowdComponent-right">
          <div>组件示例</div>
          <Form key={crowdComKey}>
            <Form.Item
              name="crowdItem"
              rules={[{ required: true, message: '请选择人群包' }]}
            >
              <BLMCrowdSelect
                ref={crowdComponentRef}
                disabled={disabled}
                defaultOptions={showDefaultOption}
                clearable={clearable}
                multiple={multiple}
                requestParams={requestParams}
                useType={useType}
                showCrowdInfo={showCrowdInfo}
                showRefreshBtn={showRefreshBtn}
                showCrowdLink={showCrowdLink}
                showExtraText={showExtraText}
                showCrowdOptionID={showCrowdOptionID}
                showAll={showAll}
                optionDisableFn={handlers[optionDisableFn]}
                onChange={ChangeEvent}
                old={<div>测试</div>}
              ></BLMCrowdSelect>
            </Form.Item>
          </Form>
          <Divider />
          <div>当前选中值</div>
          <div>{JSON.stringify(crowdItemKey)}</div>
          <Divider />
          <div>当前选中项</div>
          <div>{JSON.stringify(crowdItemOption)}</div>
          <Divider />
          <Button
            onClick={() => {
              console.log(crowdComponentRef?.current?.getSelectedLabelName());
              setCurGetSelectedLabelName(
                crowdComponentRef?.current?.getSelectedLabelName(),
              );
            }}
          >
            点击获取ref属性的选中项中文名
          </Button>
          <div>{curGetSelectedLabelName}</div>
          <Button
            onClick={() => {
              setCurGetSelectedOptions(
                JSON.stringify(
                  crowdComponentRef?.current?.getSelectedOptions(),
                ),
              );
            }}
          >
            点击获取ref属性的选中项
          </Button>
          <div>{curGetSelectedOptions}</div>
        </div>
      </div>
    </div>
  );
};
export default CrowdComponentDemo;
