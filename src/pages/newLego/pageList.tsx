import LegoListView from '@blm/bi-lego-sdk/LegoListView';
import { event } from '@blm/bi-lego-sdk/utils';
import React, { useState } from 'react';
import { isHubble } from '@/utils/hubble';

import {
  blmAnalysisPageView,
  blmAnalysisModuleClick,
  blmMicrofs,
} from '@/utils/eventTracking';
import { getAllConfig } from '@/pages/lego/api/hubble';

export default function () {
  const [brandList, setBrandList] = useState<any[]>([]);
  const showEditTab =
    location.hostname === 'localhost'
      ? true
      : window?.BLMPermissionAuth?.({
          fk: '',
          type: 2, // 扩展字段  1列表页 2详情页 3tab 4按钮
          name: '自助报告-编辑态',
          pk: 'legoReportEditBI',
        });
  React.useEffect(() => {
    event.on('init', () => {
      blmAnalysisPageView({
        pageId: 'p_leopard_cp_00000480',
        eventId: 'e_leopard_cp_pv_00002626',
      });
    });
    event.on('loaded', () => {
      blmMicrofs();
    });
    event.on('templateCopy', ({ reportId, reportName, copyId }) => {
      blmAnalysisModuleClick({
        eventId: 'e_leopard_cp_click_00003554',
        pageId: 'p_leopard_cp_00000480',
        ext: {
          str0_e: reportId,
          str1_e: reportName,
          str2_e: copyId,
          str3_e: '0',
        },
      });
    });
    event.on('templateView', ({ reportId, reportType }) => {
      blmAnalysisModuleClick({
        eventId: 'e_leopard_cp_click_00003810',
        pageId: 'p_leopard_cp_00000884',
        ext: {
          str0_e: reportId,
          str1_e: reportType,
        },
      });
    });
    event.on('templateAdd', () => {
      blmAnalysisModuleClick({
        eventId: 'e_leopard_cp_click_00001100',
        pageId: 'p_leopard_cp_00000344',
      });
    });
    event.on('templateTransfer', ({ reportId, reportType }) => {
      blmAnalysisModuleClick({
        eventId: 'e_leopard_cp_click_00003808',
        pageId: 'p_leopard_cp_00000884',
        ext: {
          str0_e: reportType,
          str1_e: reportId,
        },
      });
    });
    event.on('templateEdit', ({ reportId }) => {
      blmAnalysisModuleClick({
        eventId: 'e_leopard_cp_click_00001098',
        pageId: 'p_leopard_cp_00000344',
        ext: {
          int0_e: reportId,
        },
      });
    });
    event.on('templateShare', ({ reportType, reportId }) => {
      blmAnalysisModuleClick({
        eventId: 'e_leopard_cp_click_00003806',
        pageId: 'p_leopard_cp_00000884',
        ext: {
          str0_e: reportType,
          str1_e: reportId,
        },
      });
    });

    // 获取放量租户列表数据
    if (isHubble) {
      getAllConfig().then((res: any) => {
        if (res?.data) {
          let arr: any = [];
          res.data?.forEach((item: any) => {
            arr.push({
              label: item?.name || '',
              value: item.tenantId || null,
              disabled: item.tenantId === 1,
              ...item,
            });
          });
          setBrandList(arr);
        }
      });
    }
  }, []);
  return <LegoListView showEditTab={showEditTab} brandList={brandList} />;
}
