import { event } from '@blm/bi-lego-sdk/utils';
import LegoRender from '@blm/bi-lego-sdk/LegoRender';
import React from 'react';
import { useParams } from 'umi';
import {
  blmAnalysisPageView,
  blmAnalysisModuleClick,
  blmMicrofs,
} from '@/utils/eventTracking';

export default function () {
  const params = useParams();
  React.useEffect(() => {
    event.on('init', ({ reportId }) => {
      blmAnalysisPageView({
        pageId: 'p_leopard_cp_00000668',
        eventId: 'e_leopard_cp_pv_00003084',
        ext: {
          str0_e: reportId,
        },
      });
      // blmAnalysisPageView({
      //   pageId: 'p_leopard_cp_00001012',
      //   eventId: 'e_leopard_cp_pv_00004304',
      //   ext: {
      //     str0_e: reportId,
      //   },
      // });
    });
    event.on('loaded', () => {
      blmMicrofs();
    });
    event.on('templateExport', ({ reportId }) => {
      blmAnalysisModuleClick({
        pageId: 'p_leopard_cp_00000400',
        eventId: 'e_leopard_cp_click_00003238',
        ext: {
          str0_e: reportId,
        },
      });
    });
  }, []);
  return <LegoRender reportKey={params.pageName} edit={false} />;
}
