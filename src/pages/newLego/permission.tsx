import React from 'react';
import LegoPermissionView from '@blm/bi-lego-sdk/LegoPermissionView';
import { event } from '@blm/bi-lego-sdk/utils';
import { blmAnalysisPageView, blmMicrofs } from '@/utils/eventTracking';

export default function () {
  React.useEffect(() => {
    event.on('init', () => {
      blmAnalysisPageView({
        pageId: 'p_leopard_cp_00000348',
        eventId: 'e_leopard_cp_pv_00001108',
      });
    });
    event.on('loaded', () => {
      blmMicrofs();
    });
  }, []);
  return <LegoPermissionView indexs={['司机-司机明细', '司机-订单统计']} />;
}
