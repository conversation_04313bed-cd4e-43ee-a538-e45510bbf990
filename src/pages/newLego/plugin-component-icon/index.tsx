import { IPublicModelPluginContext } from '@alilc/lowcode-types';
// import ExportData from './modules/ExportData';
import FilterEditor from './modules/FilterEditor';

const funcs = [
  // {
  //   name: '导出数据',
  //   Component: ExportData,
  //   noWrapper: true,
  // },
  {
    name: '配置',
    Component: FilterEditor,
  },
];
// const createComponent = (option) => (props) => {
//   if (option.noWrapper) {
//     return <option.Component {...props} />;
//   }
//   return (
//     <div className="lc-borders-action" title={option.name}>
//       <option.Component {...props} />
//     </div>
//   );
// };
// 保存功能示例
const DiyComponentIconPlugin = (ctx: IPublicModelPluginContext) => {
  return {
    async init() {
      const { config } = ctx;
      const diyActions = config.get('diyActions') ?? [];
      funcs.forEach((m) => {
        diyActions.push(m.Component);
      });
      config.set('diyActions', diyActions);
    },
  };
};
DiyComponentIconPlugin.pluginName = 'DiyComponentIconPlugin';
export default DiyComponentIconPlugin;
