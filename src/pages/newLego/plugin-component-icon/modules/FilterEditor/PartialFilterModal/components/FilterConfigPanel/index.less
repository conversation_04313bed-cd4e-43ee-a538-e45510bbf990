.filter-config-panel-context {
  height: 100%;
  display: flex;
  flex-direction: column;

  .empty-config {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8c8c8c;
    .empty-config-png {
      width: 64px;
      height: 64px;
      background-image: url('../../../../../../../../assets/img/noDataPartial.png');
      margin: 8px auto;
    }
    .empty-config-text {
      color: rgba(0, 0, 0, 0.6);
      font-size: 12px;
    }
  }

  .config-section {
    display: flex;
    padding: 12px;
    border-bottom: 1px solid #f2f3f5;

    .section-title {
      font-size: 14px;
      color: #86909c;
      margin-right: 16px;
    }

    .filter-type-radio {
      .ant-radio-wrapper {
        margin-right: 16px;
      }
    }

    .list-filter-alert {
      margin-top: 12px;
    }
  }
  .config-section-table {
    padding: 12px;
    .section-title {
      color: #4e5969;
    }
  }
}
