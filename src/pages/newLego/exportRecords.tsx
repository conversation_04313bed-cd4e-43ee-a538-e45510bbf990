import React from 'react';
import LegoExportRecords from '@blm/bi-lego-sdk/LegoExportRecords';
// import LegoPermissionView from '@blm/bi-lego-sdk/LegoPermissionView';

import { event } from '@blm/bi-lego-sdk/utils';
import { Button } from '@blmcp/ui';
import { blmAnalysisPageView, blmMicrofs } from '@/utils/eventTracking';

export default function () {
  React.useEffect(() => {
    event.on('init', () => {
      blmAnalysisPageView({
        pageId: 'p_leopard_cp_00000348',
        eventId: 'e_leopard_cp_pv_00001108',
      });
    });
    event.on('loaded', () => {
      blmMicrofs();
    });
  }, []);
  return (
    <LegoExportRecords
      btnSlot={(records: any) => <Button>{records.elementTitle}</Button>}
    />
  );
}
