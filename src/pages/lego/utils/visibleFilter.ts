import { getDesignWindow } from '@/pages/lego/utils';
import { isHubble } from '@/utils/hubble';
import linkageCenterExp from '../libraryMaterials/module/Linkage';
import isMobile from './isMobile';

const filterNumMap: { [key: string]: number } = {
  adcode: 1,
  car_team_id: 2,
  fleet_id: 3,
};

interface Props {
  [key: string]: boolean;
}

class VisibleFilter {
  props: Props;
  styleDom: HTMLElement | null;
  hideNum: number;
  constructor() {
    this.props = {};
    this.styleDom = null;
  }

  init() {
    this.remove();
    this.styleDom = null;
    this.props = {};
  }

  run() {
    this.hideNum = 0;
    let str = '';
    // eslint-disable-next-line guard-for-in
    for (let k in this.props) {
      str += this.toCss(filterNumMap[k], this.props[k]);
      if (!this.props[k]) {
        this.hideNum += 1;
      }
    }
    this.createStyle(str);
  }

  toProps(level: number) {
    for (let k in filterNumMap) {
      if (filterNumMap[k] + 1 <= level) {
        this.props[k] = true;
      } else {
        this.props[k] = false;
      }
    }
  }

  toCss(_index: number, visible: boolean) {
    const index = isHubble ? _index + 1 : _index;
    if (isMobile()) {
      return `
      .lce-page .lego-default-row-filter-wrapper>div:nth-of-type(${index}) {
        display: ${visible ? 'block' : 'none !important'};
      }
    `;
    }
    return `
      .lce-page .legoFilterDefaultRow>.fd-layout-cell:nth-of-type(${index}) {
        display: ${visible ? 'block' : 'none !important'};
      }
    `;
  }

  createStyle(str: string) {
    if (!this.styleDom) {
      this.styleDom = document.createElement('style');
      this.styleDom.id = 'legoFilterVisibleStyle';
      getDesignWindow()?.document.head.appendChild(this.styleDom);
    }

    this.styleDom.innerHTML = str;
  }

  set(_level: number) {
    const level = _level || 4;
    this.toProps(level);
    this.run();

    // 设置在window 上，方便组件内部使用
    window.legoMinAuthLevel = level;
    const linkageCenter = linkageCenterExp('');

    // 设置组件数据源
    linkageCenter.notify(
      'setCarTeamFilterKind',
      level > 3 ? 'link' : 'authOpen',
    );
    linkageCenter.notify('setCityFilterKind', level > 2 ? 'link' : 'authOpen');
  }

  remove() {
    const dom = getDesignWindow()?.document.getElementById(
      'legoFilterVisibleStyle',
    );
    if (dom) {
      getDesignWindow()?.document.head.removeChild(dom);
    }
  }
}

export default new VisibleFilter();
