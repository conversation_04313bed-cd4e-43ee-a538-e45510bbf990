import { getPublicApiHub, ApiListType } from '@blmcp/peento-publicApiHub';
import request from '@/utils/request';

const apiRequestList: ApiListType = getPublicApiHub(request);

export default async function () {
  try {
    const res = await apiRequestList.batchCloudConfig({
      group: 'default',
      modules: [
        {
          moduleName: '	cp-platform-normal-config',
          configNames: ['legoGlobalConfig'],
        },
      ],
    });
    if (res.code === 1) {
      return JSON.parse(res.data[0].value);
    }
  } catch (error) {
    return {};
  }
}
