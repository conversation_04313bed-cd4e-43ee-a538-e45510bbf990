/*
 * Created on Tue Jan 30 2024
 *
 * Copyright (c) 2024 bai long ma
 */

// 给指定的dom元素设置css样式
const css = (el: HTMLElement, styles: { [key: string]: string }) => {
  Object.keys(styles).forEach((key: string) => {
    el.style.setProperty(key, styles[key]);
  });
};

// 创建一个dom元素
const element = (
  tag: string,
  property: { [key: string]: any },
  parentNode: HTMLElement,
) => {
  const dom = document.createElement(tag);
  Object.keys(property).forEach((key: string) => {
    if (key === 'css') {
      css(dom, property[key]);
    } else if (key === 'innerHTML') {
      dom.innerHTML = property.innerHTML;
    }
  });
  parentNode.appendChild(dom);
  return {
    dom,
    remove: () => {
      parentNode.removeChild(dom);
    },
  };
};

// 计算一个dom元素填充文案之后的高度和宽度，可用于测量省略号
export const getTextyWidthAndHeight = (
  text: string,
  styles: object,
  tag = 'div',
) => {
  const el = element(tag, { css: styles, innerHTML: text }, document.body);
  // getBoundingClientRect的精度更高
  let width, height;
  if (el?.dom?.getBoundingClientRect) {
    width = el.dom.getBoundingClientRect().width;
    height = el.dom.getBoundingClientRect().height;
  } else {
    width = el.dom.offsetWidth;
    height = el.dom.offsetHeight;
  }
  el.remove();
  return { width, height };
};

// 计算当前table表格表头的最大宽度
// 动态计算列表头宽度，内容宽度根据table宽度自适应
export const calculateColumnWidth = (title: string, maxWidth = 100) => {
  const aliasTitle =
    typeof title === 'string' && title
      ? title?.replace(/\s+/g, ' ').trim()
      : '';
  // 分组表头可能需要更多的空间来容纳子列
  const baseWidth = aliasTitle?.length * 14 + 32 + 12 + 10 || 0;
  return Math.max(baseWidth, maxWidth);
};
