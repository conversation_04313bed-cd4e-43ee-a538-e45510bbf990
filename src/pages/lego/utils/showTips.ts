import { getWindow, setVariable } from '@/utils/common';

const notAuthSet = setVariable('__lego_showTipsSet__', {});

const insertBeforeAlert = (reportId: string, arr: string[]) => {
  const info = `以下图表中使用了[${arr.join(
    '、',
  )}]数据集，您暂无权限，请联系管理员添加`;
  const Window = getWindow();

  const infoDom = Window.document.getElementById('lego-notDataSetInfo');

  // 存在该dom 需要变更内部信息
  if (infoDom) {
    infoDom.querySelector('.ant-alert-message')!.innerHTML = info;
    return false;
  }

  const newElement = Window.document.createElement('div');
  newElement.classList.add(
    'ant-alert',
    'ant-alert-info',
    'BLMAlert_Antd',
    'css-iksrmj',
    'lego-alert-info',
  );
  newElement.setAttribute('data-show', 'true');
  newElement.setAttribute('role', 'alert');
  newElement.setAttribute('id', 'lego-notDataSetInfo');
  newElement.innerHTML = `<span role="img" class="anticon ant-alert-icon"><svg fill="currentColor" version="1.1" width="14" height="14" viewBox="0 0 16 16"><defs><clipPath id="master_svg0_44_1044"><rect x="0" y="0" width="16" height="16" rx="0"></rect></clipPath></defs><g clip-path="url(#master_svg0_44_1044)"><g><path d="M8,15C11.866,15,15,11.866,15,8C15,4.13401,11.866,1,8,1C4.13401,1,1,4.13401,1,8C1,11.866,4.13401,15,8,15ZM7.0995,4.9950399999999995C7.04623,4.46228,7.46459,4,8,4C8.535409999999999,4,8.953769999999999,4.46228,8.900500000000001,4.9950399999999995L8.54975,8.50248C8.52151,8.78492,8.28384,9,8,9C7.71616,9,7.47849,8.78492,7.45025,8.50248L7.0995,4.9950399999999995ZM8,10C7.44772,10,7,10.44772,7,11C7,11.5523,7.44772,12,8,12C8.55228,12,9,11.5523,9,11C9,10.44772,8.55228,10,8,10Z" fill-rule="evenodd"></path></g></g></svg></span><div class="ant-alert-content"><div class="ant-alert-message">${info}</div>`;

  // 判断是预览还是编辑态
  const previewDom = Window.document.querySelector(
    '.lowcode-plugin-sample-preview',
  );
  if (previewDom) {
    const beforeDom = Window.document.querySelector(
      `.legoReport${reportId} .lowcode-plugin-sample-box`,
    );
    previewDom.insertBefore(newElement, beforeDom);
  } else {
    const editDom = Window.document.querySelector('.lc-workbench-center');
    const beforeDom = Window.document.querySelector('.lc-main-area');
    editDom!.insertBefore(newElement, beforeDom);
  }
};

export default {
  add(error: string, reportId: string) {
    const matchInfo = (error || '').match(
      /当前图表使用了\s+([^}]+)\s+指标,您无权限/,
    );
    if (matchInfo && matchInfo[1]) {
      notAuthSet[reportId] ??= new Set();
      notAuthSet[reportId].add(matchInfo[1]);
      // 判断编辑态还是预览态，插入提示
      try {
        insertBeforeAlert(reportId, [...notAuthSet[reportId]]);
      } catch (e) {}
    }
  },
  clear(reportId: string) {
    notAuthSet[reportId]?.clear?.();
    const Window = getWindow();
    const dom = Window.document.querySelector(
      `.legoReport${reportId} .lego-alert-info`,
    );
    if (dom) {
      dom.remove();
    }
  },
};
