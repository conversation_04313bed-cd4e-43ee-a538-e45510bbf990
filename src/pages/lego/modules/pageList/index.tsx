import React, { useState, useEffect } from 'react';
import { Tabs } from '@blmcp/ui';
import { useSearchParams } from 'react-router-dom';
import { isHubble } from '@/utils/hubble';
import getAssets from '@/pages/lego/modules/editPage/services/getAssets';
import { loadCDN } from '@/utils/loadCompontent';
import CommonTable from './components/CommonTable';
import { Template } from './components/Tempate';
import { Header } from './components/Header';

import styles from './index.less';
import HubblePage from './components/HubblePage';

const PageList = () => {
  const [searchParams] = useSearchParams();

  const [tabKey, setTabKey] = useState<string>(
    searchParams.get('listKey') ?? (isHubble ? '4' : '3'),
  );

  const onChange = (key: string) => {
    setTabKey(key);
  };
  // 判断是否有报告详情页面权限，区分制表人和看表人
  const pageListPermission =
    location.hostname === 'localhost'
      ? true
      : window?.BLMPermissionAuth?.({
          fk: '',
          type: 2, // 扩展字段  1列表页 2详情页 3tab 4按钮
          name: '自助报告-编辑态',
          pk: 'legoReportEditBI',
        });

  const PublicTemplate = {
    key: '3',
    label: '公共模板',
    children: <Template pageListPermission={pageListPermission} />,
  };

  const ICreated = {
    key: '1',
    label: '我创建的',
    children: (
      <CommonTable tabKey="1" pageListPermission={pageListPermission} />
    ),
  };

  const Shared = {
    key: '2',
    label: '被分享的',
    children: (
      <CommonTable tabKey="2" pageListPermission={pageListPermission} />
    ),
  };

  let items = [];
  if (isHubble) {
    // 哈勃场景
    items = [
      {
        key: '4',
        label: '公共模板',
        children: <HubblePage tabKey={4} />,
      },
      {
        key: '5',
        label: '我创建的',
        children: <HubblePage tabKey={1} />,
      },
    ];
  } else if (pageListPermission) {
    // SP 有编辑页权限
    items = [PublicTemplate, ICreated, Shared];
  } else {
    // SP 无编辑页权限
    items = [PublicTemplate, Shared];
  }

  useEffect(() => {
    setTimeout(() => {
      window.$legoAssets = window.$legoAssets || {};
      const assets = getAssets();
      assets.packages.forEach((item) => {
        item.urls.forEach((url) => {
          if (!window.$legoAssets || window.$legoAssets[item.library]) return;
          window.$legoAssets[item.library] = true;
          loadCDN(url);
        });
      });
    }, 1000);
  }, []);

  return (
    <div className={styles['legoList']}>
      {!isHubble ? <Header /> : null}
      <div className={styles['legoList-content']}>
        <Tabs
          activeKey={tabKey}
          items={items}
          onChange={(e) => onChange(e)}
          destroyInactiveTabPane={true}
        />
      </div>
    </div>
  );
};

export default PageList;
