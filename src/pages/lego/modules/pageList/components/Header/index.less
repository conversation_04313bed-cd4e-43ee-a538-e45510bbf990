.welcome {
  display: flex;
  flex-direction: row;
  height: 94px;
  border-radius: 8px;
  opacity: 1;
  padding: 16px 10px 16px 16px;
  gap: 10px;
  justify-content: space-between;
  background: #e2e9ff;
  overflow: hidden;
}
.welcome-title {
  display: flex;
  align-items: center;
  letter-spacing: 0em;
  color: #14286a;
  font-size: 22px;
  font-weight: 600;
}
.welcome-subtitle {
  margin-top: 8px;
  font-size: 14px;
  font-weight: normal;
  line-height: 22px;
  display: flex;
  align-items: center;
  letter-spacing: 0em;
  color: #14286a;
}
.welcome-icon {
  position: relative;
  top: -125px;
}
.browsing {
  margin-top: 10px;
  margin-bottom: 10px;
  background-color: white;
  min-height: 120px;
  border-radius: 8px;
  padding: 16px 12px;
  & {
    a,
    a:link,
    a:visited,
    a:hover,
    a:active {
      text-decoration: none;
      color: inherit;
    }
  }
}
.browsing-list {
  margin-top: 12px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  grid-column-gap: 16px;
  grid-row-gap: 16px;
  & > a {
    margin-left: 0px !important;
  }
}
.browsing-title {
  color: rgba(0, 0, 0, 0.9);
  font-size: 15px;
  font-weight: 500;
  line-height: 24px;
}
.browsing-item {
  position: relative;
  border-radius: 8px;
  padding: 12px;
  padding-right: 30px;
  font-size: 14px;
  gap: 8px;
  flex-grow: 1;

  color: rgba(0, 0, 0, 0.9);
  box-sizing: border-box;
  /* lineColor */
  /* 样式描述：分割线色 */
  border: 0px;
  background: #f6f7f7;
  cursor: pointer;
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 使用省略号表示截断的部分 */
}
.icon {
  position: relative;
  top: 2px;
}
.right-icon {
  position: absolute;
  right: 12px;
}
