import { useRequest } from 'ahooks';
import { Empty, Skeleton } from '@blmcp/ui';
import { getBrowsingHistory_HotContent } from '@/pages/lego/api/template';
import { ReactComponent as WelcomeIcon } from '@/assets/lego/welcome.svg';

import LegoNoDataImg from '@/assets/img/new_empty.png';
import { ItemBrowse } from './ItemBrowse';

import styles from './index.less';

export const Header = () => {
  const { data, error, loading } = useRequest<
    { browsing: { name: string; id: string }[] },
    []
  >(getBrowsingHistory_HotContent);

  return (
    <>
      <div className={styles['welcome']}>
        <div>
          <div className={styles['welcome-title']}>👏欢迎来到数据乐高！</div>
          <div className={styles['welcome-subtitle']}>
            数据乐高提供通用分析模板，支持快速搭建数据报告，从而提升看数和分析效率，沉淀分析方法，助力提升经营效率。
          </div>
        </div>

        <WelcomeIcon className={styles['welcome-icon']} />
      </div>
      <div className={styles['browsing']}>
        <div className={styles['browsing-title']}>我的浏览记录</div>

        <div>
          {loading ? (
            <Skeleton></Skeleton>
          ) : data?.data?.length > 0 ? (
            <div className={styles['browsing-list']}>
              {data?.data?.map?.((item, index) => {
                return (
                  <ItemBrowse
                    key={item.pathName + item.url}
                    text={item.pathName}
                    url={item.url}
                  />
                );
              })}
            </div>
          ) : (
            <Empty
              image={LegoNoDataImg}
              imageStyle={{ height: 90, marginTop: '30px' }}
              description="暂无数据"
            ></Empty>
          )}
        </div>
      </div>

      <div className={styles['legoList-content']}></div>
    </>
  );
};
