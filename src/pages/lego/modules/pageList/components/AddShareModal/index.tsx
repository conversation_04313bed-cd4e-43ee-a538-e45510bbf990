import React, { useState, useEffect } from 'react';
import { Transfer, Select, Tree, BLMIconFont } from '@blmcp/ui';
import { queryShareList } from '../../../../api/index';
import styles from './index.less';

const AddShareModal = ({ modalView, onModalOk, tabsType }) => {
  const { type, reportId, visible } = modalView;

  // 选中分享人
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  // 分享人数据
  const [dataSource, setDataSource] = useState<any[]>([]);
  // 转让人数据
  const [transferData, setTransferData] = useState<any[]>([]);
  // 历史选中分享人
  const [originTargetKeys, setOriginTargetKeys] = useState<string[]>([]);

  useEffect(() => {
    // 弹窗进入，将targetKeys和数据源进行重置
    setTargetKeys([]);
    setDataSource([]);
    setTransferData([]);
    // 获取穿梭框数据
    const searchParamsShare = {
      reportId: reportId,
      type: 1,
    };
    const searchParamsTransfer = {
      reportId: reportId,
      type: 2,
      pageSize: 100,
      pageNum: 1,
    };
    const searchParams =
      type === 'share' ? searchParamsShare : searchParamsTransfer;
    queryShareList(searchParams).then((res) => {
      if (res && res.data) {
        const targetKeys: string[] = [];
        let source = [];
        if (type === 'share') {
          source =
            res.data &&
            (res.data.items || res.data).map((item: any) => {
              const items = {
                key: item.id,
                title: item.name,
                value: item.id,
                disabled: Number(tabsType) === 2 ? item.share : false,
              };
              // 已分享的人员
              if (item.share) {
                targetKeys.push(items.key);
              }
              return items;
            });
          setDataSource(source);
          setTargetKeys(targetKeys);
          setOriginTargetKeys(targetKeys);
          onModalOk({ type, targetKeys, originShareList: targetKeys });
        } else {
          source = res.data.items;
          setTransferData(source);
        }
      }
    });
  }, []);

  // 根据搜索内容进行筛选
  const filterOption = (inputValue: any, option: any) => {
    return option.title.indexOf(inputValue) > -1;
  };

  // 选项在两栏之间转移时的回调函数
  const handleChange = (targetKeys: any) => {
    setTargetKeys(targetKeys);
    onModalOk({ type, targetKeys, originShareList: originTargetKeys });
  };

  // 下拉框搜索函数
  const handleTransferSearch = (e: any) => {
    queryShareList({ reportId, type: 2, q: e, pageSize: 100, pageNum: 1 }).then(
      (res) => {
        if (res && res.data) {
          setTransferData(res.data.items);
        }
      },
    );
  };
  // 下拉框改变函数回调
  const handleTransferChange = (e: any) => {
    setTargetKeys(e);
    onModalOk({ type, targetKeys: e, originShareList: [] });
    // 改变完函数后再调用一次重置内容
    queryShareList({ reportId, type: 2, pageSize: 100, pageNum: 1 }).then(
      (res) => {
        if (res && res.data) {
          setTransferData(res.data.items);
        }
      },
    );
  };
  const isChecked = (selectedKeys: React.Key[], eventKey: React.Key) =>
    selectedKeys.includes(eventKey);
  return (
    <>
      {modalView.type === 'share' ? (
        <Transfer
          className={styles['AddShareModal-Transfer']}
          key={String(Math.random())}
          dataSource={dataSource}
          targetKeys={targetKeys}
          filterOption={filterOption}
          notShowAnticonDown={true}
          showSearch
          listStyle={{
            width: 315,
            height: 380,
          }}
          render={(item: any) => item.title}
          onChange={handleChange}
          selectAllLabels={[
            ({ selectedCount, totalCount }) => (
              <div className={styles.transfer_title}>
                可选人员
                <span className={styles.transfer_num}>
                  {selectedCount}/{totalCount}
                </span>
              </div>
            ),
            ({ selectedCount, totalCount }) => (
              <div className={styles.transfer_title}>
                已选人员
                <span className={styles.transfer_num}>
                  {selectedCount}/{totalCount}
                </span>
              </div>
            ),
          ]}
        >
          {/* 若穿梭框内数据量过大，左右两侧使用tree组件实现虚拟滚动（UI与原先穿梭框有部分细节差异） */}
          {/* {({ direction, onItemSelect, selectedKeys, filteredItems }) => {
            if (direction === 'left') {
              return (
                  <Tree
                    blockNode
                    checkable
                    checkStrictly
                    checkedKeys={selectedKeys}
                    selectedKeys={selectedKeys}
                    treeData={filteredItems}
                    height={280}
                    onCheck={(_, { node: { key } }) => {
                      onItemSelect(
                        key as string,
                        !isChecked(selectedKeys, key),
                      );
                    }}
                    onSelect={(_, { node: { key } }) => {
                      onItemSelect(
                        key as string,
                        !isChecked(selectedKeys, key),
                      );
                    }}
                  />
              );
            } else if (direction === 'right') {
              return (
                <Tree
                  blockNode
                  checkable
                  checkStrictly
                  checkedKeys={selectedKeys}
                  selectedKeys={targetKeys}
                  treeData={filteredItems}
                  height={280}
                  onCheck={(_, { node: { key } }) => {
                    onItemSelect(key as string, !isChecked(selectedKeys, key));
                  }}
                  onSelect={(_, { node: { key } }) => {
                    onItemSelect(key as string, !isChecked(selectedKeys, key));
                  }}
                />
              );
            }
          }} */}
        </Transfer>
      ) : (
        <div>
          <div className={styles['transferAlert']}>
            <BLMIconFont
              className={styles['transferAlert-icon']}
              type="BLM-ic-information"
            />
            <span className={styles['transferAlert-text']}>
              转让成功后，您将无法编辑此报告
            </span>
          </div>
          <div>
            <span>选择人员</span>
            <Select
              showSearch
              placeholder="请选择"
              options={transferData}
              allowClear
              onChange={(e: any) => handleTransferChange(e)}
              onSearch={(e: any) => handleTransferSearch(e)}
              fieldNames={{ label: 'name', value: 'id' }}
              filterOption={false}
              style={{ width: 240, marginLeft: 58 }}
              notFoundContent={
                <div className={styles['lego-noDataTransfer']}>
                  <div className={styles['lego-noDataTransfer_img']}></div>
                  <p className={styles['lego-noDataTransfer_text']}>
                    请联系管理员给被转让人授权
                  </p>
                </div>
              }
            />
          </div>
        </div>
      )}
    </>
  );
};
export default AddShareModal;
