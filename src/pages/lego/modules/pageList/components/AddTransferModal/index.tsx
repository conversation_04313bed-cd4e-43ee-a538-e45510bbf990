import React, {
  forwardRef,
  useState,
  useEffect,
  useImperativeHandle,
} from 'react';
import { Modal, Transfer, Space, Radio } from '@blmcp/ui';
import styles from './index.scss';

const AddTransferModal = forwardRef(function (props, ref) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  const [dataSource, setDataSource] = useState<any[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [value, setValue] = useState(0);

  const { data } = props;

  useEffect(() => {
    if (data.length > 0) {
      // 穿梭框数据转换
      const source = data.map((item: any) => {
        const items = {
          key: item.id,
          title: item.name,
          value: item.id,
        };
        return items;
      });

      setDataSource(source);
    }
  }, [data]);

  const openModal = () => {
    setIsModalOpen(true);
  };

  useImperativeHandle(ref, () => ({
    openModal,
  }));

  // 打开报告弹窗
  const handleOk = () => {
    setIsModalOpen(false);
  };

  // 关闭报告弹窗
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  // 根据搜索内容进行筛选
  const filterOption = (inputValue: any, option: any) => {
    return option.title.indexOf(inputValue) > -1;
  };

  const onSelectChange = (
    sourceSelectedKeys: string[],
    targetSelectedKeys: string[],
  ) => {
    setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
  };

  // 选项在两栏之间转移时的回调函数
  const handleChange = (targetKeys: any) => {
    setTargetKeys(targetKeys);
  };

  return (
    <Modal
      title="转让报告"
      open={isModalOpen}
      onOk={handleOk}
      onCancel={handleCancel}
      width={720}
    >
      <Transfer
        className={styles['AddShareModal-Transfer']}
        key={String(Math.random())}
        dataSource={dataSource}
        targetKeys={targetKeys}
        selectedKeys={selectedKeys}
        filterOption={filterOption}
        notShowAnticonDown
        showSearch
        listStyle={{
          width: 315,
          height: 380,
        }}
        render={(item) => item.title}
        onSelectChange={onSelectChange}
        onChange={handleChange}
        selectAllLabels={[
          ({ selectedCount, totalCount }) => (
            <div className={styles.transfer_title}>
              可选人员
              <span className={styles.transfer_num}>
                {selectedCount}/{totalCount}
              </span>
            </div>
          ),
          ({ selectedCount, totalCount }) => (
            <div className={styles.transfer_title}>
              已选人员
              <span className={styles.transfer_num}>
                {selectedCount}/{totalCount}
              </span>
            </div>
          ),
        ]}
      >
        {({ direction, onItemSelect, selectedKeys }) => {
          if (direction === 'left') {
            return (
              <Radio.Group
                onChange={(node) => {
                  setValue(node.target.value);
                  // onItemSelect(key as string, true);
                }}
                value={value}
              >
                <Space direction="vertical">
                  <Radio value={1}>Option A</Radio>
                  <Radio value={2}>Option B</Radio>
                  <Radio value={3}>Option C</Radio>
                </Space>
              </Radio.Group>
            );
          }

          if (direction === 'right') {
            return (
              <Radio.Group onChange={(node) => {}} value={2}>
                <Space direction="vertical">
                  <Radio value={2}>Option B</Radio>
                </Space>
              </Radio.Group>
            );
          }
        }}
      </Transfer>
    </Modal>
  );
});

export default AddTransferModal;
