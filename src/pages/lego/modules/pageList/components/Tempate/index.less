.template-container {
  overflow: auto;
  height: calc(100vh - 125px);
}
.template-wrap {
  padding-top: 10px;
  margin-left: 20px;
  margin-right: 20px;
  // height: 100%;
  // height: calc(100vh - 170px);
  display: flex;
  flex-wrap: wrap;
  grid-gap: 16px;
  & > i {
    flex: 1;
    min-width: 316px;
    max-width: 490px;
    height: 0px;
  }
}
.card {
  min-width: 316px;
  max-width: 490px;
  flex: 1;
  height: 272px;
  display: flex;
  flex-direction: column;

  box-sizing: border-box;
  border-radius: 8px;
  border: 1px solid #e7e8eb;
  z-index: 2;
  transition: transform 0.3s ease;
  background: linear-gradient(157deg, #f4f7ff 0%, rgba(255, 255, 255, 0) 38%);
  &:hover {
    box-shadow: 0px 12px 20px 4px rgba(0, 0, 0, 0.06);
    transform: translateY(-5px);
  }
}

.name {
  cursor: pointer;
  min-width: 292px;
  font-size: 16px;
  font-weight: 500;
  line-height: 24px;
  display: flex;
  align-items: center;
  margin: 12px;
}
.title {
  flex: 1;
  color: rgba(0, 0, 0, 0.9);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.icon {
  margin-right: 6px;
}
.img-wrap {
  flex: 1;
  display: flex;
  border-bottom: 1px solid rgba(231, 232, 235, 0.6);
  justify-content: center;
  align-items: center;
  cursor: pointer;
  margin-bottom: 0px;
  padding-bottom: 12px;
  >img{
    // width: 95%;
    width: 292px;
  }
}
.footer {
  display: flex;
  flex-direction: row;
  justify-content: space-around;
  align-items: center;
  padding: 5px 12px;
  height: 32px;
  min-width: 60px;
  margin: 8px 12px 8px 12px;
  color: rgba(0, 0, 0, 0.9);
}
.btn {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: center;
  cursor: pointer;
}
.operation-icon {
  margin-right: 6px;
}
.welcome {
  font-size: 16px;
  margin-left: 20px;
  padding: 10px;
}
.sub-title {
  font-size: 16px;
  margin-left: 20px;
  font-weight: 800;
}
