import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, Modal } from '@blmcp/ui';
import {
  BLMTemplatePage,
  TemplatePageSchema,
} from '@blmcp/peento-businessComponents';
import { Link } from '@umijs/max';
import { message } from '@blmcp/ui';

import { throttle } from 'lodash';
import request from '@/utils/request';
import { clearLocaPageId } from '@/pages/lego/utils';
import {
  blmAnalysisPageView,
  blmAnalysisModuleClick,
} from '@/utils/eventTracking';
import { isHubble } from '@/utils/hubble';
import { deleteReport, addShares, exchangeOwner } from '../../../../api';
import UserTransfer from '../AddShareModal/index';
import { schemaEdit } from './schemaEdit';
import { schemaShare } from './schemaShare';
import styles from './index.less';

// console.log(1);
interface CommonTableProps {
  tabKey?: string;
  pageListPermission: boolean;
}

const deleteItem = throttle(
  async (id, tabKey, editRef, shareRef) => {
    Modal.confirm({
      title: '您确认要删除吗？',
      okText: '确定',
      cancelText: '取消',
      autoFocusButton: null,
      content: '删除后，当前报告将无法恢复',
      onOk: () => {
        deleteReport({ reportId: id, type: Number(tabKey) }).then((res) => {
          if (res && res?.code === 1) {
            // 删除成功，重新调用列表接口
            message.success('删除成功');
            // 删除完重新调用接口
            if (tabKey === '1') {
              editRef?.current?.refreshTableList({}, false);
            } else {
              shareRef?.current?.refreshTableList({}, false);
            }
          }
        });
      },
    });
  },
  1000,
  { trailing: false },
);

const CommonTable = ({ tabKey, pageListPermission }: CommonTableProps) => {
  const editRef = useRef<null | HTMLElement>(null);
  const shareRef = useRef<null | HTMLElement>(null);
  // 分享&编辑弹窗
  const [modalView, setModalView] = useState({
    visible: false,
    type: '',
    reportId: Number,
  });
  // 穿梭框内选中的值
  const [selectedKeys, setSelectedKeys] = useState({});

  // 点击删除报告
  const handleDelete = (id: number) => {
    deleteItem(id, tabKey, editRef, shareRef);
  };

  // 弹窗确认
  const handleOk = () => {
    if (
      (modalView.type === 'share' &&
        (selectedKeys?.originShareList.length ||
          selectedKeys?.targetKeys?.length)) ||
      (modalView.type === 'transfer' && Number(selectedKeys?.targetKeys))
    ) {
      const searchParamsShare = {
        reportId: modalView.reportId,
        shareList: selectedKeys?.targetKeys ?? [],
        originShareList: selectedKeys?.originShareList,
      };
      const searchParamsTransfer = {
        reportId: modalView.reportId,
        transferId: selectedKeys?.targetKeys,
      };
      const queryApi = modalView.type === 'share' ? addShares : exchangeOwner;
      const searchParams =
        modalView.type === 'share' ? searchParamsShare : searchParamsTransfer;
      queryApi(searchParams).then((res) => {
        if (res && res?.code === 1) {
          if (modalView.type === 'share') {
            message.success('分享成功');
            // 我编辑下的分享
            if (tabKey === '1') {
              blmAnalysisModuleClick({
                eventId: 'e_leopard_cp_click_00001094',
                pageId: 'p_leopard_cp_00000344',
                ext: {
                  str0_e: modalView.reportId,
                  int0_e: selectedKeys?.targetKeys?.length ?? 0,
                },
              });
            } else if (tabKey === '2') {
              // 被分享下的分享
              blmAnalysisModuleClick({
                eventId: 'e_leopard_cp_click_00001256',
                pageId: 'p_leopard_cp_00000346',
                ext: {
                  str0_e: modalView.reportId,
                  int0_e: selectedKeys?.targetKeys?.length ?? 0,
                },
              });
            }
          } else {
            // 编辑内转让
            if (tabKey === '1') {
              blmAnalysisModuleClick({
                eventId: 'e_leopard_cp_click_00001096',
                pageId: 'p_leopard_cp_00000344',
                ext: {
                  str0_e: selectedKeys?.targetKeys,
                },
              });
            }
            message.success('转让成功');
          }
          if (modalView.type === 'transfer') {
            // 仅转让需要重新调用
            editRef?.current?.refreshTableList({}, false);
          }
        }
      });
      setModalView({ ...modalView, visible: false, type: '' });
    } else {
      message.error('请选择人员');
    }
  };

  // 关闭报告弹窗
  const handleCancel = () => {
    setModalView({ ...modalView, visible: false, type: '' });
  };
  useEffect(() => {
    // 我编辑页面埋点
    if (tabKey === '1') {
      blmAnalysisPageView({
        pageId: 'p_leopard_cp_00000344',
        eventId: 'e_leopard_cp_pv_00001092',
      });
    } else {
      // 被分享页面埋点
      blmAnalysisPageView({
        pageId: 'p_leopard_cp_00000346',
        eventId: 'e_leopard_cp_pv_00001106',
      });
    }
  }, [tabKey]);
  useEffect(() => {
    const handleStorageChange = (ev) => {
      if (ev.key === 'lego-bi-update-reportName' && ev.newValue) {
        editRef?.current?.refreshTableList({}, false);
      }
    };
    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);
  return (
    <div className={styles['commonTable']}>
      {tabKey === '1' ? (
        <BLMTemplatePage
          request={request}
          ref={editRef}
          schema={
            schemaEdit({ setModalView, handleDelete }) as TemplatePageSchema
          }
          tableActionsRight={
            <Link target="_blank" to={'/legoBI/edit?overallLayoutShow=false'}>
              <Button
                type="primary"
                onClick={() => {
                  clearLocaPageId();
                  blmAnalysisModuleClick({
                    eventId: 'e_leopard_cp_click_00001100',
                    pageId: 'p_leopard_cp_00000344',
                  });
                }}
              >
                新建报告
              </Button>
            </Link>
          }
          height={'calc(100vh - 126px)'}
          onBeforeFetch={(res) => {
            return {
              pageNum: res.pageNum,
              pageSize: res.pageSize,
              type: Number(tabKey),
              q: res.q,
            };
          }}
          onInputSearch={(val) => {
            blmAnalysisModuleClick({
              eventId: 'e_leopard_cp_click_00001102',
              pageId: 'p_leopard_cp_00000344',
              ext: {
                str0_e: val.q,
              },
            });
          }}
        />
      ) : (
        <BLMTemplatePage
          request={request}
          ref={shareRef}
          schema={
            schemaShare({ setModalView, handleDelete }) as TemplatePageSchema
          }
          height={
            pageListPermission ? 'calc(100vh - 126px)' : 'calc(100vh - 66px)'
          }
          onInputSearch={(val) => {
            blmAnalysisModuleClick({
              eventId: 'e_leopard_cp_click_00001280',
              pageId: 'p_leopard_cp_00000346',
              ext: {
                str0_e: val.q,
              },
            });
          }}
          onBeforeFetch={(res) => {
            return {
              pageNum: res.pageNum,
              pageSize: res.pageSize,
              type: Number(tabKey),
              q: res.q,
            };
          }}
        />
      )}
      {/* 分享报告弹窗 start */}
      {modalView.visible && (
        <Modal
          title={modalView.type === 'share' ? '分享报告' : '转让报告'}
          open={modalView.visible}
          onOk={handleOk}
          onCancel={handleCancel}
          width={modalView.type === 'share' ? 720 : 560}
          style={{ maxHeight: '70vh' }}
          closable={true}
          maskClosable={false}
        >
          <UserTransfer
            modalView={modalView}
            onModalOk={(e: any) => setSelectedKeys(e)}
            tabsType={tabKey}
          />
        </Modal>
      )}
    </div>
  );
};

export default CommonTable;
