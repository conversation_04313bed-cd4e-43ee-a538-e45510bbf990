import { Button, Space, Tag, Tooltip } from '@blmcp/ui';
import { Link } from '@umijs/max';
import { blmAnalysisModuleClick } from '@/utils/eventTracking';
import styles from './index.less';

export const schemaShare = ({ setModalView, handleDelete }) => {
  const schema = {
    // 模版页面配置标识，确保唯一性，建议子应用名称-页面
    templatePageId: 'qbi-legoListShare',

    // 框搜配置
    exactSearchConfig: {
      // 是否有框搜能力（即框搜输入框）
      hasExactSearch: true,
      // 框搜输入框配置
      schema: {
        // 服务侧查询字段
        queryKey: 'q',
        placeholder: '搜索报告',
        maxLength: 20,
      },
    },

    /** 表格相关配置 */
    tableConfig: {
      // 请求接口
      url: '/admin/v1/ai/chart-manager/queryReportPageList',
      // 请求方式
      method: 'post',
      paginationConfig: {
        defaultPageSize: 20,
      },
      // 表格列配置
      tableColumns: [
        {
          title: '报告名称',
          dataIndex: 'reportName',
          align: 'left',
          // width: 400,
          render: (text, record) => (
            <Link
              target="_blank"
              to={`/legoBI/view?reportId=${record.id}&publish=${
                record.status || 0
              }`}
              onClick={() => {
                blmAnalysisModuleClick({
                  eventId: 'e_leopard_cp_click_00003810',
                  pageId: 'p_leopard_cp_00000884',
                  ext: {
                    str0_e: record.id,
                    str1_e: 'share',
                  },
                });
              }}
            >
              {record.reportName}
            </Link>
          ),
        },
        {
          title: '分享者',
          dataIndex: 'shareName',
          align: 'left',
          // width: 230,
        },
        {
          title: '最近打开时间',
          dataIndex: 'openTime',
          align: 'left',
          // width: 180,
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'left',
          width: 110,
          fixed: 'right',
          render: (
            text: any,
            // record: { status: number; id: any; actionList: number[] },
            record,
          ) => (
            <div className={styles['operation']}>
              <Button
                type="link"
                className={styles['operation-itemShare']}
                style={{ margin: '0 10px 0 0' }}
                disabled={!record.actionList.includes(2)}
                onClick={() => {
                  blmAnalysisModuleClick({
                    eventId: 'e_leopard_cp_click_00003806',
                    pageId: 'p_leopard_cp_00000884',
                    ext: {
                      str0_e: 'list',
                      str1_e: record?.id,
                    },
                  });
                  setModalView({
                    visible: true,
                    type: 'share',
                    reportId: record?.id,
                  });
                }}
              >
                分享
              </Button>
              <Button
                type="link"
                style={{ margin: 0 }}
                className={styles['operation-itemShare']}
                disabled={!record.actionList.includes(4)}
                onClick={() => handleDelete(record.id)}
              >
                删除
              </Button>
            </div>
          ),
        },
      ],
    },
  };
  return schema;
};
