import { Button, Space, Tag, Tooltip } from '@blmcp/ui';
import { Link } from '@umijs/max';
import { blmAnalysisModuleClick } from '@/utils/eventTracking';
import styles from './index.less';

export const schemaEdit = ({ setModalView, handleDelete }) => {
  const schema = {
    // 模版页面配置标识，确保唯一性，建议子应用名称-页面
    templatePageId: 'qbi-legoListEdit',

    // 框搜配置
    exactSearchConfig: {
      // 是否有框搜能力（即框搜输入框）
      hasExactSearch: true,
      // 框搜输入框配置
      schema: {
        // 服务侧查询字段
        queryKey: 'q',
        placeholder: '搜索报告',
        maxLength: 20,
      },
    },

    /** 表格相关配置 */
    tableConfig: {
      // 请求接口
      url: '/admin/v1/ai/chart-manager/queryReportPageList',
      // 请求方式
      method: 'post',
      paginationConfig: {
        defaultPageSize: 20,
      },
      // 表格列配置
      tableColumns: [
        {
          title: '报告名称',
          dataIndex: 'reportName',
          align: 'left',
          // width: 304,
          render: (text, record) => (
            <Link
              target="_blank"
              to={`/legoBI/view?reportId=${record.id}&publish=${
                record.status || 0
              }`}
              onClick={() => {
                blmAnalysisModuleClick({
                  eventId: 'e_leopard_cp_click_00003810',
                  pageId: 'p_leopard_cp_00000884',
                  ext: {
                    str0_e: record.id,
                    str1_e: 'myEdit',
                  },
                });
                blmAnalysisModuleClick({
                  eventId: 'e_leopard_cp_click_00001104',
                  pageId: 'p_leopard_cp_00000344',
                  ext: {
                    str0_e: record.id,
                  },
                });
              }}
            >
              {record.reportName}
            </Link>
          ),
        },
        {
          title: '创建者',
          dataIndex: 'createUserName',
          align: 'left',
          // width: 140,
        },
        {
          title: '修改者',
          dataIndex: 'updateUserName',
          align: 'left',
          // width: 140,
        },
        {
          title: '发布状态',
          align: 'left',
          // width: 80,
          render: (text, record) => (
            <Space size="middle">
              {record.status === 1 ? (
                <span className={styles['successStatus']}>已发布</span>
              ) : (
                <span className={styles['defaultStatus']}>未发布</span>
              )}
            </Space>
          ),
        },
        {
          title: '最近打开时间',
          dataIndex: 'openTime',
          align: 'left',
          // width: 174,
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'left',
          width: 195,
          fixed: 'right',
          render: (
            text: any,
            // record: { status: number; id: any; actionList: number[] },
            record,
          ) => (
            <div className={styles['operation']}>
              <Button
                type="link"
                style={{ margin: '0 10px 0 0' }}
                className={styles['operation-item']}
                disabled={!record.actionList.includes(3)}
                onClick={() => {
                  blmAnalysisModuleClick({
                    eventId: 'e_leopard_cp_click_00003808',
                    pageId: 'p_leopard_cp_00000884',
                    ext: {
                      str0_e: 'list',
                      str1_e: record?.id,
                    },
                  });
                  setModalView({
                    visible: true,
                    type: 'transfer',
                    reportId: record?.id,
                  });
                }}
              >
                转让
              </Button>
              <Button
                className={styles['operation-item']}
                type="link"
                style={{ margin: '0 10px 0 0' }}
                disabled={!record.actionList.includes(1)}
                onClick={() => {
                  window.open(
                    `/qbi/legoBI/edit?reportId=${record.id}&overallLayoutShow=false`,
                  );
                  blmAnalysisModuleClick({
                    eventId: 'e_leopard_cp_click_00001098',
                    pageId: 'p_leopard_cp_00000344',
                    ext: {
                      int0_e: record.id,
                    },
                  });
                }}
              >
                编辑
              </Button>
              <Tooltip
                title={
                  !record.actionList.includes(2)
                    ? record.status === 1
                      ? '当前报告已转让，不能分享'
                      : '当前报告未发布'
                    : ''
                }
              >
                <Button
                  type="link"
                  className={styles['operation-item']}
                  style={{ margin: '0 10px 0 0' }}
                  disabled={!record.actionList.includes(2)}
                  onClick={() => {
                    blmAnalysisModuleClick({
                      eventId: 'e_leopard_cp_click_00003806',
                      pageId: 'p_leopard_cp_00000884',
                      ext: {
                        str0_e: 'list',
                        str1_e: record?.id,
                      },
                    });
                    setModalView({
                      visible: true,
                      type: 'share',
                      reportId: record?.id,
                    });
                  }}
                >
                  分享
                </Button>
              </Tooltip>
              <Button
                type="link"
                className={styles['operation-item']}
                disabled={!record.actionList.includes(4)}
                onClick={() => handleDelete(record.id)}
              >
                删除
              </Button>
            </div>
          ),
        },
      ],
    },
  };
  return schema;
};
