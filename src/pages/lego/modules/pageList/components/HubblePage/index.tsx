import { useEffect, useRef, useState } from 'react';
import {
  BLMTemplatePage,
  TemplatePageSchema,
} from '@blmcp/peento-businessComponents';
import { Modal, Button, Select, Spin, message } from '@blmcp/ui';
import { Link } from '@umijs/max';
import request from '@/utils/request';
import { clearLocaPageId } from '@/pages/lego/utils';
import {
  getAllConfig,
  getTemplateIncreaseQuantityList,
  setTemplateIncreaseQuantity,
} from '@/pages/lego/api/hubble';
import schema from './schema';
import styles from './index.less';

interface CommonTableProps {
  tabKey: number;
}
const CommonTable = ({ tabKey }: CommonTableProps) => {
  const tableRef = useRef<null | HTMLElement>(null);
  const currentRowKey = useRef('');
  const [selectBrands, setSelectBrands] = useState<number[]>([]);
  const [brandList, setBrandList] = useState<any[]>([]);
  const [brandModalOpen, setBrandModalOpen] = useState(false);

  const openSelectBrandModal = (record: any) => {
    currentRowKey.current = record.id;
    setBrandModalOpen(true);
    // 先清空后获取关联
    setSelectBrands([]);

    getTemplateIncreaseQuantityList({ reportId: record.id })
      .then((res: any) => {
        setSelectBrands(
          (res?.data?.releaseTenants || [{ tenantId: 1 }]).map(
            (v: any) => v.tenantId,
          ),
        );
      })
      .catch(() => {
        setSelectBrands([1]);
      });
  };

  // 放量
  const setIncreaseQuantityBrand = () => {
    Modal.confirm({
      title: '确认操作',
      content: '即将把模板同步给已选的品牌，是否继续',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        setTemplateIncreaseQuantity({
          reportId: currentRowKey.current,
          templateStatus: 3,
          modifiedTenants: selectBrands.map((v) => {
            const find = brandList.find((item) => item.value === v);
            return {
              tenantId: v,
              tenantName: find.label,
            };
          }),
        }).then((res) => {
          if (res.code === 1) {
            setBrandModalOpen(false);
            message.success('放量成功');
            tableRef?.current?.refreshTableList({}, false);
          }
        });
      },
    });
  };

  useEffect(() => {
    const handleStorageChange = (ev) => {
      if (ev.key === 'lego-bi-update-reportName' && ev.newValue) {
        tableRef?.current?.refreshTableList({}, false);
      }
    };
    window.addEventListener('storage', handleStorageChange);

    // 获取放量列表
    getAllConfig().then((res) => {
      setBrandList(
        (res.data || []).map((item: any) => {
          const disabled = item.tenantId === 1;
          return {
            label: item.name,
            value: item.tenantId,
            disabled,
          };
        }),
      );
    });

    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, []);

  return (
    <div className={styles['commonTable']}>
      <BLMTemplatePage
        request={request}
        ref={tableRef}
        schema={
          schema(tableRef, tabKey, {
            openSelectBrandModal,
          }) as TemplatePageSchema
        }
        height={'calc(100vh - 185px)'}
        onBeforeFetch={(res) => {
          return {
            pageNum: res.pageNum,
            pageSize: res.pageSize,
            type: tabKey,
            q: res.q,
          };
        }}
        tableActionsRight={
          <Link
            target="_blank"
            to={`/legoBI/edit?ownType=${tabKey === 1 ? 0 : 3}`}
          >
            <Button
              type="primary"
              onClick={() => {
                clearLocaPageId();
              }}
            >
              新建{tabKey === 1 ? '报告' : '模板'}
            </Button>
          </Link>
        }
      />
      {/* 放量组件 */}
      <Modal
        title="品牌放量"
        open={brandModalOpen}
        onOk={setIncreaseQuantityBrand}
        onCancel={() => setBrandModalOpen(false)}
      >
        <Spin spinning={!brandList.length || !selectBrands.length}>
          <div className={styles['brandChange']}>
            <span className="brandChange-span">放量品牌</span>
            <Select
              showSearch
              mode="multiple"
              placeholder="请选择"
              filterOption={(inputValue: string, option: any) =>
                option.label.indexOf(inputValue) > -1
              }
              value={selectBrands}
              options={brandList}
              onChange={(value) => {
                setSelectBrands(value);
              }}
            />
          </div>
        </Spin>
      </Modal>
    </div>
  );
};

export default CommonTable;
