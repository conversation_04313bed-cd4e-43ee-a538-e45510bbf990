/* share-url-hooks-disable-file */
import { Button, Form, message, Modal, Radio, Select, Space } from '@blmcp/ui';
import { Link } from '@umijs/max';
import { throttle } from 'lodash';
import {
  copyTemplate,
  deleteReport,
  pageListData,
  publishReportState,
  revokeTemplate,
} from '@/pages/lego/api/hubble';
import { copyTemplateClick } from '@/pages/lego/utils/common';
import updatePk from '@/utils/updatePk';
import ViewSelect from './viewSelect';
import styles from './index.less';

const status = {
  0: {
    t: '未发布',
    c: 'default',
  },
  1: {
    t: '已发布',
    c: 'success',
  },
  2: {
    t: '测试中',
    c: 'warning',
  },
  3: {
    t: '放量中',
    c: 'warning',
  },
  4: {
    t: '已回撤',
    c: 'default',
  },
  5: {
    t: '已全量',
    c: 'success',
  },
  6: {
    t: '已下线',
    c: 'default',
  },
};

export default function (ref: any, type: number, { openSelectBrandModal }) {
  const [form] = Form.useForm();

  // 删除报告
  const deleteReportClick = throttle(
    (record: any) => {
      Modal.confirm({
        title: '您确认要删除吗？',
        okText: '确定',
        cancelText: '取消',
        autoFocusButton: null,
        content: '删除后，当前报告将无法恢复',
        onOk: () => {
          deleteReport({ reportId: record.id, type }).then((res) => {
            if (res && res?.code === 1) {
              // 删除成功，重新调用列表接口
              message.success('删除成功');
              ref?.current?.refreshTableList({}, false);
            }
          });
        },
      });
    },
    1000,
    { trailing: false },
  );

  // 发布自己的模板到公共模板上
  const publishTemplateClick = throttle(
    (record: any) => {
      Modal.confirm({
        title: '确认操作',
        content: '即将把报告发布成模板，发布后将创建模板副本，是否继续？',
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          copyTemplate({ reportId: record.id, source: 4 }).then((res) => {
            if (res?.code === 1) {
              message.success('发布成功');
              ref?.current?.refreshTableList({}, false);
            }
          });
        },
        onCancel: () => {},
      });
    },
    1000,
    { trailing: false },
  );

  // 上线、下线、回撤 弹窗
  const publishStatusClick = throttle(
    (record: any, templateStatus: number, info = '操作成功') => {
      const map: any = {
        // 1: '即将把报告发布成模板，发布后将创建模板副本，是否继续',
        2: '即将把模板同步至品牌【约约出行】，是否继续',
        4: '即将回撤下线报告，sp后台将不可见，该操作不可逆，是否继续',
        5: '即将把模板放量至所有品牌，请谨慎操作',
        6: '即将下线报告，sp后台将不可见，该操作不可逆，是否继续',
      };
      Modal.confirm({
        title: '确认操作',
        content: map[templateStatus],
        okText: '确定',
        cancelText: '取消',
        onOk: () => {
          const request = [4, 6].includes(templateStatus)
            ? revokeTemplate
            : publishReportState;
          request({ reportId: record.id, templateStatus }).then((res) => {
            if (res?.code === 1) {
              // 发布成功，重新调用列表接口
              message.success(info);
              ref?.current?.refreshTableList({}, false);
            }
          });
        },
      });
    },
    1000,
    { trailing: false },
  );

  // 上线
  const templateOnlineClick = throttle(
    (
      record: any,
      formVal: any,
      selectListInfo,
      templateStatus: number,
      info = '操作成功',
    ) => {
      let selectList = [...new Set(selectListInfo.map((i) => i.valueId))];
      const { dataAuthMode, showInList } = formVal;
      const map: any = {
        // 1: '即将把报告发布成模板，发布后将创建模板副本，是否继续',
        2: '即将把模板同步至品牌【约约出行】，是否继续',
        4: '即将回撤下线报告，sp后台将不可见，该操作不可逆，是否继续',
        5: '即将把模板放量至所有品牌，请谨慎操作',
        6: '即将下线报告，sp后台将不可见，该操作不可逆，是否继续',
      };
      Modal.confirm({
        title: '确认操作',
        content: map[templateStatus],
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          let selectListInfoCopy: any = [];
          const promiseArr: any = [];
          selectListInfo?.forEach((item) => {
            if (item.type === 'FK') {
              promiseArr.push(
                pageListData({
                  pageSize: 9999,
                  pageNum: 1,
                  functionKey: item.valueId,
                }).then((res) => {
                  if (res?.code === 1 && res?.data?.items?.length) {
                    selectListInfoCopy.push({
                      type: item.type,
                      name: item.name,
                      pk: res.data.items[0].pageKey ?? '',
                      functionKey: item.valueId,
                    });
                  }
                }),
              );
            } else {
              selectListInfoCopy.push({
                type: item.type,
                name: item.name,
                pk: item.valueId,
                functionKey: '',
              });
            }
          });
          await Promise.all(promiseArr);

          const params = {
            reportId: record.id,
            templateStatus,
            dataAuthMode,
            showInList,
            pageMarks: selectList,
          };
          publishReportState(params)
            .then((res) => {
              if (res?.code === 1) {
                // 成功之后调用鉴权接口
                // 测网传dev， 正网环境传pre
                let typeURL = 'dev';
                let hostnameURL = window.location.hostname ?? '';
                const list = [
                  'hubble-pre.yueyuechuxing.cn',
                  'hubble-pre2.yueyuechuxing.cn',
                  'hubble.yueyuechuxing.cn',
                ];
                if (list.includes(hostnameURL)) {
                  typeURL = 'pre';
                }
                updatePk(typeURL, selectListInfoCopy)
                  .then((res) => {
                    if (
                      [1, 9020, 9021].includes(res[0]?.data?.code) &&
                      [1, 9020, 9021].includes(res[1]?.data?.code) &&
                      [1, 9020, 9021].includes(res[2]?.data?.code)
                    ) {
                      // 鉴权通过发布成功，重新调用列表接口
                      message.success(info);
                      ref?.current?.refreshTableList({}, false);
                    } else {
                      let msgText = '请求错误请联系相关人员查看';
                      try {
                        res?.forEach((item) => {
                          if (
                            [1, 9020, 9021].includes(item?.data?.code) &&
                            item?.data?.msg
                          ) {
                            msgText = item.data.msg;
                            throw Error();
                          }
                        });
                      } catch (err) {}
                      message.error(msgText);
                    }
                  })
                  .catch((e) => {});
              }
            })
            .catch((e) => {
              console.log(e);
            });
        },
      });
    },
    1000,
    { trailing: false },
  );

  // 模板上线弹窗
  const templateOnlineModal = throttle(
    async (record: any) => {
      let selectListInfo: any = [];

      Modal.confirm({
        title: '模板上线',
        icon: null,
        width: '560px',
        closable: true,
        content: (
          <Form
            form={form}
            colon={false}
            labelAlign={'left'}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            initialValues={{ dataAuthMode: 0, showInList: 0, pageMarks: [] }}
          >
            <Form.Item
              name="dataAuthMode"
              label="数据集鉴权"
              extra="配置模板是否进行数据集权限校验，常规场景统一需要鉴权"
              rules={[{ required: true, message: '请选择数据集鉴权' }]}
            >
              <Radio.Group>
                <Radio value={0}>鉴权</Radio>
                <Radio value={1}>不鉴权</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              name="showInList"
              label="公共模板展示"
              extra="配置模板是否在公共模板列表展示，常规场景建议统一展示"
              rules={[{ required: true, message: '请选择公共模板展示' }]}
            >
              <Radio.Group>
                <Radio value={0}>展示</Radio>
                <Radio value={1}>不展示</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              name="pageMarks"
              label="SP后台资源关联"
              extra="配置模板与SP后台资源的关联关系，需要从基础运营获取"
            >
              <ViewSelect
                onChange={(value, options) => {
                  selectListInfo = options;
                  form.setFieldsValue({ pageMarks: value });
                }}
              ></ViewSelect>
            </Form.Item>
          </Form>
        ),
        okText: '提交上线',
        cancelText: '取消',
        onOk: () => {
          return new Promise((resolve, reject) => {
            form
              .validateFields()
              .then((values) => {
                templateOnlineClick(
                  record,
                  form.getFieldsValue(),
                  selectListInfo,
                  2,
                  '发布成功，请前往SP后台【约约出行】进行测试',
                );
                // 重置表单字段到初始值
                form.resetFields();
                resolve();
              })
              .catch((info) => {
                console.log('请完善表单信息:', info);
                reject();
              });
          });
        },
        onCancel: () => {
          // 重置表单字段到初始值
          form.resetFields();
        },
      });
    },
    1000,
    { trailing: false },
  );

  return {
    // 模板页面配置标识，确保唯一性，建议子应用名称-页面
    templatePageId: 'hubble-qbi-legoList',

    // 框搜配置
    exactSearchConfig: {
      // 是否有框搜能力（即框搜输入框）
      hasExactSearch: true,
      // 框搜输入框配置
      schema: {
        // 服务侧查询字段
        queryKey: 'q',
        placeholder: type === 1 ? '搜索报告' : '搜索模板',
        maxLength: 20,
      },
    },

    /** 表格相关配置 */
    tableConfig: {
      // 请求接口
      url: '/lego/admin/v1/ai/chart-manager/queryReportPageList',
      // 请求方式
      method: 'post',
      paginationConfig: {
        defaultPageSize: 20,
      },
      // 表格列配置
      tableColumns: [
        {
          title: type === 1 ? '报告名称' : '模板名称',
          dataIndex: 'reportName',
          align: 'left',
          // width: 220,
          render: (text, record) => (
            <Link
              target="_blank"
              to={`/legoBI/view?reportId=${record.id}&publish=${
                record.status || 0
              }`}
            >
              {record.reportName}
            </Link>
          ),
        },
        {
          title: '创建者',
          dataIndex: 'createUserName',
          align: 'left',
          width: 90,
        },
        {
          title: '修改者',
          dataIndex: 'updateUserName',
          align: 'left',
          width: 90,
        },
        {
          title: type === 1 ? '报告状态' : '模板状态',
          align: 'left',
          width: 100,
          render: (text, record) => (
            <Space size="middle">
              <span
                className={
                  styles[status[record.templateStatus || record.status].c]
                }
              >
                {status[record.templateStatus || record.status].t}
              </span>
            </Space>
          ),
        },
        {
          title: '创建时间',
          dataIndex: 'createTime',
          align: 'left',
          width: 170,
        },
        {
          title: '最近修改时间',
          dataIndex: 'updateTime',
          align: 'left',
          width: 170,
        },
        {
          title: '操作',
          dataIndex: 'operation',
          align: 'left',
          width: 273,
          fixed: 'right',
          render: (
            text: any,
            // record: { status: number; id: any; actionList: number[] },
            record,
          ) => (
            <div className={styles['operation']}>
              {(type === 1 && (
                // 如果是自己创建的
                <>
                  <Button
                    type="link"
                    onClick={() => {
                      window.open(`/qbi/legoBI/edit?reportId=${record.id}`);
                    }}
                  >
                    编辑
                  </Button>
                  {
                    <Button
                      type="link"
                      onClick={() =>
                        copyTemplateClick(record, !!record.status ? 3 : 2)
                      }
                    >
                      复制报告
                    </Button>
                  }
                  {!!record.status && (
                    <Button
                      type="link"
                      onClick={() => publishTemplateClick(record)}
                    >
                      发布模板
                    </Button>
                  )}
                  <Button onClick={() => deleteReportClick(record)} type="link">
                    删除
                  </Button>
                </>
              )) || (
                <>
                  {[0].includes(record.templateStatus) && (
                    <Button
                      type="link"
                      onClick={() => {
                        window.open(`/qbi/legoBI/edit?reportId=${record.id}`);
                      }}
                    >
                      编辑
                    </Button>
                  )}
                  <Button
                    type="link"
                    onClick={() => copyTemplateClick(record, 0, '模板')}
                  >
                    复制模板
                  </Button>
                  {[1].includes(record.templateStatus) && (
                    <Button
                      type="link"
                      onClick={() => templateOnlineModal(record)}
                    >
                      模板上线
                    </Button>
                  )}
                  {[0, 1, 4].includes(record.templateStatus) && (
                    <Button
                      type="link"
                      onClick={() => deleteReportClick(record)}
                    >
                      删除
                    </Button>
                  )}
                  {[2].includes(record.templateStatus) && (
                    <Button
                      type="link"
                      onClick={() => publishStatusClick(record, 4)}
                    >
                      回撤
                    </Button>
                  )}
                  {[2, 3].includes(record.templateStatus) && (
                    <Button
                      type="link"
                      onClick={() => {
                        openSelectBrandModal(record);
                      }}
                    >
                      模板放量
                    </Button>
                  )}
                  {[3].includes(record.templateStatus) && (
                    <Button
                      type="link"
                      onClick={() => publishStatusClick(record, 5)}
                    >
                      模板全量
                    </Button>
                  )}
                  {[3, 5].includes(record.templateStatus) && (
                    <Button
                      type="link"
                      onClick={() => publishStatusClick(record, 6)}
                    >
                      下线
                    </Button>
                  )}
                </>
              )}
            </div>
          ),
        },
      ],
    },
  };
}
