import { Pie<PERSON>hart, TableSheet, Table } from '../../libraryMaterials';
import styles from './index.less';

const ViewPage = () => {
  const config = [
    {
      type: 'table',
      component: <Table />,
    },
    {
      type: 'pie',
      component: <PieChart />,
    },
    // {
    //   type: 'tablesheet',
    //   component: <TableSheet data={mockSheet.data}></TableSheet>,
    // },
  ];

  return (
    <div className={styles.wrap}>
      {config.map((item) => {
        return (
          <div key={item.type} className={styles['item']}>
            {item.component}
          </div>
        );
      })}
    </div>
  );
};

export default ViewPage;
