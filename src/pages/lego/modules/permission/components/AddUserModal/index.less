.userModal {
  &-content {
    // height: 300px;
    padding-top: 10px;

    &_selected {
      height: 20px;
      margin: 5px 0 15px;
    }

    &_tree {
      border: 1px solid rgba(37, 52, 79, 0.12);
      border-radius: 6px;
      // height: 100%;
      padding: 10px 0 10px 10px;
    }
  }

  &-footer {
    padding: 10px 20px;
    text-align: right;
  }
}

.SearchOutlinedIcon {
  color: rgba(0, 0, 0, 0.3);
}


.permissionModal {
  max-height: calc(70vh - 100px);
  overflow: auto;
  padding: 0 20px;
}

.permissionContent {
  height: 210px;
  overflow: auto;

  &-spin {
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
  }
}