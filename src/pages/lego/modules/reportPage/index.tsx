import { Spin } from '@blmcp/ui';
import { useEffect, useState } from 'react';
import { useParams } from 'umi';
import { blmAnalysisPageView } from '@/utils/eventTracking';
import { LegoReportIdKey, clearLocaPageId } from '@/pages/lego/utils';
import LegoRenderer from '../viewPage/LegoRenderer';
import { getPageStructure } from '../../api/index';

const hideFilterEditPk = [
  'financeBusinessAnalysis',
  'driverStratificationResultChart',
  'driverLayeredList',
  'driverStratificationResultFK',
  'driverConversionFunnelAnalysisFK',
  'driverDailyRetentionAnalysisFK',
  'driverMonthlyRetentionAnalysisFK',
  'driverWeekRetentionAnalysisFK',
  'insightIntoOperData',
  'marketingInsights',
];

export default function () {
  const [data, setData] = useState<any>();
  const params = useParams();
  const [pageMark, setPageMark] = useState('');

  useEffect(() => {
    window.$legoPageMontTime =
      Date.now() - (window.$routerBeforeEachTime || window.htmlStartTime);

    setData(null);
    blmAnalysisPageView({
      pageId: 'p_leopard_cp_00000668',
      eventId: 'e_leopard_cp_pv_00003084',
      ext: {
        str0_e: params.pageName,
      },
    });

    setPageMark('');
    getPageStructure({
      pageMark: params.pageName,
      publishStatus: 1,
    })
      .then((res) => {
        if (res.code === 1) {
          sessionStorage.setItem(LegoReportIdKey, res.data.reportId);
          setPageMark(res?.data?.pageMark);
        } else {
          clearLocaPageId();
        }
        setData(res as any);
      })
      .catch((res) => {
        setData(res as any);
      });

    return function () {
      clearLocaPageId();
    };
  }, [params.pageName]);

  if (!data) {
    return <Spin size="large" className="lego-page-loading" />;
  }

  return (
    <LegoRenderer
      assets={true}
      data={data}
      edit={!hideFilterEditPk.some((v) => pageMark?.includes?.(v))}
      walkerComponents={(item: any, parent: any) => {
        if (
          'financeBusinessAnalysis' === params.pageName &&
          item.componentName === 'CapacityCompanyFilter'
        ) {
          parent.props.style.display = 'none';
        }
      }}
    ></LegoRenderer>
  );
}
