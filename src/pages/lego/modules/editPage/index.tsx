import React, { useEffect } from 'react';
import { legoInit } from '@blmlc/lego-init';
// import { project, config } from '@alilc/lowcode-engine';
const { project } = window.AliLowCodeEngine || {};
import { blmAnalysisPageView } from '@/utils/eventTracking';
import { clearLocaPageId } from '../../utils';
import { store } from '../../hooks/useComponent';
import icons from '../../libraryMaterials/module/Icons';
import {
  editorInit,
  assetsConfig,
} from './plugins/plugin-editor-init/pluginEditorInit.ts';
import ComponentPanelPlugin from './plugins/plugin-component-panel';
import SaveSamplePlugin from './plugins/plugin-save-sample';
import HeadSamplePlugin from './plugins/plugin-head-sample';
import ComponentIconPlugin from './plugins/plugin-component-icon';
import './global.less';
import '../../libraryMaterials/setter';
import './components/AddCalculateFields';
import { unRegisterFunction } from './plugins/plugin-head-sample/event';
import './info.css';

function findParent(_node: HTMLElement, className) {
  let node = _node;
  let index = 0;
  while (node && !node?.classList?.contains(className) && index < 20) {
    node = node.parentNode;
    index++;
  }
  if (node?.classList.contains(className)) {
    return node;
  }
}

function isInViewport(element, container) {
  const containerRect = container.getBoundingClientRect();
  const elementRect = element.getBoundingClientRect();

  // 检查元素是否在容器的边界内
  return (
    elementRect.top >= containerRect.top &&
    elementRect.left >= containerRect.left &&
    elementRect.bottom <= containerRect.bottom &&
    elementRect.right <= containerRect.right
  );
}

const Lowcode = () => {
  const registerLegoPlugins = async (plugins: any) => {
    plugins.delete('PreviewSamplePlugin');
    await plugins.register(ComponentIconPlugin, {}, { override: true });
    await plugins.register(ComponentPanelPlugin, { icons }, { override: true });
    await plugins.register(HeadSamplePlugin, {}, { override: true });
    await plugins.register(SaveSamplePlugin, {}, { override: true });
  };

  const initCode = async () => {
    const defaultExtra = [
      {
        type: 'jsText',
        content: 'window.blmRequest=parent.blmRequest;',
      },
    ];

    let extraEnvironment = [
      `https://${
        window.cdnPubCosUrl || 'webstatic'
      }.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/css/antd-umdV5.15.3-440b813236.css`,
      `https://${
        window.cdnPubCosUrl || 'webstatic'
      }.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/js/antd-umdV1.11.10-2bf27bc00d.js`,
      `https://${
        window.cdnPubCosUrl || 'webstatic'
      }.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/js/antdIcons-umdV5.5.1-05b7122c56.js`,
      `https://${
        window.cdnPubCosUrl || 'webstatic'
      }.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/css/blmcpUi-umdV1.1.21-3164c60f39.css`,
      `https://${
        window.cdnPubCosUrl || 'webstatic'
      }.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/js/blmcpUi-umdV1.1.21-b15876bc61.js`,
      `https://${
        window.cdnPubCosUrl || 'webstatic'
      }.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/css/blmBusinessComponents-umdV0.0.68-5bdcc48ea9.css`,
      `https://${
        window.cdnPubCosUrl || 'webstatic'
      }.yueyuechuxing.cn/yueyue/admin/static_umd/release/cp/js/blmBusinessComponents-umdV0.0.68-c443472c19.js`,
    ];
    const qiankunDom = document.getElementsByTagName('qiankun-head')?.[0];
    if (qiankunDom) {
      const htmlStr = qiankunDom.innerHTML;
      const scripts =
        htmlStr.match(/script\s([^]*?)replaced by import-html-entry/g) || [];
      const links = htmlStr.match(/\/\*\shttp[^]*?.css\s\*\//g) || [];
      const filterScript = scripts
        .filter((f) =>
          [
            'antd-umd',
            'antdIcons-umd',
            'blmcpUi-umd',
            'blmBusinessComponents-umd',
          ].some((k) => f.includes(k)),
        )
        .map((v) =>
          v.replace(/script|replaced\s+by\s+import-html-entry|\s/g, ''),
        )
        .filter((f) => /^https:\/\/[^]*?.js$/.test(f));
      const filterLink = links
        .filter((f) =>
          ['antd-umd', 'blmcpUi-umd', 'blmBusinessComponents-umd'].some((k) =>
            f.includes(k),
          ),
        )
        .map((v) => v.replace(/\/\*|\s|\*\//g, ''))
        .filter((f) => /^https:\/\/[^]*?.css$/.test(f));

      if (filterScript.length === 4 && filterLink.length === 3) {
        extraEnvironment = [...filterLink, ...filterScript];
      } else {
        console.log('extraEnvironment 获取错误, 配置文件丢失');
      }
    } else {
      console.log('extraEnvironment 获取错误，不存在qiankunDom');
    }

    legoInit({
      dom: 'lce-container',
      projectName: 'legoBI',
      params: {
        deviceClassName: 'lego_deviceClassName',
        realTimeHandleSelectNode(node, { observed, ...ohter } = {}) {
          // 筛选区域不处理
          if (
            [
              'ListFilter',
              'DatePickerFilter',
              'RangeOfIntervalsFilter',
              'InputFilter',
            ].includes(node.schema.children?.[0]?.componentName)
          ) {
            return true;
          }
          const iframe = document.getElementById(
            'lego-iframe-simulatorRenderer',
          );
          const legoFilterDom =
            iframe?.contentWindow?.document?.querySelector?.(
              '.legoFilterDefaultRow',
            );
          const clientHeight = legoFilterDom?.clientHeight || 0;
          if (
            observed &&
            observed.top < -(observed.height - clientHeight - 20)
          ) {
            return false;
          }

          // tab 组件内部判断
          const nodeDom = node.getDOMNode();
          const tabDom = findParent(nodeDom, 'xtab-item-wrapper');
          if (tabDom && !isInViewport(nodeDom, tabDom)) {
            return false;
          }
        },
        customizeIgnoreSelectors() {
          return []; // 解决编辑input组件忽略点击问题
        },
        extraEnvironment: ([] as string[]).concat(
          defaultExtra,
          extraEnvironment,
        ),
        filterComponents: [],
      },
      editorInitConfig: {
        assets: assetsConfig,
        initFn: editorInit,
      },
      registerPlugins: registerLegoPlugins,
    });
  };

  useEffect(() => {
    store.clear();

    (async () => {
      initCode();
      unRegisterFunction();
    })();
    blmAnalysisPageView({
      pageId: 'p_leopard_cp_00000400',
      eventId: 'e_leopard_cp_pv_00001782',
    });
    return function () {
      project?.removeDocument(project?.currentDocument as any);
      clearLocaPageId();
      store.clear();
      unRegisterFunction();
    };
  }, []);

  return <div id="lce-container"></div>;
};
export default Lowcode;
