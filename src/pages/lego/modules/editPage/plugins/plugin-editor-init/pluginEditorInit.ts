import { message } from '@blmcp/ui';
import {
  LegoReportIdKey,
  transformServerToSchema,
  getDesignBody,
} from '@/pages/lego/utils';
import { createReportInitStatus, getPageStructure } from '@/pages/lego/api';
import { reportBusinessMonitor, blmMicrofs } from '@/utils/eventTracking';
import { getDefaultProjectSchema } from '../../services/mockService';
import createAssets from '../../services/getAssets';
import VisibleFilter from '../../../../utils/visibleFilter';

export const assetsConfig = createAssets();

// const insertBeforeAlert = () => {
//   const center = document.querySelector('.lc-workbench-center');
//   const area = document.querySelector('.lc-main-area');
//   const newElement = document.createElement('div');
//   newElement.classList = ['ant-alert ant-alert-info BLMAlert_Antd css-iksrmj'];
//   newElement.setAttribute('data-show', true);
//   newElement.setAttribute('role', 'alert');
//   newElement.innerHTML = `<span role="img" class="anticon ant-alert-icon"><svg fill="currentColor" version="1.1" width="14" height="14" viewBox="0 0 16 16"><defs><clipPath id="master_svg0_44_1044"><rect x="0" y="0" width="16" height="16" rx="0"></rect></clipPath></defs><g clip-path="url(#master_svg0_44_1044)"><g><path d="M8,15C11.866,15,15,11.866,15,8C15,4.13401,11.866,1,8,1C4.13401,1,1,4.13401,1,8C1,11.866,4.13401,15,8,15ZM7.0995,4.9950399999999995C7.04623,4.46228,7.46459,4,8,4C8.535409999999999,4,8.953769999999999,4.46228,8.900500000000001,4.9950399999999995L8.54975,8.50248C8.52151,8.78492,8.28384,9,8,9C7.71616,9,7.47849,8.78492,7.45025,8.50248L7.0995,4.9950399999999995ZM8,10C7.44772,10,7,10.44772,7,11C7,11.5523,7.44772,12,8,12C8.55228,12,9,11.5523,9,11C9,10.44772,8.55228,10,8,10Z" fill-rule="evenodd"></path></g></g></svg></span><div class="ant-alert-content"><div class="ant-alert-message">编辑报告后，请到“我创建的”页签下查看</div>`;
//   center.insertBefore(newElement, area);
// };

export const editorInit = async (ctx: any) => {
  const { project, config, event } = ctx;
  /**-- 新建报表，或者更新报表 start --**/
  // 通过querystring
  const reportId =
    new URLSearchParams(location.search.slice(1)).get('reportId') ??
    sessionStorage.getItem(LegoReportIdKey);
  let minAuthLevel: any;
  if (reportId) {
    // 编辑
    try {
      const res = await getPageStructure({
        reportId,
        publishStatus: 0,
      }).catch((v) => v);

      // 权限判断
      const actionList = (
        typeof res?.data?.actionList === 'string'
          ? res?.data?.actionList.split(',')
          : res?.data?.actionList || []
      ).map((v: string) => +v);

      if (
        res.code === 401 ||
        res.data === 173403 ||
        (res?.data?.actionList &&
          (!actionList.includes(5) || !actionList.includes(1)))
      ) {
        message.error('暂无该报告编辑权限，请联系管理员授权');
        window.location.href = '/qbi/403';
        return false;
      }

      if (res.code !== 1) {
        message.error(res.tips || res.msg);
        return;
      }

      // 设置报告名称
      config.set('pageName', res.data.reportName);
      config.set('isTemplate', !!res.data.ownType);
      config.set('reportId', res.data.reportId);
      minAuthLevel = res.data.minAuthLevel;

      // // 如果是模板复制进来的，插入一句话
      // if (
      //   new URLSearchParams(location.search.slice(1)).get('isTemplate') === '1'
      // ) {
      //   insertBeforeAlert();
      // }
      if (!res.data.schema) {
        // 没有数据走新建默认模板
        const schema = await getDefaultProjectSchema();
        project.importSchema(schema as any);
      } else {
        const schema = await transformServerToSchema(res.data);
        // 加载 schema
        project.importSchema(schema as any);
      }
    } catch (e) {
      reportBusinessMonitor(
        'legoBI-schema-error',
        { error: e, type: 'edit' },
        'error',
      );
      // const schema = await getDefaultProjectSchema();
      // project.importSchema(schema as any);
    }
  } else {
    const ownType = Number(
      new URLSearchParams(location.search.slice(1)).get('ownType') || '',
    );
    // 新建
    const res = await createReportInitStatus(ownType);
    sessionStorage.setItem(LegoReportIdKey, `${res?.data?.id}`);
    // 设置默认schema
    config.set('pageName', res.data.reportName);
    config.set('isTemplate', ownType !== 0);
    config.set('reportId', res.data.id);
    event.emit('savePage.init');
    const schema = await getDefaultProjectSchema();
    project.importSchema(schema as any);
  }

  try {
    // 判断是否只有筛选器，如果是则插入提示
    const schema = project.exportSchema();
    const { componentsTree = [] } = schema || {};
    const { children = [] } = componentsTree[0] || {};
    setTimeout(() => {
      if (children.length <= 1) {
        getDesignBody()?.classList.remove('designerDefaultText_control');
      } else {
        getDesignBody()?.classList.add('designerDefaultText_control');
      }

      VisibleFilter.init();
      VisibleFilter.set(minAuthLevel);
    }, 200);

    // 调整按钮文本
    const btnDom = document.getElementById('legoCopyTemplateButton');
    if (btnDom) {
      btnDom.innerText = config.get('isTemplate') ? '复制模板' : '复制报告';
    }
  } catch (e) {
    // console.error('schemaError', e);
  }

  blmMicrofs();

  project.onSimulatorRendererReady(() => {
    event.emit('savePage.init');
  });

  /**-- 新建报表，或者更新报表 end--**/
};
