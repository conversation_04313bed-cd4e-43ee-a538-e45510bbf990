import { IPublicModelPluginContext } from '@alilc/lowcode-types';
import { useEffect, useState } from 'react';
import { debounce } from 'lodash';
import { message } from '@blmcp/ui';
import { ReactComponent as ExportIconWhite } from '@/assets/lego/export.svg';
import { getCurrnetPageId, walkerComponents } from '@/pages/lego/utils';
import { LegoExport } from '@/pages/lego/components/LegoExport';
import { isHubble } from '@/utils/hubble';
import useComponent from '@/pages/lego/hooks/useComponent';
import { globalCache } from '@/pages/lego/utils/cache';

import { blmAnalysisModuleClick } from '@/utils/eventTracking';
import styles from './index.less';

interface FilterType {
  bizColumn: string;
  value: string[];
  displayValue: string;
  overflowTooltip: boolean;
}

function ExportIcon(props) {
  const [searchParams, setSearchParams] = useState({});
  const [otherParams, setOtherParams] = useState({});
  let chartId = null;
  walkerComponents(props.node.schema, (item) => {
    chartId = item.id;
  });
  const [meta] = useComponent(chartId);
  useEffect(() => {
    const exportBtn = document.querySelector('.export-container');
    if (exportBtn) {
      exportBtn.style = 'width: 1px; height: 0; overflow: hidden;';
    }
  }, [props.node.schema]);
  const hidden = [
    'XTab',
    'Text',
    'IndexCard',
    'Card',
    'DatePickerFilter',
    'ListFilter',
    'RangeOfIntervalsFilter',
    'InputFilter',
  ];
  if (
    !meta.isRender ||
    props?.meta === undefined ||
    hidden.includes(props.meta.componentName) ||
    isHubble
  ) {
    return null;
  }
  const exportData = debounce(() => {
    blmAnalysisModuleClick({
      pageId: 'p_leopard_cp_00000400',
      eventId: 'e_leopard_cp_click_00003238',
      ext: {
        str0_e: getCurrnetPageId(),
      },
    });
    walkerComponents(props.node.schema, (item) => {
      try {
        if (Object.keys(globalCache?.relationshipMap).length > 0) {
          const oldValue = globalCache?.relationshipMap?.[item.id]?.[2];
          const query = JSON.parse(oldValue);
          const filterInfo = query?.filterInfo ?? [];
          const filters: FilterType[] = [];
          filterInfo.forEach((filter) => {
            filters.push({
              bizColumn: filter.key,
              value: filter.fieldValue,
              displayValue: filter.fieldLabel?.join('、'),
              overflowTooltip: true,
            });
          });

          setSearchParams(filters);
          setOtherParams(query);
          document.querySelector('.export-container button')?.click();
        } else {
          message.error('数据未加载完成，请稍后重试');
        }
      } catch (e: any) {
        console.warn(e);
      }
    });
  }, 300);

  return (
    <div className={styles['icon']}>
      <ExportIconWhite
        onClick={() => {
          exportData();
        }}
      />
      <LegoExport searchParams={searchParams} otherParams={otherParams} />
    </div>
  );
}

// 保存功能示例
const DiyComponentIconPlugin = (ctx: IPublicModelPluginContext) => {
  return {
    async init() {
      const { config } = ctx;
      const diyActions = config.get('diyActions') ?? [];
      diyActions.push(ExportIcon);
      config.set('diyActions', diyActions);
    },
  };
};
DiyComponentIconPlugin.pluginName = 'DiyComponentIconPlugin';
export default DiyComponentIconPlugin;
