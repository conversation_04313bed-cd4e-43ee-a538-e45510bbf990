.lowcode-plugin-logo {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-end;
  width: 306px;
  align-items: center;

  .logo {
    display: block;
    width: 139px;
    height: 26px;
    cursor: pointer;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
  }

  .scenario-name {
    display: block;
    margin-left: 20px;
    margin-right: 5px;
    font-size: 15px;
  }

  .info-dropdown {
    display: block;
  }

  .lowcode-plugin-logo-edit {
    border: 0;
    outline: none;
    font-size: 15px;
    color: #000;
    display: inline-block;
    overflow: hidden;
    white-space: nowrap;
    font-weight: 500;
  }

  .lowcode-plugin-logo-state {
    font-size: 13px;
    font-weight: normal;
    color: rgba(0, 0, 0, 0.3);
    padding-left: 12px;
    display: inline-block;
    width: 110px;
    white-space: nowrap;
  }

  .lowcode-plugin-logo-edit-icon {
    margin-left: 10px;
  }

  .lowcode-plugin-logo-left {
    cursor: pointer;
    margin-right: 8px;
  }
}