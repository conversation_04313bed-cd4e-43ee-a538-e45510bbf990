import {
  IPublicModelNode,
  IPublicTypePropChangeOptions,
} from '@alilc/lowcode-types';
import axios, { Canceler } from 'axios';
import dayjs from 'dayjs';
// import { event, editor, project, config } from '@alilc/lowcode-engine';
const { event, editor, project, config } = window.AliLowCodeEngine || {};
import { globalCache } from '@/pages/lego/utils/cache';
// import { message } from '@blmcp/ui';
import {
  saveComponentData,
  savePageStructure,
  saveComponentsData,
} from '@/pages/lego/api';
import {
  getComponentMetaByName,
  getProjectSchema,
  transformSchemaToServer,
  transformDataSetConfig,
  walkerComponents,
  getCurrnetPageId,
} from '@/pages/lego/utils';
import { store } from '@/pages/lego/hooks/useComponent';
import VisibleFilter from '../../../../utils/visibleFilter';

let savePageStructureToken: Canceler | null;
let savePageStructureTime: number | undefined;
// let saveComponentTokens: { [key: string]: Canceler | null } = {};
let savePageCallbacks: (() => any)[] = [];
let onChangeNodePropTime: number | undefined;

enum componentName {
  '指标卡' = 1,
  '表格',
  '图表',
  '筛选器',
  '文本',
  '选项卡',
  'DateFilterGlobal' = '时间筛选器(内置)',
  'CityFilterGlobal' = '城市筛选器(内置)',
  'CapacityCompanyFilter' = '运力筛选器(内置)',
  'BrandFilter' = '品牌筛选器(内置)',
}

// 关联组件数据集信息 没用了
// export const callSaveComponentData = function (
//   node: IPublicModelNode,
//   params: any,
// ) {
//   // if (saveComponentTokens[node.id]) {
//   //   saveComponentTokens[node.id]!('取消请求');
//   // }
//   return saveComponentData(
//     {
//       ...params,
//       reportId: getCurrnetPageId(),
//     },
//     // new axios.CancelToken((c) => {
//     //   if (node.id) {
//     //     saveComponentTokens[node.id] = c;
//     //   }
//     // }),
//   ).then((res) => {
//     // 如果当前没有组件id，则保存组件后需重新赋予id
//     if (params.elementId === undefined) {
//       store.merge(node.id, {
//         id: node.id,
//         elementId: res.data.id,
//       });
//     }
//   });
//   // .finally(() => {
//   //   saveComponentTokens[node.id] = null;
//   // });
// };

const unRegisters: ((() => void) | undefined)[] = [];

export const unRegisterFunction = function () {
  unRegisters.forEach((fn) => {
    if (fn) {
      fn();
    }
  });
  while (unRegisters.length) {
    unRegisters.pop();
  }
};

export default function () {
  // 复制模版、修改数据集时会调用
  const callSaveComponentsData = function (params: any) {
    return saveComponentsData(params).then((res) => {
      const data = res.data || [];
      data.forEach((item: any) => {
        const meta = store.get(item.id);
        // 兜底操作
        // 如果当前没有组件id，则保存组件后需重新赋予id
        if (!meta?.elementId) {
          store.merge(item.id, {
            elementId: item.elementId,
            id: item.id,
          });
        }

        // 状态变更为已关联
        store.merge(item.id, { isLinkDataSet: true });
        // 查询当前组件数据 关联指标不能用缓存，这里是false
        store.get(item.id)?.query?.({
          elementId: meta.elementId,
          cache: false,
          repeatedSubmission: false,
        });
      });
      VisibleFilter.set(data[0]?.minAuthLevel);
    });
  };

  const callSavePageStructure = function (param?: any, errorCallback?: any) {
    config.set('isSavePage', true);
    event.emit('savePage.loading');
    // 调用前取消上一个请求
    if (savePageStructureToken) {
      savePageStructureToken('取消请求');
      savePageStructureToken = null;
    }
    if (param) {
      savePageCallbacks.push(param);
    }

    clearTimeout(savePageStructureTime);
    savePageStructureTime = window.setTimeout(async () => {
      event.emit('updatePage');
      const serverParams = await transformSchemaToServer(
        undefined,
        getProjectSchema(),
      );
      savePageStructure(
        serverParams,
        new axios.CancelToken((c) => {
          savePageStructureToken = c;
        }),
      )
        .then((res) => {
          VisibleFilter.set(res.data?.minAuthLevel);
          (res.data.children || []).forEach((v: any) =>
            store.merge(v.id, {
              title: v.title,
              elementId: v.elementId,
              componentType: v.componentType,
            }),
          );
          // savePageCallbacks.forEach((fn) => fn()?.catch?.((e) => e));
          // savePageCallbacks = [];
          // 批量关联组件数据集信息
          if (savePageCallbacks.length) {
            const callbackList = savePageCallbacks
              .map((v) => v())
              .filter((f) => !!f);
            const params = new Map();
            // 倒循环遍历，删除之前相同id的
            for (let i = callbackList.length - 1; i >= 0; i--) {
              const item = callbackList[i];
              if (!params.has(item.id)) {
                params.set(item.id, item);
              }
            }
            if (params.size) {
              callSaveComponentsData(Array.from(params, ([, value]) => value));
            }
          }

          savePageCallbacks = [];
          event.emit('savePage.end', dayjs(new Date()).format('HH:mm:ss'));
        })
        .catch((res) => {
          // console.log('error', res);
          if (errorCallback) {
            errorCallback(res);
          }
        })
        .finally(() => {
          savePageStructureToken = null;
          config.set('isSavePage', false);
        });
    }, 300);
  };

  // 先清除后注册
  unRegisterFunction();

  // unRegisters.push(registerResize());

  /** 新增组件事件 */
  unRegisters.push(
    project.currentDocument?.onMountNode((NODE: any) => {
      const { node, copy } = NODE;
      walkerComponents(node.schema, (item, _, metaMap) => {
        if (
          !store.has(item.id as string) ||
          store.get(item.id as string).elementId === undefined
        ) {
          let componentIndex = 1;
          let matchINdex; // 匹配复制的
          if (item.__title__) {
            // 大于6位（+万级） 则截取，避免超大数字递归去重卡死
            // 开头不能为 0， 避免00000 + 1 = 1
            const mat = item.__title__.match(/[1-9]\d{0,6}$/);
            if (mat && mat[0]) {
              matchINdex = mat.index;
              componentIndex = Number(mat[0]) || 0;
            }
          }
          const meta = metaMap[item.componentName];
          let chartTile;
          let titleList: any[] = [];
          walkerComponents(getProjectSchema(), (item2, meta2) => {
            if (item.id !== item2.id) {
              titleList.push(item2.props?.title || meta2.title);
            }
          });

          do {
            if (item.__title__) {
              chartTile =
                (matchINdex !== undefined
                  ? item.__title__.slice(0, matchINdex)
                  : item.__title__) + componentIndex++;
            } else {
              chartTile =
                '未命名' + componentName[meta.componentType] + componentIndex++;
            }
          } while (titleList.includes(chartTile));
          store.merge(item.id as string, {
            ...meta,
            id: item.id as string,
            title: chartTile,
            loading: !!copy,
            isLinkDataSet: false,
          });

          callSavePageStructure(() => {
            const dataSourceId = (item.props as any)?.dataSetConfig
              ?.dataSourceId;
            if (copy && dataSourceId !== undefined) {
              const meta = store.get(item.id as string);
              const params = {
                id: item.id,
                reportId: getCurrnetPageId(),
                componentType: meta.componentType,
                requestDataType: meta.dataType,
                elementId: meta.elementId,
                datasetId: dataSourceId,
                title: meta.title || componentName[node.componentName],
                ...transformDataSetConfig(
                  (item.props as any)?.dataSetConfig,
                  meta,
                  item,
                ),
              };

              return params;

              // 绑定组件关系
              // await callSaveComponentData(
              //   item as unknown as IPublicModelNode,
              //   params,
              // );
              // // 关联完成
              // store.merge(item.id as string, { isLinkDataSet: true });
              // // 查询当前组件数据
              // // console.log('查询数据', store.get(item.id as string));
              // store.get(item.id as string)?.query?.({
              //   elementId: meta.elementId,
              //   repeatedSubmission: false,
              // });
            }
          });
        }
      });
    }),
  );

  /** 拖拽事件 */
  let changeNodeChildrenTime: any = null;
  unRegisters.push(
    project.currentDocument?.onChangeNodeChildren((doc: any) => {
      clearTimeout(changeNodeChildrenTime);
      changeNodeChildrenTime = setTimeout(() => {
        callSavePageStructure();
      }, 50);
    }),
  );

  /** 修改某组件props 事件 */
  unRegisters.push(
    project.currentDocument?.onChangeNodeProp(
      async (props: IPublicTypePropChangeOptions) => {
        // 非物料组件不触发
        // if (!store.has(props.node.id as string)) return;
        const node: any = props.node;
        if (typeof props.key === 'string' && props.key.startsWith('__')) return;
        const oldValue = globalCache?.relationshipMap?.[node.id]?.[1];

        if (
          props.key === 'dataSetConfig' &&
          props.newValue.dataSourceId &&
          oldValue !== JSON.stringify(props.newValue)
        ) {
          store.merge(node.id, { loading: true, isLinkDataSet: false });

          // 保存画布信息
          callSavePageStructure(() => {
            // console.log('周这里了');
            const meta = store.get(node.id) || {};
            const params = {
              id: node.id,
              reportId: getCurrnetPageId(),
              componentType:
                meta.componentType ||
                getComponentMetaByName(node.componentName)?.componentBehavior
                  ?.componentType,
              requestDataType:
                meta.dataType ||
                getComponentMetaByName(node.componentName)?.componentBehavior
                  ?.dataType,
              elementId: meta.elementId,
              title: meta.title || componentName[node.componentName],
              datasetId: node.propsData.dataSetConfig.dataSourceId,
              ...transformDataSetConfig(
                node.propsData.dataSetConfig,
                meta,
                node,
              ),
            };
            if (node.componentName === 'DateFilterGlobal') {
              // 默认时间筛选控件没有elementId、datasetId的概念
              params.requestDataType = 2;
              params.elementId = -1;
            }

            return params;
            // console.log('周这里了2');
            // 绑定组件关系
            // await callSaveComponentData(node, params);
            // store.merge(node.id, { isLinkDataSet: true });
            // // 查询当前组件数据
            // store
            //   .get(node.id)
            //   ?.query?.({ cache: false, repeatedSubmission: false });
          });
        } else {
          // 由于下面加了定时器，导致发布时候获取不到是否编辑状态,因此这里先设置
          config.set('isSavePage', true);
          // 保存画布信息
          clearTimeout(onChangeNodePropTime);
          onChangeNodePropTime = window.setTimeout(() => {
            callSavePageStructure();
          }, 200);
        }
      },
    ),
  );

  // 改变组件大小
  // let animation: boolean;
  // unRegisters.push(
  //   editor.eventBus.on(
  //     'designer.resize',
  //     function name({ node = {} as IPublicModelNode }) {
  //       if (!animation) {
  //         requestAnimationFrame(() => {
  //           animation = false;
  //           // console.log('node', node);
  //           walkerComponents(node.schema, (item) => {
  //             const list = store.get(item.id as string)?.resize || [];
  //             list.forEach((fn) => fn());
  //           });
  //         });
  //         animation = true;
  //       }
  //     },
  //   ),
  // );

  unRegisters.push(
    editor.eventBus.on('designer.border.resize', () => {
      callSavePageStructure();
    }),
  );
  unRegisters.push(
    project?.currentDocument?.history.onChangeState(() => {
      walkerComponents(getProjectSchema(), (i, o, k) => {
        // 只有筛选器变更
        if (k[i.componentName].componentType === 4) return true;
        return false;
      })
        .then(() => {
          callSavePageStructure();
        })
        .catch((e) => e);
    }),
  );

  // 删除节点， 清除store 中的是否查询属性
  unRegisters.push(
    project?.currentDocument?.onRemoveNode((node) => {
      if (store.has(node.id)) {
        store.merge(node.id, { queryState: false });
      }
    }),
  );
  // 画布更改位置
  // editor.eventBus.on('lego.designer.dragend', () => {
  //   console.log('画布更改位置');
  //   callSavePageStructure();
  // });
}
