import React, { useState, useRef, useEffect } from 'react';
import { IPublicModelPluginContext } from '@alilc/lowcode-types';
// import { event, config } from '@alilc/lowcode-engine';
const { event, config } = window.AliLowCodeEngine || {};
import './index.less';
// import { ReactComponent as LeftlIcon } from './leftt.svg';
import { message, Input, Tooltip } from '@blmcp/ui';
import Icon from '@ant-design/icons';
import dayjs from 'dayjs';
// import { isValidName } from '@/pages/lego/utils/validate';
import { savePageStructure } from '@/pages/lego/api';
import {
  getCurrnetPageName,
  transformSchemaToServer,
  getProjectSchema,
} from '@/pages/lego/utils';
import { ReactComponent as EditIcon } from '@/assets/lego/edit.svg';
import savePageEvent from './event';
// import { history } from '@umijs/max';

export interface IProps {
  logo?: string;
  href?: string;
  scenarioInfo?: any;
  scenarioDisplayName?: string;
}

const Title: React.FC<IProps> = (): React.ReactElement => {
  const [state, setState] = useState('加载中...');
  const [pageName, setPageName] = useState('');
  const [edit, setEdit] = useState(false);
  const inputRef = useRef(null);

  useEffect(() => {
    let offList: any = [];
    offList.push(
      event.on('common:savePage.init', () => {
        setPageName(getCurrnetPageName());
        setState('已加载最新版本');
        // 注册事件
        savePageEvent();
      }),
    );

    offList.push(
      event.on('common:savePage.loading', () => {
        setState('自动保存中...');
      }),
    );
    offList.push(
      event.on('common:savePage.end', (date) => {
        setState(`已保存 ${date}`);
      }),
    );
    offList.push(
      event.on('common:savePage.release', () => {
        setState('正在发布中...');
      }),
    );
    return () => {
      offList.forEach((fn) => {
        if (fn) {
          fn();
        }
      });
      offList = [];
    };
  }, []);
  return (
    <div className="lowcode-plugin-logo">
      {/* <Icon
        className="lowcode-plugin-logo-left"
        component={LeftlIcon}
        onClick={() => {
          history.push('/qbi/legoBI/list');
        }}
      ></Icon> */}
      {edit ? (
        <Input
          ref={inputRef}
          defaultValue={pageName}
          maxLength={50}
          onBlur={async (event: React.FocusEvent<HTMLDivElement>) => {
            const value = event.target.value;
            const _pageName = pageName;
            setEdit(false);
            if (!value) {
              // event.target.value = pageName;
              message.error('名称不能为空');
              // event.target.focus();
              return;
            }
            if (value.length > 50) {
              // event.target.value = pageName;
              message.error('最长不超过50个字');
              // event.target.focus();
              return;
            }
            // if (!isValidName(value)) {
            //   message.error('名称使用的符号不符合规范');
            //   return;
            // }
            setPageName(value);
            setState('自动保存中...');
            const serverParams = await transformSchemaToServer(
              value,
              getProjectSchema(),
            );
            savePageStructure(serverParams, null)
              .then(() => {
                setState(`已保存 ${dayjs(new Date()).format('HH:mm:ss')}`);
                config.set('pageName', value);
                const storeKey = 'lego-bi-update-reportName';
                window.localStorage.setItem(storeKey, Date.now().toString());
                window.localStorage.removeItem(storeKey);
              })
              .catch(() => {
                setState('保存失败');
                setPageName(_pageName);
              });
          }}
        />
      ) : (
        <span
          className="lowcode-plugin-logo-edit"
          style={{ textOverflow: 'ellipsis' }}
          onDoubleClick={() => {
            setEdit(true);
            setTimeout(() => {
              inputRef.current?.focus();
            }, 100);
          }}
        >
          <Tooltip title={pageName}>{pageName}</Tooltip>
        </span>
      )}

      <Icon
        className="lowcode-plugin-logo-edit-icon"
        component={EditIcon}
        onClick={() => {
          setEdit(true);
          setTimeout(() => {
            inputRef.current?.focus();
          }, 100);
        }}
        style={{ color: 'rgba(0, 0, 0, 0.9)' }}
      ></Icon>
      <span className="lowcode-plugin-logo-state">{state}</span>
    </div>
  );
};

const HeadSamplePlugin = (ctx: IPublicModelPluginContext) => {
  return {
    async init() {
      const { skeleton, config, project } = ctx;
      const scenarioDisplayName = config.get('scenarioDisplayName');
      const scenarioInfo = config.get('scenarioInfo');
      skeleton.add({
        area: 'topArea',
        type: 'Widget',
        name: 'logo',
        content: (
          <Title
            scenarioDisplayName={scenarioDisplayName}
            scenarioInfo={scenarioInfo}
          />
        ),
        contentProps: {
          logo: '',
          href: '/',
        },
        props: {
          align: 'left',
        },
      });
    },
  };
};
HeadSamplePlugin.pluginName = 'HeadSamplePlugin';
HeadSamplePlugin.meta = {
  dependencies: ['EditorInitPlugin'],
};
export default HeadSamplePlugin;
