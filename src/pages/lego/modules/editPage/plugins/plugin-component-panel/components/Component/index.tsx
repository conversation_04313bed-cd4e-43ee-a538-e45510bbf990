import React from 'react';
import Svg from '../../Icon/Component';
import './index.less';
import { Text, StandardComponentMeta } from '../../utils/transform';
import { reportBusinessMonitor } from '@/utils/eventTracking';
interface Props {
  data: StandardComponentMeta;
  t: (input: Text) => string;
}

interface State {
  icon: string | React.ReactNode;
  snippet: any;
}

export default class Component extends React.Component<Props, State> {
  static getDerivedStateFromProps(props) {
    const { data } = props;
    const { icon, snippets = [] } = data;
    const snippet = snippets[0];
    const screenshot = icon || snippet?.screenshot;
    return {
      icon: screenshot,
      snippet,
    };
  }

  state = {
    icon: '',
    snippet: null,
  };

  t: (s) => string;

  constructor(props) {
    super(props);
    this.t = props.t;
  }

  renderIcon() {
    const { icon } = this.state;

    if (!icon) {
      return <Svg className={'no-icon'} />;
    }

    if (typeof icon === 'string') {
      return <img src={icon} alt="" />;
    }

    if (typeof icon === 'function') {
      const X = icon as any;
      return <X />;
    }

    return icon;
  }

  render() {
    const { data } = this.props;
    // const { title } = data;
    const { snippet } = this.state;
    let title = data.title;
    try {
      const { components = [] } = window.LeopardWebQbiMeta || {};
      const curr = components.find((c) => c.componentName === data.title);
      if (curr && curr.name) {
        title = curr.name;
      }
    } catch (e) {
      reportBusinessMonitor(
        'legoBI-editInit-error',
        { error: e, type: 'edit' },
        'error',
      );
    }

    return (
      <div
        className={'snippet' + ' card'}
        data-id={snippet.id}
        // title={this.t(title)}
      >
        <div className={'icon'}>{this.renderIcon()}</div>
        <div className={'name'}>{this.t(title)}</div>
      </div>
    );
  }
}
