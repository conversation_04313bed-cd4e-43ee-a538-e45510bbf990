.card {
  display: flex;
  flex-direction: column;
  padding: 0 10px;
  align-items: center;
  justify-content: space-between;
  // width: (100 / 3) * 1%;
  // height: 114px;
  // flex-grow: 0;
  // flex-shrink: 0;
  // border-right: 1px solid #eaeaea;
  // border-bottom: 1px solid #eaeaea;
  // box-shadow: 0 0 0 0 rgba(0, 0, 0, 0.15);
  transition: box-shadow 0.2s ease;
  text-align: center;

  &:hover {
    // box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.15);
    background: rgba(37, 52, 79, 0.05);
    border-color: transparent;
    border-radius: 6px;
  }

  .icon {
    width: 16px;
    height: 16px;
    margin: 0 1px;
    display: flex;
    justify-content: center;
    align-items: center;

    >img {
      width: 100%;
    }
  }

  .name {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    color: black;
    text-align: center;
  }
}