.tab {
  display: flex;
  flex-direction: column;

  .header {
    position: relative;
    flex-grow: 0;
    flex-shrink: 0;
    overflow-x: scroll;

    .items {
      display: flex;
      height: 32px;
      box-shadow: inset 0 -1px 0 0 rgba(0, 0, 0, 0.07);
    }

    .item {
      padding: 0 16px;
      flex-shrink: 0;
      flex-grow: 0;
      display: flex;
      height: 100%;
      align-items: center;
      justify-content: center;
      color: #666;
      font-size: 12px;
      cursor: pointer;

      &:hover {
        color: #000;
      }

      &.active {
        color: black;
      }
    }

    .indicator {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      width: 72px;
      height: 1px;
      background-color: black;
      transition: left 0.3s ease;
    }
  }

  .tabs {
    flex: 1;
    overflow: hidden;

    .contents {
      height: 100%;
      display: flex;
      flex-direction: row;
      flex-wrap: nowrap;
      transform: translate3d(0, 0, 0);
      transition: transform 0.3s ease-in-out;
    }

    .content {
      width: 100%;
      height: 100%;
      overflow-y: overlay;
      overflow-x: hidden;
      flex-shrink: 0;
      flex-grow: 0;
      opacity: 0;
      transition: opacity 0.3s ease-in-out;

      &::-webkit-scrollbar-thumb {
        background-color: #999;
      }
      &.active {
        opacity: 1;
      }
    }
  }
}
