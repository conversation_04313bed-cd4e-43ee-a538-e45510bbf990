// import { material } from '@alilc/lowcode-engine';
const { material } = window.AliLowCodeEngine || {};
import { IPublicTypeProjectSchema } from '@alilc/lowcode-types';
import { isHubble } from '@/utils/hubble';
import { addBrandComponent } from '@/pages/lego/utils';
import { curSchemaVersion } from '../../../config/config';
import DefaultPageSchema from './defaultPageSchema.json';
import DefaultPageSchemaV2 from './defaultPageSchemaV2.json';

const generateProjectSchema = (
  pageSchema: any,
  i18nSchema: any,
): IPublicTypeProjectSchema => {
  return {
    componentsTree: [JSON.parse(JSON.stringify(pageSchema))],
    componentsMap: material.componentsMap as any,
    version: '2.6.0',
    i18n: i18nSchema,
  };
};

// export const getProjectSchema = async (): Promise<IPublicTypeProjectSchema> => {
//   const pageSchema = DefaultPageSchema;
//   return generateProjectSchema(pageSchema, {});
// };
export const getDefaultProjectSchema =
  async (): Promise<IPublicTypeProjectSchema> => {
    const pageSchema = DefaultPageSchemaV2;

    const Schema = generateProjectSchema(pageSchema, {});

    if (isHubble) {
      addBrandComponent(Schema);
    }

    return Schema;
  };
