declare global {
  interface Window {
    mainDomain: string;
    envVar: string;
  }
}

export default function () {
  const isDev = process.env.NODE_ENV === 'development';
  window.envVar =
    window.envVar || window.$HbEnvVar || process.env.BUILD_ENV || 'daily';
  let base = '';
  if (isDev) {
    base = 'http://localhost:3333/';
  } else {
    const isTest = ['daily', 'daily2', 'dev', 'sit'].includes(window.envVar);
    base =
      process.env.PUBLIC_PATH?.replace(
        '<%= mainDomain %>',
        window.mainDomain || 'yueyuechuxing.cn',
      ) +
        (isTest ? window.envVar + '/' : '') +
        'lowcode' || '/';

    if (Number(window.cdnState) === 1 && window.cdnPubCosUrl) {
      if (['dev', 'daily', 'daily2', 'sit'].includes(window.envVar)) {
        base = base.replace('//cdntest.', window.cdnBaseUrl);
      } else if (['pre', 'pre2', 'publish', 'fresh'].includes(window.envVar)) {
        base = base.replace('//webstatic.', window.cdnBaseUrl);
      }
    }
  }
  // alifdLayout文件加载处理
  // const alifdLayoutEnv = 'cdntest';
  const alifdLayoutEnv = window.cdnPubCosUrl || 'webstatic';
  const alifdLayoutVersion = '2.4.2-rc.21';

  // const qbiVersion = ['daily', 'daily2', 'dev'].includes(window.envVar)
  //   ? Date.now()
  //   : '1.2';
  const qbiVersion = process.env.buildDate;

  return {
    packages: [
      // {
      //   package: 'dayjs',
      //   urls: [
      //     `https://webstatic.yueyuechuxing.cn/yueyue/admin/static_umd/prerelease2/js/dayjs.js`,
      //   ],
      //   library: 'dayjs',
      //   priority: 1,
      //   await: true,
      // },
      // {
      //   package: 'antd',
      //   urls: [
      //     `https://webstatic.yueyuechuxing.cn/yueyue/admin/lego-base/v1.0.0/antd.js`,
      //   ],
      //   library: 'antd',
      //   await: true,
      //   priority: 2,
      // },
      // {
      //   title: 'fusion组件库',
      //   package: '@alifd/next',
      //   version: '1.26.4',
      //   urls: [
      //     'https://webstatic.yueyuechuxing.cn/yueyue/admin/lowcode/next.min.css',
      //     'https://webstatic.yueyuechuxing.cn/yueyue/admin/lowcode/next-with-locales.min.js',
      //   ],
      //   library: 'Next',
      // },
      {
        package: 'echarts',
        version: '5.5.0',
        library: 'echarts',
        urls: [
          `https://${alifdLayoutEnv}.yueyuechuxing.cn/yueyue/npm/umd/blmcpCharts/pre2/250313_0325144323/echarts.min.js`,
        ],
        await: true,
        priority: 2,
      },
      {
        package: '@alifd/layout',
        version: '2.0.7',
        library: 'AlifdLayout',
        // urls: [`http://localhost:3334/view.js`],
        urls: [
          `https://${alifdLayoutEnv}.yueyuechuxing.cn/yueyue/admin/alifdlayout/${alifdLayoutVersion}/AlifdLayout.js`,
          `https://${alifdLayoutEnv}.yueyuechuxing.cn/yueyue/admin/alifdlayout/${alifdLayoutVersion}/AlifdLayout.css`,
        ],
        editUrls: [
          `https://${alifdLayoutEnv}.yueyuechuxing.cn/yueyue/admin/alifdlayout/${alifdLayoutVersion}/lowcode/view.js`,
          `https://${alifdLayoutEnv}.yueyuechuxing.cn/yueyue/admin/alifdlayout/${alifdLayoutVersion}/lowcode/view.css`,
        ],
      },
      {
        package: 'leopard-web-qbi',
        version: '0.1.0',
        library: 'BizComps',
        // editUrls: [`${base}/view.js?v=${qbiVersion}`].concat(
        //   isDev ? [] : [`${base}/view.css?v=${qbiVersion}`],
        // ),
        urls: isDev
          ? [`${base}/view.js?v=${qbiVersion}`].concat(
              isDev ? [] : [`${base}/view.css?v=${qbiVersion}`],
            )
          : [`${base}/render/default/view.js?v=${qbiVersion}`].concat(
              isDev ? [] : [`${base}/render/default/view.css?v=${qbiVersion}`],
            ),
      },
    ],
    components: [
      {
        exportName: 'AlifdLayoutMeta',
        npm: {
          package: '@alifd/layout',
          version: '2.0.7',
        },
        // url: `http://localhost:3334/meta.js`,
        url: `https://${alifdLayoutEnv}.yueyuechuxing.cn/yueyue/admin/alifdlayout/${alifdLayoutVersion}/lowcode/meta.js`,
      },
      {
        exportName: 'LeopardWebQbiMeta',
        npm: {
          package: 'leopard-web-qbi',
          version: '0.1.0',
        },
        url: `${base}/meta.js?v=${qbiVersion}`,
        urls: {
          default: `${base}/meta.js?v=${qbiVersion}`,
        },
      },
    ],
    sort: {
      groupList: ['精选组件', '原子组件', '低代码组件'],
      categoryList: [
        '基础元素',
        '布局容器类',
        '表格类',
        '表单详情类',
        '帮助类',
        '对话框类',
        '业务类',
        '通用',
        '引导',
        '信息输入',
        '信息展示',
        '信息反馈',
      ],
    },
    groupList: ['精选组件', '原子组件', '低代码组件'],
    ignoreComponents: {},
  };
}
