{"componentName": "Page", "props": {"ref": "outerView", "style": {"height": "100%", "padding": "10px 10px 200px"}}, "fileName": "/", "dataSource": {}, "state": {"text": {"type": "JSExpression", "value": "\"outer\""}, "isShowDialog": {"type": "JSExpression", "value": "false"}}, "css": "body {}", "methods": {}, "hidden": false, "title": "", "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "FDRow", "props": {"isDefaultFilter": true, "className": "legoDefaultRow legoFilterDefaultRow", "style": {"backgroundColor": "white"}}, "title": "页面", "hidden": false, "isLocked": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "F<PERSON>ell", "props": {"isDefaultFilter": true, "style": {}}, "title": "页面", "hidden": false, "isLocked": true, "condition": true, "conditionGroup": "", "children": [{"componentName": "NewCityFilter"}]}, {"componentName": "F<PERSON>ell", "props": {"isDefaultFilter": true, "style": {}}, "title": "页面", "hidden": false, "isLocked": true, "condition": true, "conditionGroup": "", "children": [{"componentName": "NewCarTeamFilter"}]}, {"componentName": "F<PERSON>ell", "props": {"isDefaultFilter": true, "style": {}}, "title": "页面", "hidden": false, "isLocked": true, "condition": true, "conditionGroup": "", "children": [{"componentName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}]}, {"componentName": "F<PERSON>ell", "props": {"style": {}}, "title": "页面", "hidden": false, "condition": true, "conditionGroup": "", "children": [{"componentName": "DateFilterGlobal", "props": {"isDefaultFilter": true}}]}, {"componentName": "F<PERSON>ell", "props": {"isDefaultFilter": true, "style": {}}, "title": "页面", "hidden": false, "isLocked": true, "condition": true, "conditionGroup": "", "children": [{"componentName": "SearchButton"}]}]}]}