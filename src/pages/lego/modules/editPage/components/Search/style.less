.bi-lego-search{
  position: relative;
  >.anticon{
    position: absolute;
    z-index: 9;
    left: 10px;
    top: 8px;
  }
  .ant-select-selection-search-input,.ant-select-selection-placeholder{
    padding-left: 20px !important;
  }
  .ant-select-arrow{
    display: none;
  }
}

.bi-lego-AddCalculateFields-SelectFields.BLMSelect_Antd.ant-select{
  height: 34px;
  border-bottom: 1px solid #EBEEF5;
  &:not(.ant-select-customize-input):focus-within{
    .ant-select-selector{
      border: 0 !important;
    }
  }
  >div.ant-select-selector{
    background: transparent !important;
    &:focus{
      border: 0 !important;
    }
  }
  input,.ant-select-selection-item{
    font-size: 13px;
    font-weight: 500;
  }
}

.cm-placeholder{
  color: rgba(0,0,0,0.3) !important;
}

.bi-lego-search-empty{
  padding-top: 10px;
  .ant-empty-description{
    margin: 0;
    color: rgba(0, 0, 0, 0.25);
    font-size: 12px;
  }
}