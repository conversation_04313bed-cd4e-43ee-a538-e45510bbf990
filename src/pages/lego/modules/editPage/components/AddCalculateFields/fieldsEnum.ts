export const polymerization: Array<FieldsItemType> = [
  {
    name: '求和聚合',
    key: 'SUM',
    doc: `返回表达式中所有值的总和。SUM 只能用于数字字段。
    示例: SUM([出车时长])`,
    sql: 'SUM(${表达式})',
  },
  {
    name: '平均聚合',
    key: 'AVG',
    doc: `返回表达式中所有值的平均值。AVG 只能用于数字字段。
    示例: AVG([完单量])`,
    sql: 'AVG(${表达式})',
  },
  {
    name: '计数聚合',
    key: 'COUNT',
    doc: `返回组中的项目数。
    示例: COUNT([运力公司])`,
    sql: 'COUNT(${表达式})',
  },
  {
    name: '去重计数聚合',
    key: 'COUNTD',
    doc: `返回组中的不同项目数。
    示例: COUNTD([运力公司])`,
    sql: 'COUNTD(${表达式})',
  },
  {
    name: '最大值聚合',
    key: 'MAX',
    doc: `返回表达式在所有记录中的最大值。MAX只能用于数字、日期、日期时间字段。
    示例: MAX([匹配量])`,
    sql: 'MAX(${表达式})',
  },
  {
    name: '最小值聚合',
    key: 'MIN',
    doc: `返回表达式在所有记录中的最小值。MIN只能用于数字、日期、日期时间字段。
    示例: MIN([匹配量])`,
    sql: 'MIN(${表达式})',
  },
];

const count: Array<FieldsItemType> = [
  {
    name: '加法',
    key: '+',
    doc: `当表达式1和表达式2的类型均为数值时，做算术加法；当它们的类型为字符串时，做字符串连接；当表达式1为日期\\日期时间时，做日期天数的加法。
    示例: 用法1，数值相加：[固定成本] + [非固定成本]； 用法2，字符串连接：[省份] + "/" + [城市] ；\n用法3，日期\\日期时间增加天数：[订单日期]+10。`,
    sql: '${表达式1} + ${表达式2}',
  },
  {
    name: '减法',
    key: '-',
    doc: `当表达式1和表达式2的类型均为数值时，做算术减法；当表达式1为日期\\日期时间时，做日期天数的减法。
    示例: 用法1，数值相减：[完单量] - [发单量]； 用法2，日期相减：[结算日期]-[订单日期]，得到日期天数； 用法3，日期\\日期时间减少天数：[订单日期]-10。`,
    sql: '${表达式1} - ${表达式2}',
  },
  {
    name: '乘法',
    key: '*',
    doc: `* 作为乘法运算符，只能用于数字字段。
    示例: [客单价] * [出车司机数]`,
    sql: '${表达式1} * ${表达式2}',
  },
  {
    name: '除法',
    key: '/',
    doc: `/ 作为除法运算符，只能用于数字字段。
    示例: [完单量] / [出车时长]`,
    sql: '${表达式1} / ${表达式2}',
  },
  {
    name: '绝对值',
    key: 'ABS',
    doc: `返回给定数字的绝对值
    示例: ABS(-7) = 7`,
    sql: 'ABS(${数值})',
  },
  {
    name: '指定小数位',
    key: 'ROUND',
    doc: `将数字四舍五入为最接近的整数或指定小数位数
    示例: ROUND(3.1415,2)=3.14`,
    sql: 'ROUND(${表达式},${小数位数})',
  },
  {
    name: '上取整',
    key: 'CEIL',
    doc: `将数字舍入为等于或大于数值的最接近整数
    示例: CEIL(3.1415) = 4`,
    sql: 'CEIL(${数值})',
  },
  {
    name: '下取整',
    key: 'FLOOR',
    doc: `将数字舍入为等于或小于值的最接近整数
    示例: FLOOR(200.7) = 200，FLOOR(-1.5) = -2`,
    sql: 'FLOOR(${数值})',
  },
];

const logic: Array<FieldsItemType> = [
  // {
  //   name: 'IF条件',
  //   key: 'IF',
  //   doc: `当条件满足时，返回返回值1，否则返回返回值2。
  //   示例: 简单用法：IF [完单量] > 100 THEN "优" ELSE "差" 。嵌套用法：IF [完单量] > 100 THEN "优" ELSE IF [完单量] > 50 THEN "良" ELSE "差"`,
  //   sql: 'IF ${条件} THEN ${返回值1} ELSE ${返回值2}'
  // },
  {
    name: 'CASE条件',
    key: 'CASE',
    doc: `对数据进行多级条件判断，一旦满足了某一个WHEN，则这条数据就会退出CASE WHEN，确定返回值
    示例:  CASE WHEN  [发单量] >= 900 THEN 'A'
          WHEN [发单量] >= 500 THEN 'B'
          WHEN [发单量] >= 100 THEN 'C'
          ELSE 'D'
          END`,
    sql: 'CASE WHEN ${条件} THEN ${值1} ELSE ${值2} END',
  },
  {
    name: 'IF条件',
    key: 'IF',
    doc: `检查某个条件是否得到满足，如果为 TRUE 则返回值1，如果为 FALSE 则返回值2，如果值2不填写，则会返回NULL。
    示例: IF([利润] > 0, 盈利, 亏损), IF([利润] > 0, 盈利)`,
    sql: 'IF(${条件},${值1},${值2})',
  },
];

export const fieldsKeyWords = [
  '表达式1',
  '表达式2',
  '表达式',
  '数值',
  '小数位数',
  '条件',
  '值1',
  '值2',
  '返回值1',
  '返回值2',
  '默认返回值',
];

export const fieldsAllEnum: Array<FieldsItemType> = [].concat(
  polymerization as never,
  count as never,
  logic as never,
);

export type FieldsEnumType = {
  label: string;
  value: string;
  list: Array<FieldsItemType>;
};

export type FieldsItemType = {
  name: string;
  doc: string;
  key: string;
  sql: string;
  fieldDesc: string;
};

export default [
  {
    label: '全部',
    value: '0',
    list: fieldsAllEnum,
  },
  {
    label: '聚合函数',
    value: '1',
    list: polymerization,
  },
  {
    label: '计算函数',
    value: '2',
    list: count,
  },
  {
    label: '逻辑函数',
    value: '3',
    list: logic,
  },
];
