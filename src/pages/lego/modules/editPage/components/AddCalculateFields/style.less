@border-color: #ebeef5;

.calculateFieldsHeader {
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
}

.calculateFieldsBody {
  display: flex;
  border: 1px solid @border-color;
  border-radius: 10px;
  height: 440px;
  color: #000;
  font-family: PingFang SC;
  overflow: hidden;
  .funBox {
    width: 140px;
    border-right: 1px solid @border-color;
    padding: 10px;
  }
  .selectFieldsBox {
    width: 250px;
    border-right: 1px solid @border-color;
    padding: 10px;
  }
  .formulaBox {
    width: calc(100% - 660px);
    display: flex;
    flex-direction: column;
  }
  .docBox {
    width: 270px;
    border-left: 1px solid @border-color;
  }
  .headBox {
    background: rgba(37, 52, 79, 0.05);
    height: 40px;
    line-height: 40px;
    padding: 0 10px;
    font-size: 13px;
    font-weight: 500;
  }
  .errorinfo {
    min-height: 30px;
    line-height: 30px;
    padding: 0 10px;
    color: #db3241;
    background: rgba(219, 50, 65, 0.05);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 5;
    overflow: hidden;
    text-overflow: ellipsis;
    max-height: 150px;
  }
  .successinfo {
    min-height: 30px;
    line-height: 30px;
    padding: 0 10px;
    color: #2ec735;
    background: rgba(95, 236, 97, 0.05);
  }
  .editBox {
    // height: calc(100% - 120px);
    padding: 6px 10px;
    font-size: 12px;
    color: #303133;
    font-weight: normal;
    outline: none;
    white-space: break-spaces;
    flex: 1;
    overflow: hidden;
    > div {
      width: 100%;
      height: 100%;
      outline: none;
      font-family: PingFang SC;
      padding: 0;
      &:empty::before {
        content: 'qadasd';
        color: rgba(0, 0, 0, 0.3);
      }
    }
  }
  .docInfo {
    height: calc(100% - 40px);
    padding: 10px;
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;
    color: #303133;
    white-space: pre-line;
  }
}

.SelectFields {
  display: inline-block;
  width: 100%;
  height: 100%;
  > h1 {
    font-size: 13px;
    font-weight: 500;
    padding: 10px;
    line-height: 1;
    border-bottom: 1px solid @border-color;
  }
  > ul {
    height: calc(100% - 65px);
    overflow: auto;
    padding: 0;
    > li {
      padding: 10px;
      font-size: 13px;
      cursor: pointer;
      line-height: 1;
    }
    > li:hover {
      background: rgba(37, 52, 79, 0.05);
      border-radius: 6px;
    }
  }
}

.textOverflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.dropdownItem{
  p{
    font-family: PingFang SC;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0em;
    color: rgba(0, 0, 0, 0.9);
    margin-bottom: 8px;
  }
  span{
    font-family: PingFang SC;
    font-size: 12px;
    font-weight: normal;
    line-height: 20px;
    letter-spacing: 0em;
    color: rgba(0, 0, 0, 0.6);
  }
}