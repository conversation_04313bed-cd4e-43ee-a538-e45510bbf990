import { AssetItem, Asset, AssetList, AssetBundle } from '@alilc/lowcode-types';

export enum AssetType {
  JSUrl = 'jsUrl',
  CSSUrl = 'cssUrl',
  CSSText = 'cssText',
  JSText = 'jsText',
  Bundle = 'bundle',
}

export enum AssetLevel {
  // 环境依赖库 比如 react, react-dom
  Environment = 1,
  // 基础类库，比如 lodash deep fusion antd
  Library = 2,
  // 主题
  Theme = 3,
  // 运行时
  Runtime = 4,
  // 业务组件
  Components = 5,
  // 应用 & 页面
  App = 6,
}

export const AssetLevels = [
  AssetLevel.Environment,
  AssetLevel.Library,
  AssetLevel.Theme,
  AssetLevel.Runtime,
  AssetLevel.Components,
  AssetLevel.App,
];

export function evaluate(script: string, scriptType?: string) {
  const scriptEl = document.createElement('script');
  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  scriptType && (scriptEl.type = scriptType);
  scriptEl.text = script;
  document.head.appendChild(scriptEl);
  document.head.removeChild(scriptEl);
}

export function createDefer<T = any>(): any {
  const r: any = {};
  const promise = new Promise<T>((resolve, reject) => {
    r.resolve = resolve;
    r.reject = reject;
  });

  r.promise = () => promise;

  return r;
}

export function load(url: string, scriptType?: string) {
  const node = document.createElement('script');

  // node.setAttribute('crossorigin', 'anonymous');
  const i = createDefer();
  function onload(e: any) {
    node.onload = null;
    node.onerror = null;
    if (e.type === 'load') {
      i.resolve();
    } else {
      i.reject();
    }
    // document.head.removeChild(node);
    // node = null;
  }
  node.onload = onload;
  node.onerror = onload;

  node.src = url;

  // `async=false` is required to make sure all js resources execute sequentially.
  node.async = false;

  // eslint-disable-next-line @typescript-eslint/no-unused-expressions
  scriptType && (node.type = scriptType);

  document.head.appendChild(node);

  return i.promise();
}

function assetItem(
  type: AssetType,
  content?: string | null,
  level?: AssetLevel,
  id?: string,
): AssetItem | null {
  if (!content) {
    return null;
  }
  return {
    type,
    content,
    level,
    id,
  };
}

function isAssetBundle(obj: any): obj is AssetBundle {
  return obj && obj.type === AssetType.Bundle;
}

function isCSSUrl(url: string): boolean {
  return /\.css(\?.*)?$/.test(url);
}

function isAssetItem(obj: any): obj is AssetItem {
  return obj && obj.type;
}

function parseAssetList(
  scripts: any,
  styles: any,
  assets: AssetList,
  level?: AssetLevel,
) {
  for (const asset of assets) {
    // eslint-disable-next-line @typescript-eslint/no-use-before-define
    parseAsset(scripts, styles, asset, level);
  }
}

function parseAsset(
  scripts: any,
  styles: any,
  asset: Asset | undefined | null,
  level?: AssetLevel,
) {
  if (!asset) {
    return;
  }
  if (Array.isArray(asset)) {
    return parseAssetList(scripts, styles, asset, level);
  }

  if (isAssetBundle(asset)) {
    if (asset.assets) {
      if (Array.isArray(asset.assets)) {
        parseAssetList(scripts, styles, asset.assets, asset.level || level);
      } else {
        parseAsset(scripts, styles, asset.assets, asset.level || level);
      }
      return;
    }
    return;
  }

  if (!isAssetItem(asset)) {
    // eslint-disable-next-line no-param-reassign
    asset = assetItem(
      isCSSUrl(asset) ? AssetType.CSSUrl : AssetType.JSUrl,
      asset,
      level,
    )!;
  }

  let lv = asset.level || level;

  if (!lv || AssetLevel[lv] === null) {
    lv = AssetLevel.App;
  }

  asset.level = lv;
  if (asset.type === AssetType.CSSUrl || asset.type === AssetType.CSSText) {
    styles[lv].push(asset);
  } else {
    scripts[lv].push(asset);
  }
}

export class StylePoint {
  private lastContent: string | undefined;

  private lastUrl: string | undefined;

  private placeholder: Element | Text;

  readonly level: number;

  readonly id: string;

  constructor(level: number, id?: string) {
    this.level = level;
    if (id) {
      this.id = id;
    }
    let placeholder: any;
    if (id) {
      placeholder = document.head.querySelector(`style[data-id="${id}"]`);
    }
    if (!placeholder) {
      placeholder = document.createTextNode('');
      const meta = document.head.querySelector(`meta[level="${level}"]`);
      if (meta) {
        document.head.insertBefore(placeholder, meta);
      } else {
        document.head.appendChild(placeholder);
      }
    }
    this.placeholder = placeholder;
  }

  applyText(content: string) {
    if (this.lastContent === content) {
      return;
    }
    this.lastContent = content;
    this.lastUrl = undefined;
    const element = document.createElement('style');
    element.setAttribute('type', 'text/css');
    if (this.id) {
      element.setAttribute('data-id', this.id);
    }
    element.appendChild(document.createTextNode(content));
    document.head.insertBefore(
      element,
      this.placeholder.parentNode === document.head
        ? this.placeholder.nextSibling
        : null,
    );
    document.head.removeChild(this.placeholder);
    this.placeholder = element;
  }

  applyUrl(url: string) {
    if (this.lastUrl === url) {
      return;
    }
    this.lastContent = undefined;
    this.lastUrl = url;
    const element = document.createElement('link');
    const i = createDefer();
    function onload(e: any) {
      element.onload = null;
      element.onerror = null;
      if (e.type === 'load') {
        i.resolve();
      } else {
        i.reject();
      }
    }

    element.onload = onload;
    element.onerror = onload;

    element.href = url;
    element.rel = 'stylesheet';
    if (this.id) {
      element.setAttribute('data-id', this.id);
    }
    document.head.insertBefore(
      element,
      this.placeholder.parentNode === document.head
        ? this.placeholder.nextSibling
        : null,
    );
    document.head.removeChild(this.placeholder);
    this.placeholder = element;
    return i.promise();
  }
}

export class AssetLoader {
  private stylePoints = new Map<string, StylePoint>();

  async load(asset: Asset) {
    // console.log('assetasset', asset);
    const styles: any = {};
    const scripts: any = {};
    AssetLevels.forEach((lv) => {
      styles[lv] = [];
      scripts[lv] = [];
    });
    parseAsset(scripts, styles, asset);
    const styleQueue: AssetItem[] = styles[AssetLevel.Environment].concat(
      styles[AssetLevel.Library],
      styles[AssetLevel.Theme],
      styles[AssetLevel.Runtime],
      styles[AssetLevel.App],
    );
    const scriptQueue: AssetItem[] = scripts[AssetLevel.Environment].concat(
      scripts[AssetLevel.Library],
      scripts[AssetLevel.Theme],
      scripts[AssetLevel.Runtime],
      scripts[AssetLevel.App],
    );
    console.time('css耗时');
    await Promise.all(
      styleQueue.map(({ content, level, type, id }) =>
        this.loadStyle(content, level!, type === AssetType.CSSUrl, id),
      ),
    );
    console.timeEnd('css耗时');
    console.time('await1耗时');
    await Promise.all(
      scriptQueue
        .filter((v) => v.content?.await && v.content?.priority !== 2)
        .map(({ content, type, scriptType }) =>
          this.loadScript(content.url, type === AssetType.JSUrl, scriptType),
        ),
    );
    console.timeEnd('await1耗时');
    console.time('await2耗时');
    await Promise.all(
      scriptQueue
        .filter((v) => v.content?.priority === 2)
        .map(({ content, type, scriptType }) =>
          this.loadScript(content.url, type === AssetType.JSUrl, scriptType),
        ),
    );
    console.timeEnd('await2耗时');
    console.time('其他耗时');
    await Promise.all(
      scriptQueue
        .filter((v) => !v.content?.await)
        .map(({ content, type, scriptType }) =>
          this.loadScript(content, type === AssetType.JSUrl, scriptType),
        ),
    );
    console.timeEnd('其他耗时');
  }

  private loadStyle(
    content: string | undefined | null,
    level: AssetLevel,
    isUrl?: boolean,
    id?: string,
  ) {
    if (!content) {
      return;
    }
    let point: StylePoint | undefined;
    if (id) {
      point = this.stylePoints.get(id);
      if (!point) {
        point = new StylePoint(level, id);
        this.stylePoints.set(id, point);
      }
    } else {
      point = new StylePoint(level);
    }
    return isUrl ? point.applyUrl(content) : point.applyText(content);
  }

  private loadScript(
    content: string | undefined | null,
    isUrl?: boolean,
    scriptType?: string,
  ) {
    if (!content) {
      return;
    }
    return isUrl ? load(content, scriptType) : evaluate(content, scriptType);
  }

  // todo 补充类型
  async loadAsyncLibrary(asyncLibraryMap: Record<string, any>) {
    const promiseList: any[] = [];
    const libraryKeyList: any[] = [];
    const pkgs: any[] = [];
    for (const key in asyncLibraryMap) {
      // 需要异步加载
      if (asyncLibraryMap[key].async) {
        promiseList.push(window[asyncLibraryMap[key].library]);
        libraryKeyList.push(asyncLibraryMap[key].library);
        pkgs.push(asyncLibraryMap[key]);
      }
    }
    await Promise.all(promiseList).then((mods) => {
      if (mods.length > 0) {
        mods.map((item, index) => {
          const { exportMode, exportSourceLibrary, library } = pkgs[index];
          window[libraryKeyList[index]] =
            exportMode === 'functionCall' &&
            (exportSourceLibrary == null || exportSourceLibrary === library)
              ? item()
              : item;
          return item;
        });
      }
    });
  }
}

export const dependencyCheck = function (deps: string[]) {
  const depUrl = ['echarts' /* 'AlifdLayout', 'view' */];
  const depNames = ['echarts' /*  'AlifdLayout', 'BizComps'  */];
  for (let i = deps.length - 1; i >= 0; --i) {
    const dep = deps[i];
    const name = depUrl.findIndex((f) => dep.includes(f));
    const lib =
      // @ts-expect-error
      window[depNames[name]] ||
      window.opener?.[depNames[name]] ||
      window.opener?.proxy?.[depNames[name]];
    if (dep.includes('.js') && depNames[name] && lib) {
      // @ts-expect-error
      window[depNames[name]] = lib;
      deps.splice(i, 1);
    }
  }
  if (deps.length === 0) {
    return true;
  }
};
