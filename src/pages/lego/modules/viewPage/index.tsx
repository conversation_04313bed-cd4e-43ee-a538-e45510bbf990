import { Spin } from '@blmcp/ui';
import { useEffect, useState } from 'react';
import { blmAnalysisPageView } from '@/utils/eventTracking';
import LegoRenderer from '../viewPage/LegoRenderer';
import { getPageStructure } from '../../api/index';
import { getCurrnetPageStatus } from '../../utils';

const hideFilterEditPk = [
  'financeBusinessAnalysis',
  'driverStratificationResultChart',
  'driverLayeredList',
  'driverStratificationResultFK',
  'driverConversionFunnelAnalysisFK',
  'driverDailyRetentionAnalysisFK',
  'driverMonthlyRetentionAnalysisFK',
  'driverWeekRetentionAnalysisFK',
  'insightIntoOperData',
  'marketingInsights',
];
export default function () {
  const [data, setData] = useState();
  const [pageMark, setPageMark] = useState('');

  useEffect(() => {
    const reportId = new URLSearchParams(location.search.slice(1)).get(
      'reportId',
    );

    window.$legoPageMontTime =
      Date.now() - (window.$routerBeforeEachTime || window.htmlStartTime);

    blmAnalysisPageView({
      pageId: 'p_leopard_cp_00000662',
      eventId: 'e_leopard_cp_pv_00003064',
      ext: {
        str0_e: reportId,
      },
    });
    setPageMark('');

    getPageStructure({
      reportId,
      publishStatus: getCurrnetPageStatus(),
    })
      .then((res) => {
        setPageMark(res?.data?.pageMark);
        setData(res as any);
      })
      .catch((res) => {
        setData(res as any);
      });
  }, []);

  if (!data) {
    return <Spin size="large" className="lego-page-loading" />;
  }

  return (
    <LegoRenderer
      assets={true}
      data={data}
      edit={!hideFilterEditPk.some((v) => pageMark?.includes?.(v))}
      walkerComponents={(item: any, parent: any) => {
        if (
          pageMark?.includes?.('financeBusinessAnalysis') &&
          item.componentName === 'CapacityCompanyFilter'
        ) {
          parent.props.style.display = 'none';
        }
      }}
    ></LegoRenderer>
  );
}
