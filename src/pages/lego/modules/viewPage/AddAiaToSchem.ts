import _ from 'lodash';
// import { getPublicApiHub, ApiListType } from '@blmcp/peento-publicApiHub';
import { isHubble } from '@/utils/hubble';
// import request from '@/utils/request';
import isMobile from '../../utils/isMobile';

// const apiRequestList: ApiListType = getPublicApiHub(request);

const checkCntOrdDone = (arr) => {
  let flag = false;

  const recursionOrdDone = (arr) => {
    for (let i = 0; i < arr.length; i++) {
      let cur = arr[i];
      if (cur.children) {
        recursionOrdDone(cur.children);
      } else {
        if (cur.componentName === 'IndexCard' || cur.componentName === 'Card') {
          let measureInfo = cur?.props?.dataSetConfig?.measureInfo;
          let isHave = _.find(measureInfo, (o) => {
            return o.key === 'cnt_ord_done' || o.key === 'sum_cnt_order_done';
          });
          if (isHave) {
            flag = true;
          }
        }
      }
    }
  };
  recursionOrdDone(arr);
  return flag;
};

const AddAiaToSchem = async (schema: any) => {
  const { componentsMap, componentsTree } = schema;

  let isHave = checkCntOrdDone(componentsTree);

  // if (isHave) {
  //   try {
  //     const res = await apiRequestList.batchCloudConfig({
  //       group: 'default',
  //       modules: [
  //         {
  //           moduleName: '	cp-platform-normal-config',
  //           configNames: ['ai_test'],
  //         },
  //       ],
  //     });
  //     if (res.code === 1) {
  //       const { whitelist } = JSON.parse(res.data[0].value);
  //       isHave = whitelist.includes(tenantId);
  //     }
  //   } catch (error) {}
  // }

  console.log(`isHave:${isHave}`);
  console.log(`isHubble:${isHubble}`);
  console.log(`isMobile:${isMobile()}`);
  if (isHave)
    if (isHave && !isHubble && !isMobile()) {
      const userInfo = JSON.parse(localStorage.getItem('USERINFO_LOCAL'));
      // 放量后判断当前账号是否有完整的城市权限，没有完整城市权限不展示智能归因模块
      if (!userInfo?.hasCityPermission) {
        return schema;
      }
      componentsMap.push({
        package: 'leopard-web-qbi',
        version: '0.1.0',
        exportName: 'AiAttribution',
        main: 'src/pages/lego/libraryMaterials/index.tsx',
        destructuring: true,
        subName: '',
        componentName: 'AiAttribution',
      });
      componentsTree[0].children.splice(1, 0, {
        componentName: 'FDRow',
        id: '',
        docId: '',
        props: {},
        hidden: false,
        title: '',
        isLocked: false,
        condition: true,
        conditionGroup: '',
        children: [
          {
            componentName: 'FDCell',
            id: '',
            docId: '',
            props: {
              align: 'center',
              verAlign: 'top',
              style: {
                backgroundColor: 'rgba(255,255,255,1)',
              },
              id: '',
            },
            hidden: false,
            title: '',
            isLocked: false,
            condition: true,
            conditionGroup: '',
            children: [
              {
                componentName: 'AiAttribution',
                id: '',
                docId: '',
                props: {},
                hidden: false,
                title: '',
                isLocked: false,
                condition: true,
                conditionGroup: '',
              },
            ],
          },
        ],
      });
    }
  return schema;
};
export default AddAiaToSchem;
