import React, { forwardRef } from 'react';
import { buildComponents, wrapReactClass } from '@alilc/lowcode-utils';
import VisibleFilter from '@/pages/lego/utils/visibleFilter'

export const FDRow = forwardRef(({ children, className }, ref) => {
  const isLegoDefaultRow = className && className?.indexOf('legoDefaultRow') !== -1;
  let content = children;
  const hideNum = VisibleFilter.hideNum || 0
  if (isLegoDefaultRow) {
    content = (
      <div className={`lego-default-row-wrapper ${(children?.length - hideNum) > 4 ? 'show-expand' : 'hide-expand'}`}>
        <div className='lego-default-row-filter-wrapper'>{children.filter((item, index) => index !== children?.length - 1)}</div>
        {children?.[children?.length - 1]}
      </div>
    )
  }
  return (
    <div className={`${className || ''} m-fd-row mobile`} ref={ref}>
      {content}
    </div>
  )
})

export default wrapReactClass(FDRow)
