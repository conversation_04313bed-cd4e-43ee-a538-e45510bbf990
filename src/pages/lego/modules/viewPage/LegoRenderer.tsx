// share-url-hooks-disable-file
import { useEffect, useMemo, useState, useRef } from 'react';
import { buildComponents } from '@alilc/lowcode-utils';
import ReactRenderer from '@alilc/lowcode-react-renderer';
// import { injectComponents } from '@alilc/lowcode-plugin-inject';
// import { Loading } from '@alifd/next';
import { Button, Modal, message, Empty, Tooltip } from '@blmcp/ui';
import { useSize } from 'ahooks';
import { throttle, debounce } from 'lodash';
import { IPublicTypeRootSchema } from '@alilc/lowcode-types';
import { isHubble } from '@/utils/hubble';
import UserTransfer from '@/pages/lego/modules/pageList/components/AddShareModal';
import { copyTemplateClick } from '@/pages/lego/utils/common';
import VisibleFilter from '@/pages/lego/utils/visibleFilter';
import queryCenter from '@/pages/lego/libraryMaterials/module/Query';
import relationCenter from '@/pages/lego/libraryMaterials/module/RelationCenter';
import LinkageCenter from '@/pages/lego/libraryMaterials/module/Linkage';
import {
  blmAnalysisModuleClick,
  reportBusinessMonitor,
  sceneActionStartMonitor,
  sceneActionEndMonitor,
  blmMicrofs,
  blmAnalysisPageView,
} from '@/utils/eventTracking';
import {
  transformServerToSchema,
  getCurrnetPageStatus,
  registerResize,
  walkTree,
} from '../../utils';
import isMobile from '../../utils/isMobile';
import { getTextyWidthAndHeight } from '../../utils/css';
import { addShares, exchangeOwner, copyTemplate } from '../../api/index';
import { store, ComponentValue } from '../../hooks/useComponent';

import './index.less';
import '../editPage/info.css';
import showTips from '../../utils/showTips';
import { FDCell } from './layout/FDCell';
import { FDRow } from './layout/FDRow';
import './layout/style.scss';
import AddAiaToSchem from './AddAiaToSchem';
import { AssetLoader } from './AssetLoader';

declare global {
  interface Window {
    $baseRouter: any;
    $baseSelfReport: any;
    htmlStartTime: any;
    $routerBeforeEachTime: any;
  }
}

interface LoaderConfig {
  await: boolean;
  priority: number;
  urls: string[];
  package: string;
  library: string;
}

interface ViewProps {
  data: any; // 页面数据
  edit?: boolean; // 编辑按钮显示
  share?: boolean; // 分享按钮显示
  assets?: boolean; // 是否需要加载资源
  walkerComponents?: (
    item: IPublicTypeRootSchema,
    meta: ComponentValue,
  ) => void; // 组件渲染
  libraryAssetCallback: (list: LoaderConfig[]) => void;
  aiAttribution?: boolean;
}

function clearAll() {
  store.clear(); // 状态
  showTips.clear(); // 提示
  LinkageCenter.clear(); // 关联
  queryCenter.clear(); // 查询参数
  relationCenter.clear(); // 事件
}

const ViewPage = (props: ViewProps) => {
  const {
    data,
    assets = false,
    edit = true,
    share = true,
    aiAttribution = true,
    libraryAssetCallback,
  } = props;
  const [reportSchema, setReportSchema] = useState({});
  const [isEmpty, setEmpty] = useState(false);
  const [actionList, setActionList] = useState<number[]>([]);
  const BoxRef = useRef<null | HTMLElement>(null);
  const size = useSize(BoxRef);
  const sizeTime = useRef<any>(null);
  const reportId = data?.data?.reportId || '';

  useEffect(
    debounce(() => {
      BoxRef.current?.classList.add('legoALLOverflowHidden');
      registerResize();
      clearTimeout(sizeTime.current);
      sizeTime.current = setTimeout(() => {
        BoxRef.current?.classList.remove('legoALLOverflowHidden');
      }, 300);
    }, 100),
    [size?.width],
  );

  // 分享&编辑弹窗
  const [modalView, setModalView] = useState({
    visible: false,
    type: '',
    reportId: '',
  });
  // 穿梭框内选中的值
  const [selectedKeys, setSelectedKeys] = useState<any>({});

  // 弹窗确认
  const handleOk = () => {
    if (
      (!actionList.includes(1) && selectedKeys?.targetKeys?.length) ||
      (actionList.includes(1) &&
        (selectedKeys?.originShareList?.length ||
          selectedKeys?.targetKeys?.length))
    ) {
      const searchParamsShare = {
        reportId: modalView.reportId,
        shareList: selectedKeys?.targetKeys ?? [],
        originShareList: selectedKeys?.originShareList,
      };
      const searchParamsTransfer = {
        reportId: modalView.reportId,
        transferId: selectedKeys?.targetKeys,
      };
      const queryApi = modalView.type === 'share' ? addShares : exchangeOwner;
      const searchParams =
        modalView.type === 'share' ? searchParamsShare : searchParamsTransfer;
      queryApi(searchParams).then((res) => {
        if (res && res?.code === 1) {
          message.success('分享成功');
        }
      });
      setModalView({ ...modalView, visible: false, type: '' });
    } else {
      message.error('请选择人员');
    }
  };

  // 关闭报告弹窗
  const handleCancel = () => {
    setModalView({ ...modalView, visible: false, type: '' });
  };

  // const reportId = new URLSearchParams(location.search.slice(1)).get(
  //   'reportId',
  // );
  const [reportName, setReportName] = useState('');

  // 是否为模板
  const [isTemplate, setTemplateState] = useState();

  // 测试
  useEffect(() => {
    clearAll();
    VisibleFilter.init();
    (async () => {
      if (data) {
        blmAnalysisPageView({
          pageId: 'p_leopard_cp_00001012',
          eventId: 'e_leopard_cp_pv_00004304',
          ext: {
            str0_e: data.data?.pageMark || data.data?.reportId,
          },
        });
        const beforeSchemaTime =
          Date.now() - (window.$routerBeforeEachTime || window.htmlStartTime);
        // 转换权限list
        const _actionList = (
          typeof data.data?.actionList === 'string'
            ? data.data?.actionList.split(',')
            : data.data?.actionList || []
        ).map((v: string) => +v);
        setActionList(_actionList);

        // 报告名称
        setReportName(data?.data?.reportName);
        // 报告状态
        setTemplateState(data?.data?.ownType);

        blmMicrofs({
          legoPageMontTime: window.$legoPageMontTime,
          beforeSchemaTime,
          reportId: data?.data?.pageMark || data?.data?.reportId,
        });

        // 查看判断
        if (
          data.code === 401 ||
          data.code === 173403 ||
          (data?.data?.actionList && !_actionList.includes(5))
        ) {
          message.error('暂无该报告查看权限，请联系管理员授权');
          if (window.$baseRouter) {
            window.$baseRouter.push('/qbi/403');
          }
          return () => {};
        } else if (data.code !== 1) {
          message.error(data.tips || data.msg);
          setEmpty(true);
          return () => {};
        }

        // 找不到 schema 处理
        if (!data?.data?.schema) {
          // message.error('请先发布后再查看');
          setEmpty(true);
          return () => {};
        }

        // 数据粒度影响筛选器
        VisibleFilter.set(data.data.minAuthLevel);

        try {
          const {
            componentsMap: componentsMapArray,
            componentsTree,
            packages,
          } = await transformServerToSchema(
            data.data,
            aiAttribution && AddAiaToSchem,
            data.data.dataTenantId,
          );

          if (props.walkerComponents) {
            walkTree(componentsTree, props.walkerComponents);
          }
          const componentsMap: any = {};
          componentsMapArray.forEach((component: any) => {
            componentsMap[component.componentName] = component;
          });
          const pageSchema = componentsTree[0];

          const libraryMap: any = {};
          const libraryAsset: any = [];

          if (libraryAssetCallback) {
            libraryAssetCallback(packages);
          }

          packages?.forEach(
            ({
              package: _package,
              library,
              urls,
              renderUrls,
              await: _await,
              priority,
            }: any) => {
              libraryMap[_package] = library;
              if (_await || priority) {
                libraryAsset.push({
                  await: true,
                  url: renderUrls || urls,
                  priority,
                });
              } else if (renderUrls) {
                libraryAsset.push(renderUrls);
              } else if (urls) {
                libraryAsset.push(urls);
              }
            },
          );

          if (assets) {
            sceneActionStartMonitor({
              sceneId: 'legoBI-loadAssets-warn',
              uniqueId: 0,
              maxTime: 6000 * 2,
            });
            const assetLoader = new AssetLoader();

            // 资源包处理， 依赖复用
            // for (let i = libraryAsset.length - 1; i >= 0; i--) {
            //   const item = libraryAsset[i];
            //   const urls =
            //     Object.prototype.toString.call(item) === '[object Object]'
            //       ? item.url
            //       : item;
            //   if (dependencyCheck(urls)) {
            //     libraryAsset.splice(i, 1);
            //   }
            // }
            // console.log('libraryAsset', libraryAsset);
            await assetLoader.load(libraryAsset);
            sceneActionEndMonitor({
              sceneId: 'legoBI-loadAssets-warn',
              uniqueId: 0,
              maxTime: 6000 * 2,
            });
          }
          // @ts-expect-error
          const components = buildComponents(libraryMap, componentsMap);
          if (isMobile()) {
            // 处理布局兼容
            components.FDRow = FDRow;
            components.FDCell = FDCell;
          }
          setReportSchema({
            schema: pageSchema,
            components,
          });
        } catch (error) {
          reportBusinessMonitor(
            'legoBI-schema-error',
            { error, type: 'view' },
            'error',
          );
        }
      }
    })();
    return () => {
      VisibleFilter.remove();
    };
  }, [data]);

  useEffect(() => {
    // 哈勃下，添加高度 100% 类，达到高度撑开
    if (isHubble) {
      document
        .getElementById('_overScrollWrap')
        ?.classList.add('hubble-qbi-height100');
    }
    return function () {
      clearAll();
      setEmpty(false);
      // 哈勃下，添加高度 100% 类，达到高度撑开

      if (isHubble) {
        setTimeout(() => {
          document
            .getElementById('_overScrollWrap')
            ?.classList.remove('hubble-qbi-height100');
        });
      }
    };
  }, [data]);

  const { schema, components } = reportSchema as any;
  const ComponentRendering = useMemo(() => {
    if (!data) return <></>;
    if (isEmpty) {
      return (
        <Empty
          imageStyle={{ marginTop: '80px' }}
          image={Empty.PRESENTED_IMAGE_SIMPLE}
          description="暂未创建任何内容"
        />
      );
    }
    return (
      <ReactRenderer
        className="lowcode-plugin-sample-preview-content"
        schema={schema}
        components={components}
      />
    );
  }, [schema, components, isEmpty, data]);
  let isTittleNeedToolTip = false;
  if (
    getTextyWidthAndHeight(
      reportName,
      {
        'font-size': isMobile() ? '18px' : '15px',
        'font-weight': 500,
        'word-break': 'break-all',
      },
      'span',
    ).width > (isMobile() ? document.body.clientWidth - 20 : 300)
  ) {
    isTittleNeedToolTip = true;
  }

  return (
    <div
      className={
        'lowcode-plugin-sample-preview' +
        (isHubble ? ' isHubble' : '') +
        (isMobile() ? ' mobile' : '')
      }
      ref={BoxRef}
    >
      <div className="lowcode-plugin-sample-header">
        <h1>
          {isTittleNeedToolTip ? (
            <Tooltip title={reportName}>{reportName}</Tooltip>
          ) : (
            reportName
          )}
        </h1>
        <span style={{ flex: 1 }}></span>
        <div>
          {isHubble && !isMobile() && (
            <Button
              onClick={() => {
                copyTemplateClick(
                  { id: reportId },
                  getCurrnetPageStatus(),
                  isTemplate ? '模板' : '报告',
                );
              }}
            >
              复制{isTemplate ? '模板' : '报告'}
            </Button>
          )}
          {!isHubble &&
            share &&
            !isMobile() &&
            !(
              isTemplate ||
              !actionList.includes(2) ||
              getCurrnetPageStatus() === 0
            ) && (
              <Button
                onClick={() => {
                  setModalView({
                    visible: true,
                    type: 'share',
                    reportId: reportId,
                  });
                  blmAnalysisModuleClick({
                    eventId: 'e_leopard_cp_click_00003806',
                    pageId: 'p_leopard_cp_00000884',
                    ext: {
                      str0_e: 'view',
                      str1_e: reportId,
                    },
                  });
                }}
              >
                分享
              </Button>
            )}
          {!isMobile() && actionList.includes(1) && edit && (
            <Button
              style={{ marginLeft: '10px' }}
              type="primary"
              onClick={throttle(
                () => {
                  // 哈勃下不能调用，有专门的复制按钮
                  if (isTemplate && !isHubble) {
                    // TODO copy函数调用
                    copyTemplate(reportId as string).then((res) => {
                      if (res?.code === 1) {
                        blmAnalysisModuleClick({
                          eventId: 'e_leopard_cp_click_00003554',
                          pageId: 'p_leopard_cp_00000480',
                          ext: {
                            str0_e: reportId,
                            str1_e: reportName,
                            str2_e: res?.data?.reportId,
                            str3_e: '1',
                          },
                        });
                        setTimeout(() => {
                          const href = `/qbi/legoBI/edit?reportId=${res?.data?.reportId}&overallLayoutShow=false`;
                          location.href = href;
                        });
                      }
                    });
                  } else {
                    blmAnalysisModuleClick({
                      eventId: 'e_leopard_cp_click_00001098',
                      pageId: 'p_leopard_cp_00000344',
                      ext: {
                        int0_e: reportId,
                      },
                    });
                    setTimeout(() => {
                      const href = `/qbi/legoBI/edit?reportId=${reportId}&overallLayoutShow=false`;
                      location.href = href;
                    });
                  }

                  // if (window.$baseRouter) {
                  //   window.$baseRouter.push(href);
                  // } else {
                  //   location.href = href;
                  // }
                },
                2000,
                { trailing: false },
              )}
            >
              编辑
            </Button>
          )}
        </div>
      </div>
      <div
        className="lowcode-plugin-sample-box"
        style={{ background: isEmpty ? '#fff' : 'transparent' }}
      >
        {ComponentRendering}
      </div>
      {modalView.visible && (
        <Modal
          title={modalView.type === 'share' ? '分享报告' : '转让报告'}
          open={modalView.visible}
          onOk={handleOk}
          onCancel={handleCancel}
          width={modalView.type === 'share' ? 720 : 560}
          style={{ maxHeight: '70vh' }}
          closable={true}
          maskClosable={false}
        >
          <UserTransfer
            modalView={modalView}
            onModalOk={(e: any) => setSelectedKeys(e)}
            tabsType={actionList.includes(1) ? 1 : 2}
          />
        </Modal>
      )}
    </div>
  );
};

export default ViewPage;
