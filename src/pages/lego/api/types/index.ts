export interface ResBaseData<T> {
  code: number;
  res: string; // 响应文案，例如 成功
  data: T;
}
export interface DimensionInfo {
  title: string;
  id: number;
  key: string;
  columnId: number;
  fieldType: 'measureInfo' | 'dimensionInfo';
  config: string; //  配置项JSON 字符串
}
export interface MeasureInfo {
  title: string;
  id: number;
  key: string;
  config: string; //  配置项JSON 字符串
  computeModeId: number; // 计算规则
}
type Contrasts = DimensionInfo;

export interface ResData {
  dataSourceId: number; // 数据集ID
  dimensionInfo: DimensionInfo[];
  measureInfo: MeasureInfo[];
  contrastInfo: Contrasts[];
  values: unknown;
}

export interface DataSourceItem {
  id: number; // 数据集ID
  name: string; // 数据集名称
}

export interface DimensionListItem {
  computeModeId: string; // 计算模式，增长率等
  dateFormat?: string; // 日期格式化
  feConfig?: any; // 自定义配置
  fieldType: string;
  advanceComputeModeId: number; // 平均？百分比
  sortNum?: number;
  columnId: number; // 维度ID
  dataType: number; // 2 : 日期  1:数字  0: 字符串
  type: boolean; // false: 物理字段 true: 计算字段
  title: string; // 维度名称
  key: string; // 维度唯一标识
  isAggr: number; //是否可以再次sum 1可以|0不可以
  isSensitive: 0 | 1; // 是否是敏感字段
  defaultVal: object; // 默认值，如日期控件的
}

export interface MeasureListItem {
  columnId: number; // 维度ID
  type: number; // false: 物理字段 true: 计算字段
  tenantFlag: number; // 0 的都的数据集字段都不可删除和编辑
  dataType: number; // 2 : 日期  1:数字  0: 字符串
  title: string; // 维度名称
  key: string; // 指标唯一标识
  isAggr: number; //是否可以再次sum 1可以|0不可以
  isSensitive: 0 | 1; // 是否是敏感字段
}

export interface MeasureGroupListItem {
  columnList: MeasureListItem[];
  category: string;
}

export interface DataSourceDetail {
  dimensionList: DimensionListItem[];
  measureList: MeasureGroupListItem[];
}

export interface CityData {
  value: number;
  label: string;
  children?: CityData[];
}
export interface OptionItem {
  value: number;
  label: string;
}
