import request from '@/utils/request';
import { oriThreePhase } from '@/utils/oriAndCategoryFlag';
import { ResBaseData, ResData } from './types';

// 请求浏览历史+热门内容 下面注释有接口后需要干掉
export const getBrowsingHistory_HotContent = (): ResBaseData<ResData> => {
  return request({
    url: oriThreePhase
      ? '/bos/admin/v1/ai/chart-manager/dataPortal/visitRecords'
      : '/admin/v1/ai/chart-manager/dataPortal/visitRecords',
    method: 'POST',
    data: {},
  });
};
