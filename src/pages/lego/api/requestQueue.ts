import { reportStore, config } from '@blm/bi-lego-sdk/dist/es/utils';
import md5 from 'md5';

type ResolveType<T> = (value: T) => void;
type RejectType = (value: unknown) => void;

interface ItemQuery<T, K> {
  params: T;
  resolve: ResolveType<K>;
  reject: RejectType;
}

function transMd5Header(...args: any[]) {
  const t = new Date().getTime().toString();
  return {
    'Lego-Rq': md5('loge' /** 专门拼错的 */ + args.join(t)),
    'Lego-Tim': t,
  };
}

export class RequestQueue<T, K = unknown> {
  url: string;
  maxRequestConcurrent: number = 6; // 每次最大并发数
  executeIndex: number; // 标识当前执行的最大索引；
  waitQueryQueue: { params: T; resolve: ResolveType<K>; reject: RejectType }[]; //
  constructor(maxRequestConcurrent: number = 6) {
    this.maxRequestConcurrent = maxRequestConcurrent;
    this.executeIndex = 0;
    this.waitQueryQueue = [];
    this.url = '/admin/v1/ai/chart-manager/queryElementResultData';
  }

  execute(
    params: T,
    others: object,
    resolve: ResolveType<K>,
    reject: RejectType,
  ) {
    if (this.executeIndex === this.maxRequestConcurrent) {
      this.waitQueryQueue.push({ params, resolve, reject });
    } else {
      // 直接执行
      this.executeIndex++;
      const req = config.get('setting.request');

      // @ts-expect-error
      req<T, K>({
        url: this.url,
        method: 'POST',
        data: {
          ...params,
          desensitizeFlag: reportStore.get(params.reportId)?.sensitiveState,
        },
        notMessage: true,
        cancelKey: 'queryElementResultData' + params?.elementId,
        ...(others || {}),
        timeout: 60 * 1000,
        headers: transMd5Header(params?.elementId, params?.reportId),
      })
        .then((res) => {
          console.log('执行：then', this.executeIndex);
          resolve(res);
          this.executeIndex--;
          this.finish();
        })
        .catch((e) => {
          console.log('执行：catch', this.executeIndex);
          reject(e);
          this.executeIndex--;
          this.finish();
        });
    }
  }

  // 完成请求
  finish() {
    const { params, resolve, reject } =
      (this.waitQueryQueue.pop() as ItemQuery<T, K>) ?? {};
    if (params) {
      this.executeIndex++;
      const req = config.get('setting.request');

      // @ts-expect-error
      req<T, K>({
        url: this.url,
        method: 'POST',
        data: {
          ...params,
          desensitizeFlag: reportStore.get(params.reportId)?.sensitiveState,
        },
        headers: transMd5Header(params?.elementId, params?.reportId),
      })
        .then((res) => {
          resolve(res);
          this.executeIndex--;
          this.finish();
        })
        .catch((e) => {
          reject(e);
          this.executeIndex--;
          this.finish();
        });
    }
  }

  // 重置请求
  request(params: T, others: object): Promise<K> {
    return new Promise((resolve, reject) => {
      this.execute(params, others, resolve, reject);
    });
  }
}
