// import request from '@/utils/request';
import { config } from '@blm/bi-lego-sdk/dist/es/utils';

// 取得数据集列表
export const queryDataSourceList = () => {
  return config.get('setting.request')({
    url: '/admin/v1/ai/chart-manager/queryAllDatasets',
    method: 'POST',
    data: {},
  });
};
// 取得数据集详情
export const queryDataSourceDetail = ({
  dataSourceId,
  title,
}: {
  dataSourceId: number; // 数据源ID 必填
  title?: string; //字段名称 非必填null默认筛选全部数据集下字段
}) => {
  return config.get('setting.request')({
    url: '/admin/v1/ai/chart-manager/queryDatasetsColumnsV2',
    method: 'POST',
    data: {
      datasetId: dataSourceId,
      title,
    },
  });
};
