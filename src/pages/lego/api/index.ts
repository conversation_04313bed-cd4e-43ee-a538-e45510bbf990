import { config } from '@blm/bi-lego-sdk/dist/es/utils';
import { isHubble } from '@/utils/hubble';
import { RequestQueue } from './requestQueue';

import { ResBaseData, ResData } from './types';

// 创建分批查询队列实例
const queryInstance = new RequestQueue<unknown, ResBaseData<ResData>>();

// 获取卡片数据
// bigbang ignore export getCardData
export const getCardData = (
  params: unknown,
  repeatedSubmission: boolean | undefined,
): Promise<ResBaseData<ResData>> => {
  return queryInstance.request(params, { repeatedSubmission });
};

// bigbang ignore export getFilters
export const getFilters = (params: unknown) => {
  return queryInstance.request(params);
};

// bigbang ignore export getComponentData
export const getComponentData = (
  params: unknown,
  repeatedSubmission: boolean | undefined,
): Promise<ResBaseData<ResData>> => {
  return queryInstance.request(params, { repeatedSubmission });
};

export const checkTextSafe = (data = {}) => {
  return config.get('setting.request')({
    url: isHubble
      ? config.get('sdkConfig.manageSensitiveInfoPort')
      : config.get('sdkConfig.userSensitiveInfoPort'),
    method: 'POST',
    data,
  });
};
export const queryDatasetUpdateTime = (data = {}) => {
  return config.get('setting.request')({
    url: '/admin/v1/ai/chart-manager/queryDatasetUpdateTime',
    method: 'POST',
    data,
  });
};
