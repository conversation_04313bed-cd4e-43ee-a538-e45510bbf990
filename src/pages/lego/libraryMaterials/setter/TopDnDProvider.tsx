import { useCallback, useMemo, useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';

export const useDndProvider = () => {
  const [dndArea, setDndArea] = useState();
  const handleRef = useCallback((node) => setDndArea(node), []);
  const html5Options = useMemo(() => ({ rootElement: dndArea }), [dndArea]);
  return { dndArea, handleRef, html5Options };
};

export const TopDnDProvider = ({ children }) => {
  const { handleRef, html5Options } = useDndProvider();
  return (
    <div ref={handleRef}>
      {
        <DndProvider backend={HTML5Backend} options={html5Options}>
          {children}
        </DndProvider>
      }
    </div>
  );
};
