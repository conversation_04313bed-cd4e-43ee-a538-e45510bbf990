export const getRateMenuData = (dateFormat: string, hideRate: boolean) => {
  const styleItem = {
    lineHeight: '25px',
    width: '120px',
    display: 'inline-block',
  };
  const mapSubMenu = (value: number) =>
    hideRate
      ? [
          {
            label: (
              <span style={{ width: '120px', display: 'inline-block' }}>
                增长值
              </span>
            ),
            value: `${value}-1`,
            key: `${value}-1`,
          },
        ]
      : [
          {
            label: (
              <span style={{ width: '120px', display: 'inline-block' }}>
                增长率
              </span>
            ),
            value: `${value}-2`,
            key: `${value}-2`,
          },
          {
            label: (
              <span style={{ width: '120px', display: 'inline-block' }}>
                增长值
              </span>
            ),
            value: `${value}-1`,
            key: `${value}-1`,
          },
        ];
  const rateMap: Record<string, any> = {
    // 200: [
    //   {
    //     label: <span style={styleItem} >年环比</span>,
    //     children: mapSubMenu(10),
    //   },
    // ],
    201: [
      {
        label: <span style={styleItem}>季环比</span>,
        children: mapSubMenu(11),
      },
    ],
    202: [
      {
        label: <span style={styleItem}>月环比</span>,
        children: mapSubMenu(12),
      },
      {
        label: <span style={styleItem}>年同比</span>,
        children: mapSubMenu(21),
      },
    ],
    203: [
      {
        label: <span style={styleItem}>日环比</span>,
        children: mapSubMenu(14),
      },
      {
        label: <span style={styleItem}>周同比</span>,
        children: mapSubMenu(23),
      },
      {
        label: <span style={styleItem}>月同比</span>,
        children: mapSubMenu(22),
      },
    ],
    205: [
      {
        label: <span style={styleItem}>周环比</span>,
        children: mapSubMenu(13),
      },
    ],
  };

  return (rateMap[dateFormat] ?? []).map((item) => ({
    ...item,
    popupOffset: [-290, -5],
  }));
};

export const MapTitle: Record<string, string> = {
  '11': '季环比',
  '12': '月环比',
  '13': '周环比',
  '14': '日环比',
  '21': '年同比',
  '22': '月同比',
  '23': '周同比',
};
