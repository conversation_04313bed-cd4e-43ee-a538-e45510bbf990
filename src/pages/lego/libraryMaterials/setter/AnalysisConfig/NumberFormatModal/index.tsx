import { Button, Checkbox, Form, Input, Modal, Select } from '@blmcp/ui';
import { useState, forwardRef, useImperativeHandle } from 'react';
import { dataFormatValue, metricFormatConfig } from '../../../module/utils';
import styles from './index.less';

const { Option } = Select;
export interface ModalRef {
  showModal?: (type: string, index: number, numFormatInfo: string) => void;
}

const precisionShow = ['1', '2'];
const divideShow = ['2'];
const numberShow = ['1'];

interface NumberFormatModalProps {
  setNumFormat: (type: string, index: number, numFormatInfo: string) => void;
}

export const NumberFormatModal = forwardRef(
  ({ setNumFormat }: NumberFormatModalProps, ref) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [fieldInfo, setFieldInfo] = useState<{
      type?: string;
      index?: number;
      numFormatInfo?: string;
    }>({});

    const [form] = Form.useForm();
    useImperativeHandle(
      ref,
      () => {
        return {
          showModal(type: string, index: number, numFormatInfo: string) {
            setFieldInfo({ type, index, numFormatInfo });

            try {
              const {
                percentageAgain,
                thousandSplit,
                metricFormat,
                numStyle = '0',
                scale,
                valueUnit,
              } = JSON.parse(numFormatInfo || '{}');
              form.setFieldsValue({
                numStyle,
                scale,
                percentageAgain: percentageAgain === '1' ? true : false,
                thousandSplit: thousandSplit === '1' ? true : false,
                metricFormat: metricFormat,
                valueUnit,
              });
            } catch (e) {
              console.error('解析数据格式化数据失败', numFormatInfo);
            }
            setIsModalOpen(true);
          },
        };
      },
      [form],
    );

    const handleOk = () => {
      form.submit();
    };

    const handleCancel = async () => {
      setIsModalOpen(false);
    };
    const onFinish = (values: any) => {
      const { percentageAgain, metricFormat, thousandSplit, ...rest } = values;
      try {
        if (rest.numStyle === '0') {
          setNumFormat(
            fieldInfo?.type ?? '',
            fieldInfo?.index ?? 0,
            JSON.stringify(rest),
          );
        } else {
          const result: {
            percentageAgain?: string;
            metricFormat?: string;
            thousandSplit?: string;
          } = {};
          // 如果有百分比处理的话
          if (percentageAgain) {
            result.percentageAgain = percentageAgain ? '1' : '0';
          }
          if (metricFormat) {
            result.metricFormat = metricFormat;
          }
          result.thousandSplit = thousandSplit ? '1' : '0';

          setNumFormat(
            fieldInfo?.type ?? '',
            fieldInfo?.index ?? 0,
            JSON.stringify({ ...result, ...rest }),
          );
        }
      } catch (error) {
        console.error('序列化数据格式化失败', error, values);
      }
      setIsModalOpen(false);
    };

    // 类型的切换
    const onNumStyleChange = (value: string) => {
      const {
        percentageAgain,
        thousandSplit,
        metricFormat,
        numStyle,
        scale,
        valueUnit,
      } = JSON.parse(fieldInfo?.numFormatInfo || '{}');
      // 回填

      if (value === numStyle) {
        form.setFieldsValue({
          numStyle,
          scale,
          percentageAgain: percentageAgain === '1' ? true : false,
          thousandSplit: thousandSplit === '1' ? true : false,
          metricFormat: metricFormat,
          valueUnit,
        });
      } else {
        // 清空上次填的
        form.setFieldsValue({
          numStyle: value,
          scale: '2',
          percentageAgain: false,
          thousandSplit: false,
          metricFormat: '0',
          valueUnit: '',
        });
      }
    };

    const layout = {
      labelCol: { span: 4 },
      wrapperCol: { span: 20 },
    };

    return (
      <Modal
        title="数据格式"
        open={isModalOpen}
        onOk={handleOk}
        onCancel={handleCancel}
        width={320}
        centered
        footer={null}
        destroyOnClose
        closable={true}
        maskClosable={false}
      >
        <div className={styles['data-form']}>
          <Form
            {...layout}
            form={form}
            name="data-format"
            onFinish={onFinish}
            style={{ maxWidth: 600 }}
            colon={false}
            initialValues={{ numStyle: '0', precision: '0' }}
          >
            <Form.Item name="numStyle" label="类型">
              <Select
                placeholder="请选择类型"
                allowClear
                onChange={onNumStyleChange}
              >
                <Option value="0">默认</Option>
                <Option value="1">数值</Option>
                <Option value="2">百分比</Option>
              </Select>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.numStyle !== currentValues.numStyle
              }
            >
              {({ getFieldValue }) =>
                precisionShow.includes(getFieldValue('numStyle')) ? (
                  <>
                    <Form.Item name="scale" label="精度">
                      <Select def placeholder="请选择精度" allowClear>
                        {[...new Array(5).keys()].map((item) => (
                          <Option value={item} key={item}>
                            {item}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item name="thousandSplit" valuePropName="checked">
                      <Checkbox>千位分隔符</Checkbox>
                    </Form.Item>
                  </>
                ) : null
              }
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.numStyle !== currentValues.numStyle
              }
            >
              {({ getFieldValue }) =>
                numberShow.includes(getFieldValue('numStyle')) ? (
                  <>
                    <Form.Item name="metricFormat" label="度量">
                      <Select def placeholder="请选择度量" allowClear>
                        {metricFormatConfig.map((metric, index) => (
                          <Option value={`${index}`} key={`${index}`}>
                            {metric.text}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </>
                ) : null
              }
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.numStyle !== currentValues.numStyle
              }
            >
              {({ getFieldValue }) =>
                divideShow.includes(getFieldValue('numStyle')) ? (
                  <Form.Item name="percentageAgain" valuePropName="checked">
                    <Checkbox>除以100</Checkbox>
                  </Form.Item>
                ) : null
              }
            </Form.Item>
            <Form.Item noStyle shouldUpdate={true}>
              {({ getFieldValue }) =>
                precisionShow.includes(getFieldValue('numStyle')) ? (
                  <Form.Item label="预览" valuePropName="checked">
                    {dataFormatValue(
                      getFieldValue('numStyle') === '2' ? 0.85123232 : 1000,
                      {
                        numStyle: getFieldValue('numStyle'),
                        valueUnit: getFieldValue('valueUnit'),
                        percentageAgain: getFieldValue('percentageAgain'),
                        thousandSplit: getFieldValue('thousandSplit')
                          ? '1'
                          : '0',
                        metricFormat: getFieldValue('metricFormat'),
                        scale: getFieldValue('scale'),
                      },
                    )}
                  </Form.Item>
                ) : null
              }
            </Form.Item>
            <Form.Item
              name="valueUnit"
              label="单位"
              rules={[{ type: 'string', max: 10 }]}
            >
              <Input placeholder="请输入单位" maxLength={10} showCount />
            </Form.Item>
          </Form>
        </div>

        <p className={styles['footer']}>
          <Button
            className={styles['modal-btn']}
            style={{ background: 'white' }}
            onClick={handleCancel}
          >
            取消
          </Button>{' '}
          <Button
            className={styles['modal-btn']}
            type="primary"
            onClick={handleOk}
          >
            确定
          </Button>
        </p>
      </Modal>
    );
  },
);
