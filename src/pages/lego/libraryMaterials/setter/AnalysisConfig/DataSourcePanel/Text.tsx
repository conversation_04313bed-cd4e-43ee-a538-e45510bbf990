import { Tooltip } from '@blmcp/ui';
import React, { ReactNode, useEffect, useRef, useState } from 'react';
import ResizeObserver from 'resize-observer-polyfill';

interface TextProps {
  children: ReactNode; //
  text: string;
}

export const Text = ({ children, text }: TextProps) => {
  const [tooltipEnable, setTooltipEnable] = useState(false);
  const textRef = useRef<HTMLSpanElement>(null);
  // tooltipEnable 判断
  useEffect(() => {
    const resizeObserver = new ResizeObserver((entries) => {
      const scrollWidth = textRef.current?.scrollWidth ?? 0;
      const offsetWidth = textRef?.current?.offsetWidth ?? 0;
      if (scrollWidth > offsetWidth) {
        setTooltipEnable(true);
      } else {
        setTooltipEnable(false);
      }
    });
    if (textRef?.current) {
      resizeObserver.observe(textRef.current);
    }

    return () => {
      if (textRef.current) {
        resizeObserver.unobserve(textRef.current);
      }
    };
  }, [setTooltipEnable]);

  const child = React.Children.only(children);
  if (!React.isValidElement(child)) {
    return null;
  }
  return (
    <Tooltip
      title={text}
      mouseLeaveDelay={0}
      open={tooltipEnable ? undefined : false}
    >
      {React.cloneElement(
        child,
        { ...child.props, ref: textRef },
        child.props.children,
      )}
    </Tooltip>
  );
};
