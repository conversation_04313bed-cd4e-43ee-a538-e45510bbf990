import { SortType } from '@/pages/lego/components/types';

export interface ItemData {
  title: string;
  columnId: number;
  id?: number;
  key: string;
  dataType: number;
  fieldType: string;
  computeModeId: number;
  dateFormat?: string;
  advanceComputeModeId?: string;
  numberFormat?: string;
  numFormatInfo?: string; //  数据格式
  // 来自drop 的字段
  dropType?: string;
  onlyOne?: boolean;
  dataSourceId?: string;
  isAggr: boolean;
  sortType?: SortType;
  insertIndex?: number; // 拖拽字段需要插入的index
}

export interface AddFun {
  (data: ItemData): void;
}

export enum CalcMode {
  SUM = 6, //  求和
  Count = 1, // 计数
  Average = 5, //  平均值
  CountUniq = 2, //  去重计数
  Max = 3, //最大值
  Min = 4, //最小值'
}

export interface CalcType {
  key: string | number;
  label: string;
  children?: CalcType[];
  column?: boolean; // 按行
  row?: boolean; // 按列
}

export enum FieldType {
  Dim = 'dimensionInfo',
  Index = 'measureInfo',
}
export interface FieldItem {
  dataType: number;
  key: string;
  columnId: number;
  title: string;
  type: FieldType;
}

export type SetterData = Record<string, number | string | ItemData[]>;

// Dimension 对应接口中dateFormat枚举
// 维度日期函数格式化枚举
//      /**
//      * 年
//      */
//      YEAR(200)
//      /**
//      * 年-季
//      */
//      QUARTER_OF_YEAR(201)
//      /**
//      * 年-月
//      */
//      MONTH_OF_YEAR(202)
//      /**
//      * 年-月-日
//      */
//      YEAR_MONTH_DAY(203)

//      /**
//      * 年-月-日-小时
//      */
//      YEAR_MONTH_DAY_HOUR(204)
//      /**
//      * 年-周
//      */
//      WEEK_OF_YEAR(205)
//      /**
//      * 季度
//      */
//      QUARTER(300)
//      /**
//      * 月
//      */
//      MONTH(400)
//      /**
//      * 周
//      */
//      WEEK(500)
//      /**
//      * 星期
//      */
//      DAY_OF_WEEK(600)
//      /**
//      * 日期
//      */
//      DAY(700)

// 指标数值类型字段操作逻辑
//      --聚合函数 对应接口中computeModeId字段枚举
//     EXPR(0, "%s", 1),
//     COUNT(1, "COUNT(%s)", 1),
//     COUNT_DISTINCT(2, "COUNT_DISTINCT(%s)", 1),
//     MAX(3, "MAX(%s)", 1),
//     MIN(4, "MIN(%s)", 1),
//     AVG(5, "AVG(%s)", 1),
//     SUM(6, "SUM(%s)", 1),
//      --快速计算 同环比 对应接口中advanceComputeModeId字段枚举
//      /**
//      * 年环比
//      */
//     YOY_YEAR(10, 1, "年环比", TimeGranularity.YEAR),
//     /**
//      * 季环比
//      */
//     YOY_QUARTER(11, 1, "季环比", TimeGranularity.QUARTER),
//     /**
//      * 月环比
//      */
//     YOY_MONTH(12, 1, "月环比", TimeGranularity.MONTH),
//     /**
//      * 周环比
//      */
//     YOY_WEEK(13, 1, "周环比", TimeGranularity.WEEK),
//     /**
//      * 日环比
//      */
//     YOY_DAY(14, 1, "日环比", TimeGranularity.DAY),
//     /**
//      * 季度年同比
//      */
//     MOM_QUARTER_YEAR(20, 1, "季度_年同比", TimeGranularity.QUARTER),
//     /**
//      * 月-年同比
//      */
//     MOM_MONTH_YEAR(21, 1, "月_年同比", TimeGranularity.MONTH),
//     /**
//      * 日-月同比
//      */
//     MOM_DAY_MONTH(22, 1, "日_月同比", TimeGranularity.DAY),
//     /**
//      * 日-周同比
//      */
//     MOM_DAY_WEEK(23, 1, "日_周同比", TimeGranularity.DAY),
//     --快速计算 百分比
//     /**
//      * 列占比 百分比按列计算方式
//      */
//     PERCENTAGE(30, 2, "列占比", TimeGranularity.NONE),
//       --后端拆分
//           "advanceFunction": "PERCENTAGE",
//           "summationFunction": "SUM",
//           "measureMode": "PERCENTAGE"

//      添加总计：对应接口summationComputeModeId字段枚举
//      MEASURE_SUMMATION(31,"列总计")

// 计算字段枚举
//      --聚合函数
//      SUM(1)
//      AVG(2)
//      COUNT(3)
//      COUNT_DISTINCT(4)
//      MAX(5)
//      MIN(6)
//      --计算函数
//      ADDITION(1,"+")
//      SUBTRACTION(2,"-")
//      MULTIPLICATION(3,"*")
//      DIVISION(4,"/")
//      ABS(5,"ABS")
//      ROUND(6,"ROUND")
//      CEIL(7,"CEIL")
//      FLOOR(8,"FLOOR")
//      --逻辑函数
//      IF(9,"IF THEN ELSE")
//      CASE(10,"CASE WHEN THEN ELSE END")
//      IIF(11,"IIF")
