import { useEffect, useRef } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import type { FC } from 'react';
import type { Identifier, XYCoord } from 'dnd-core';
import styles from './index.less';

interface CardProps {
  id: string;
  index: number;
  moveCard: (dragIndex: number, hoverIndex: number) => void;
  // 序列类型： 默认number 数字递增，uppercase: 大写递增，
  children: React.ReactNode;
  dragType: string;
  allowedDropEffect: string;
  setDragFlag: (flag: boolean) => void;
  deleteItem: (index: number) => void;
  isDraggingField: boolean;
  extraParams: any;
}
interface DragItem {
  index: number;
  id: string;
  type: string;
}

export const Card: FC<CardProps> = ({
  id,
  index,
  moveCard,
  children,
  dragType,
  allowedDropEffect,
  deleteItem,
  setDragFlag,
}) => {
  const ref = useRef<HTMLDivElement>(null);

  const [{ handlerId }, drop] = useDrop<
    DragItem,
    void,
    { handlerId: Identifier | null }
  >({
    accept: dragType,
    collect(monitor) {
      return {
        handlerId: monitor.getHandlerId(),
      };
    },
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();

      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      // Get pixels to the top
      const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top;

      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }

      // Time to actually perform the action
      moveCard(dragIndex, hoverIndex);

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: dragType,
    item: () => {
      return { id, index };
    },
    collect: (monitor: any) => ({
      isDragging: monitor.isDragging(),
    }),
    end(item, monitor) {
      const clientOffset = monitor.getClientOffset();
      if (clientOffset) {
        // 获取到了鼠标坐标
        const { x, y } = clientOffset;
        const configBox =
          document
            .getElementById(`configBox_${allowedDropEffect}`)
            ?.getBoundingClientRect() ?? {};
        if (
          x < configBox.x + configBox?.width &&
          x > configBox.x &&
          y < configBox.y + configBox?.height &&
          y > configBox.y
        ) {
          setDragFlag(false);
        } else {
          deleteItem(index);
          setDragFlag(false);
        }
      }
    },
  });
  const opacity = isDragging ? 0 : 1;

  useEffect(() => {
    if (isDragging) {
      setDragFlag(true);
    } else {
      setDragFlag(false);
    }
  }, [isDragging, setDragFlag]);

  drop(drag(ref));

  return (
    <div
      key={id}
      ref={ref}
      className={styles['li-item']}
      style={{ opacity }}
      data-handler-id={handlerId}
    >
      {children}
    </div>
  );
};
