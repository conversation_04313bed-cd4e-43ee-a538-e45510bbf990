export const CalcMode = {
  SUM: 6, //  求和
  Count: 1, // 计数
  Average: 5, //  平均值
  CountUniq: 2, //  去重计数
  Max: 3, //最大值
  Min: 4, //最小值'
};

export const dateItems = [
  {
    key: 200,
    label: '年',
  },
  {
    key: 201,
    label: '年-季度',
  },
  {
    key: 202,
    label: '年-月',
  },
  {
    key: 203,
    label: '年-月-日',
  },
  {
    key: 204,
    label: '年-月-日-时',
  },
  {
    key: 206,
    label: '年-月-日-时分',
  },
  {
    key: 207,
    label: '年-月-日-时分秒',
  },
  {
    key: 205,
    label: '年-周',
  },

  {
    key: 300,
    label: '季度',
  },
  {
    key: 400,
    label: '月',
  },
  {
    key: 500,
    label: '周',
  },
  {
    key: 600,
    label: '星期',
  },
  {
    key: 700,
    label: '日',
  },
];

export const sortItems = [
  {
    key: 0,
    label: '不排序',
  },
  {
    key: 1,
    label: '升序',
  },
  {
    key: 2,
    label: '降序',
  },
];

export const Aggregate: any = {
  6: '求和',
  1: '计数',
  5: '平均值',
  2: '去重计数',
  3: '最大值',
  4: '最小值',
};
export type AggregateType = keyof typeof Aggregate;
