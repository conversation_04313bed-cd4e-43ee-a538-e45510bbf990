.table-operation-wrap {
  font-family: <PERSON><PERSON>ang SC;
  font-size: 13px;
  font-weight: normal;
  line-height: 20px;
  color: rgba(0, 0, 0, 0.9);
  padding-top: 10px;
}

.setting-title {
  margin-top: 8px;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  letter-spacing: 0em;
  color: rgba(0, 0, 0, 0.9);
}
.add-btn {
  color: rgba(0, 0, 0, 0.9);
  font-size: 14px;
  width: 114px;
}
.item-input {
  width: 84px !important;
  margin: 4px 0px !important;
}
.icon {
  cursor: pointer;
  margin-right: 2px;
  margin-left: 2px;
}
.icon-drag {
  cursor: move;
  margin-top: 5px;
  margin-right: 8px;
}
.warning-icon {
  position: absolute;
  top: 14px;
  left: 17px;
}
.title {
  font-size: 14px;
  font-weight: 500;
  line-height: 22px;
  color: rgba(0, 0, 0, 0.9);
  margin-top: 8px;
}
.text {
  .title;
  font-weight: normal;
}
.content {
  display: flex;
  margin-top: 16px;
  height: 388px;
}
.left {
  width: 250px;

  margin-right: 8px;
}
.right {
  display: flex;
  align-items: flex-start;
  justify-content: center;
  flex: 1;
  box-sizing: border-box;
  /* 线框/line-grayBlue3 */
  /* 样式描述：实线分割线、边框 */
  border: 1px solid #e7e8eb;

  z-index: 2;
  padding: 8px 0px;
  :global {
    /* stylelint-disable selector-class-pattern */
    textarea.ant-input:not(.ant-input-disabled),
    .BLM_Antd_content textarea.ant-input:not(.ant-input-disabled) {
      background: #fff !important;
    }
    textarea.ant-input:not(.ant-input-disabled):hover,
    .BLM_Antd_content textarea.ant-input:not(.ant-input-disabled):hover {
      background: #fff !important;
    }
    /* stylelint-enable selector-class-pattern */
    .ant-input:focus {
      border: none !important;
      box-shadow: none !important;
    }
    .ant-input:focus-visible {
      border: none !important;
      box-shadow: none !important;
    }
  }
}
.list-title {
  height: 38px;
  border-radius: 6px 6px 0px 0px;
  margin-top: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 8px;
  align-self: stretch;
  background: #f3f3f4;
  border: 1px solid #e7e8eb;
}
.list {
  height: 310px;
  overflow: auto;
}
.list-item {
  height: 40px;
  /* Radius控件圆角 */
  border-radius: 6px;
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px;
  cursor: pointer;
}

.editBox {
  // height: calc(100% - 120px);
  padding: 6px 10px;
  font-size: 12px;
  color: #303133;
  font-weight: normal;
  outline: none;
  white-space: break-spaces;
  flex: 1;
  overflow: hidden;
  > div {
    width: 100%;
    height: 100%;
    outline: none !important;
    font-family: PingFang SC;
    padding: 0;
    &:empty::before {
      content: 'qadasd';
      color: rgba(0, 0, 0, 0.3);
    }
  }
}

// 滚动条默认隐藏，滚动显示
.lego-bi-scroll-hide .cm-scroller::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}
.lego-bi-scroll-hide .cm-scroller::-webkit-scrollbar-thumb {
  background-color: transparent;
}
.lego-bi-scroll-hide:hover .cm-scroller::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
}

.freeze-cols {
  margin: 10px auto;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.9);
}
.freeze-title {
  margin-right: 20px;
  margin-left: 6px;
}

.item-box{
  transform: translate(0px, 0px);
  border-radius: 10px;
  background: rgb(255, 255, 255);
  margin-right: -10px;
  padding-right: 10px;
  >div>*{
    vertical-align: middle;
  }
  >div>i{
    line-height: 1;
  }
}