import { useRef, useState } from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { Input, message } from '@blmcp/ui';
import { ReactComponent as DeleIcon } from '@/assets/lego/delete-menu.svg';
import { ReactComponent as ConfigMenuIcon } from '@/assets/lego/config-menu.svg';

import { ReactComponent as DragIcon } from '@/assets/lego/drag-item.svg';
import { isValidName } from '@/pages/lego/utils/validate';
import type { XYCoord } from 'dnd-core';
import type { FC } from 'react';
import styles from './index.less';

const dragType = 'OperationSettingType';
interface CardProps {
  index: number;
  moveCard: (dragIndex: number, hoverIndex: number) => void;
  dragType?: string;
  deleteItem: () => void;
  configItem: () => void;
  inputChange: (value: string) => void;
  item: { label: string; key: string };
  disabled?: boolean;
}
interface DragItem {
  index: number;
  type: string;
}
let disabledDelete = false;
export const Card: FC<CardProps> = ({
  index,
  item,
  moveCard,
  configItem,
  inputChange,
  deleteItem,
  disabled,
}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [value, setValue] = useState(item.label);

  const [status, setStatus] = useState('');

  const inputValueChange = (e) => {
    setValue(e.target.value);
  };
  const inputBlur = () => {
    if (value.trim() === '') {
      setValue(item.label);
    } else if (value !== item.label) {
      inputChange(value);
    }
    setStatus('');
  };

  const [{}, drop] = useDrop<DragItem, void, Record<string, unknown>>({
    accept: dragType,
    hover(item: DragItem, monitor) {
      if (!ref.current) {
        return;
      }
      const dragIndex = item.index;
      const hoverIndex = index;

      // Don't replace items with themselves
      if (dragIndex === hoverIndex) {
        return;
      }

      // Determine rectangle on screen
      const hoverBoundingRect = ref.current?.getBoundingClientRect();

      // Get vertical middle
      const hoverMiddleY =
        (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;

      // Determine mouse position
      const clientOffset = monitor.getClientOffset();

      // Get pixels to the top
      const hoverClientY = (clientOffset as XYCoord).y - hoverBoundingRect.top;

      // Dragging downwards
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }

      // Dragging upwards
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      // Time to actually perform the action
      moveCard(dragIndex, hoverIndex);

      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      item.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag, preview] = useDrag({
    type: dragType,
    item: { index },
    canDrag: !disabled,
    collect: (monitor: any) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  drop(ref);
  const opacity = isDragging ? 0 : 1;
  return (
    <div ref={preview} className={styles['item-box']}>
      <div ref={ref} style={{ opacity }}>
        <div style={{ display: 'inline-block' }} ref={drag}>
          <DragIcon className={styles['icon-drag']} />
        </div>

        <Input
          maxLength="20"
          value={value}
          disabled={disabled}
          status={status}
          onChange={inputValueChange}
          onBlur={inputBlur}
          className={styles['item-input']}
          placeholder="操作列名称"
        />
        {disabled ? null : (
          <i
            onClick={() => {
              configItem();
            }}
          >
            <ConfigMenuIcon className={styles['icon']} />
          </i>
        )}
        {disabled ? null : (
          <i
            onClick={() => {
              if (!disabledDelete) {
                deleteItem();
                disabledDelete = true;
                setTimeout(() => {
                  disabledDelete = false;
                }, 1000);
              }
            }}
          >
            <DeleIcon className={styles['icon']} />
          </i>
        )}
      </div>
    </div>
  );
};
