import React, { useEffect, useState} from 'react';
import { Select } from '@blmcp/ui';
// import { Input } from "@alifd/next";

interface SetterProps {
  // 当前值
  value: [];
  // 默认值
  initialValue: [];
  // setter唯一输出
  onChange: (val: string) => void;
}

export default function LinkComponentSetter({value,onChange}: SetterProps){
  const [list , setList] = useState([])
  useEffect(()=>{
    const metaMap = window.LeopardWebCompMeta.components.reduce((curr: any, next: any)=>{
      curr[next.componentName] = next.componentBehavior
      return curr
    },{})
    const Schema = window.AliLowCodeEngine.project.exportSchema()
    const buckets = [...Schema.componentsTree];
    const serverList = []
    for(const item of buckets){
      if(metaMap[item.componentName]){
        serverList.push({
          value: item.id,
          label: item.componentName
        })
      }
      if(item.children && item.children.length){
        buckets.push(...item.children)
      }
    }
    setList(serverList)
  },[])

  return (
    <Select
      value={value}
      mode={'multiple'}
      style={{ width: 120 }}
      onChange={(value)=>{
        onChange(value)
      }}
      options={list}
    />
  );
}
LinkComponentSetter.displayName = 'LinkComponentSetter'
