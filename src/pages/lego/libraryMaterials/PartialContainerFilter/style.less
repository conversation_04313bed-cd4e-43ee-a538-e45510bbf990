.fdCell-PartialContainerFilter{
  position: relative;
}
.PartialContainerFilter-box{
  padding: 10px;
  width: 100%;
  height: 100%;
  background-color: #fff;
  min-height: 50px;
  position: relative;
  border-radius: 8px;

  .fd-layout-row-flex{
    height: auto;
  }
  .fd-layout-cell{
    height: 32px;
  }
  // 重写slot 样式
  .lc-container-placeholder {
    border: none;
    position: absolute;
    background: #fff;
    min-height: auto;
    color: transparent;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    height: auto;
    width: auto;
    min-height: 30px;
    &::before {
      content: '请配置';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
      z-index: 2;
      width: 100%;
      text-align: center;
      font-family: PingFang SC;
    }
    &::after {
      content: '';
      position: absolute;
      background: #f3f3f4;
      border: 1px dashed #dcdde1;
      height: 100%;
      border-radius: 8px;
      width: 100%;
      top: 0;
    }
  }

  .fd-layout-row-flex{
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    display: grid;
    width: 100%;
  }
  .fdCell-SearchButton{
    flex: none !important;
    .lego-search-wrap {
      min-width: none !important;
    }
  }
}

  .PartialContainerFilter-empty{
    padding: 10px;
    width: 100%;
    background: #fff;
    top: 0px;
    left: 0px;
    right: 0px;
    bottom: 0px;
    >div{
      min-height: 30px;
      width: 100%;
      height: 100%;
      display: flex;
      background: #f3f3f4;
      border: 1px dashed #dcdde1;
      border-radius: 8px;
      font-size: 12px;
      color: rgba(0, 0, 0, 0.6);
      align-items: center;
      justify-content: center;
    }
  }