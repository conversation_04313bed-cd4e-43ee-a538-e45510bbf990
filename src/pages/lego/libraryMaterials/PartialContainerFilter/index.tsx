import { ReactNode, useEffect, useLayoutEffect, useMemo } from 'react';
import './style.less';

interface PartialContainerFilterProps {
  __id?: string; // 预览模式
  componentId?: string; // 编辑模式
  filters: {
    label: string; // 筛选器label
    type: string; // 筛选器类型
    links: {
      dataSourceId: number; // 数据集id
      columnId: number; // 选择的字段id
      componentId: string; // 前端图表组件id
      elementId: string; // 服务图表组件id
    }[]; // 关联图表
  }[];
  uuid: string;
  children: React.ReactNode[];
}

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  // 不参与获取数据集相关事情
  notQueryData: true,
  // 排序
  sort: 100,
};

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
  },
};

export const MergedComponentMate = {
  configure: {
    advanced: {
      initialChildren: [
        {
          componentName: 'FDRow',
          props: {
            isPartialContainerFilter: true,
            className: 'PartialContainerFilter-row',
          },
          selected: 'parent',
        },
      ],
    },
  },
};

export const ComponentRule = {
  isContainer: true,
  nestingRule: {
    childWhitelist: ['FDRow'],
  },
};

export const PartialContainerFilter = function (
  props: PartialContainerFilterProps,
) {
  const isEdit = props.__designMode === 'design';
  if (!isEdit && !props.children?.[0]?.props?.children) {
    return (
      <div className="PartialContainerFilter-empty">
        <div>未配置筛选器</div>
      </div>
    );
  }

  // const childrenLength = props.children[0]?.props?.children?.length;
  // if (childrenLength < 2) {
  //   return (
  //     <div className="PartialContainerFilter-empty">
  //       <div>未配置筛选器</div>
  //     </div>
  //   );
  // }

  // const filters: any[] = useMemo(() => {
  //   const _filters: any[] = [];
  //   if (!childrenLength) return _filters;
  //   for (let i = 0; i < childrenLength; i++) {
  //     const child: any = props.children[i];
  //     if (child) {
  //       _filters.push({
  //         key: child.key,
  //         label: child.props?.children[0]?.props.title,
  //         type: child.props?.children[0]?.props._componentName,
  //         chartLinks: child.props?.children[0]?.props.chartLinks,
  //       });
  //     }
  //   }
  //   return _filters;
  // }, [childrenLength, props.children]);

  // console.log('filtersfilters', filters, childrenLength);

  return <div className="PartialContainerFilter-box">{props.children}</div>;
};

PartialContainerFilter.displayName = '局部筛选器';
