/**
 * 日期组件
 */
import { useEffect, useState, useRef, useCallback, useMemo } from 'react';
import dayjs from 'dayjs';
import { useLegoReport } from '@blm/bi-lego-sdk/dist/es/utils';
import { BLMIconFont } from '@blmcp/ui';
import { useMutationObserver } from 'ahooks';
import { reportStore } from '@blm/bi-lego-sdk/dist/es/utils';
import relationCenterExp from '@/pages/lego/libraryMaterials/module/RelationCenter';
import useComponent from '@/pages/lego/hooks/useComponent';
import { DatePickerFilter as DatePickerFilterView } from '../../components/Filter/DatePickerFilter';
import { getOtherPresets } from '../../components/Filter/DatePickerFilter/tools';
import FilterWrapper from '../wrapper/FilterWrapper';
import { isTodayInRange } from '../../components/Filter/DatePickerFilter/tools';
import { DEFAULT_DATE_RANGE } from './constants/dateRangeOptions';
import { getLInkChartRealDataFlagById } from './utils';

interface DatePickerFilterProps {
  title?: string;
  dataSetConfig: any;
  __id: any;
  componentId: any;
  defaultValue: any;
  dateRange?: number;
  uuid: string;
  partialContainerFilterId?: string;
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          label: '维度',
          key: 'dimensionInfo',
          onlyOne: true,
          ignoreSetDefaultComputeType: true,
          placeholder: '仅支持拖入 1 个字段',
        },
      ],
      dateOnly: true,
      indexDisabled: true,
    },
  },
  // 默认时间
  defaultValue: {
    componentName: 'DatePickerDefaultValue',
    // 该组件其他配置项
    props: {
      // 拖拽进入的，还需要配置数据集
      type: 'drop-component',
    },
  },
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {
      noPadding: true,
    },
  },
  // 可选范围
  dateRange: {
    componentName: 'DatePickerRange',
    // 该组件其他配置项
    props: {},
  },
  // 别名
  title: {
    componentName: 'AliasSetter',
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  dataType: 1,
};

const temp_dateMap: any = {};
setTimeout(() => {
  const { project } = window.AliLowCodeEngine || {};
  // 暂时通过监听历史变化，重置缓存，后面统一处理默认值问题
  project?.currentDocument?.history.onChangeCursor(() => {
    // eslint-disable-next-line guard-for-in
    for (let key in temp_dateMap) {
      delete temp_dateMap[key];
    }
  });
});

// 逻辑层
export const DatePickerFilter = (props: DatePickerFilterProps) => {
  const {
    __id,
    componentId,
    defaultValue,
    dateRange = DEFAULT_DATE_RANGE,
    uuid,
  } = props;
  // 重置强制刷新用
  const [key, setKey] = useState(() => Date.now());
  const relationCenter = relationCenterExp(uuid);
  const defaultValueRef = useRef(defaultValue);
  const dateRangeRef = useRef(dateRange);
  const filterId = __id ?? componentId ?? '';
  // const { dimensionInfo } = dataSetConfig ?? {};
  // const dimension = dimensionInfo?.[0];
  const presets = getOtherPresets();
  // const presetList = getOtherPresets();
  const [_, setMeta] = useComponent(filterId);
  const [reportMeta] = useLegoReport(uuid);
  const isEdit = props.__designMode === 'design';
  // 时间单位
  const dateUnitRef = useRef(6);
  // 选中时间
  const [selectDate, setSelectDate] = useState<any>();
  // 预选中
  const [hoverDate, setHoverDate] = useState<any>();
  //数据集 实效
  const [realDataFlag, setRealDataFlag] = useState<number>();
  const dateRef = useRef(null);
  useEffect(() => {
    setMeta(
      {
        query() {
          relationCenter.notify('all');
        },
      },
      true,
    );
  }, []);

  const getDefaultDateItem = useCallback(() => {
    const dateType = defaultValue?.dateType;
    // 外部自定义默认时间
    if (dateType === 'customDate') {
      // 如果 defaultValue 中有自定义的 value，使用它；否则使用默认值
      const customValue =
        defaultValue?.value || presets.find((item) => item.type === 'last-7');
      return {
        label: null,
        title: '自定义',
        timeType: 6,
        type: 'customDate',
        value: customValue,
      };
    }
    // 清空场景
    if (dateType === 'all') {
      return presets.find((item) => item.type === 'clear-time');
    }

    // 快捷默认时间
    return (
      presets.find((item) => item.type === dateType) ||
      presets.find((item) => item.type === 'last-7')
    );
  }, [defaultValue, presets]);

  const defaultDateItem = getDefaultDateItem();
  // 配置项变更手动重置为当前默认值，并刷新图表数据
  const handleDateChange = useCallback(
    (val, newField) => {
      if (dateRangeRef.current !== dateRange) {
        dateRangeRef.current = dateRange;
        setKey(Date.now());
      }
      if (defaultValue?.dateType === 'customDate') {
        defaultValueRef.current = defaultValue;
        temp_dateMap[filterId] = {
          value:
            defaultValue?.value ||
            presets.find((item) => item.type === 'last-7'),
          type: 6, // CUSTOM 类型
        };
        dateUnitRef.current = 6;
      } else if (
        JSON.stringify(defaultValueRef.current) !== JSON.stringify(defaultValue)
      ) {
        defaultValueRef.current = defaultValue;
        dateUnitRef.current = defaultDateItem.timeType;
        temp_dateMap[filterId] = null;
        setKey(Date.now());
      }
    },
    [dateRange, defaultValue, filterId, presets],
  );
  const handleFieldLabel = (value: any[]) => {
    return value?.length
      ? [
          dayjs(value[0]).startOf('day').format('YYYY-MM-DD') +
            ' ~ ' +
            dayjs(value[1]).endOf('day').format('YYYY-MM-DD'),
        ]
      : [];
  };
  const handleDefaultValue = (value: any[]) => {
    return value?.length
      ? [
          dayjs(value[0]).startOf('day').valueOf(),
          dayjs(value[1]).endOf('day').valueOf(),
        ]
      : [];
  };

  const FooterRender = (props) => {
    return (
      <div>
        {hoverDate?.[0] &&
        hoverDate?.[1] &&
        isTodayInRange(hoverDate) &&
        realDataFlag !== 1 ? (
          <div>
            <BLMIconFont
              type="BLM-ic-caution"
              style={{ color: '#FF7D00', marginRight: '5px' }}
            />
            当前筛选器查询范围内包含离线图表，对应今日数据为空
          </div>
        ) : (
          <div>
            <BLMIconFont
              type="BLM-ic-caution"
              style={{ color: '#FF7D00', marginRight: '5px' }}
            />
            您可以查询到数据范围内的所有数据
          </div>
        )}
      </div>
    );
  };

  useMutationObserver(
    (mutationsList) => {
      // mutationsList.forEach(() => setCount((c) => c + 1));
      const inputChange = mutationsList?.filter(
        (v) => v.target.nodeName === 'INPUT',
      );
      const inputs = document
        .querySelector('.legoSelectBorder')
        ?.querySelectorAll('[date-range]');
      if (inputs && inputChange.length) {
        const hoverValue = [...inputs].map((v: any) => v.value);
        if (hoverValue[0] && hoverValue[1]) {
          setHoverDate([
            new Date(hoverValue[0]).getTime(),
            new Date(hoverValue[1]).getTime(),
          ]);
        } else {
          setHoverDate(null);
        }
      }
    },
    dateRef,
    { attributes: true, subtree: true },
  );
  const onOpenChange = () => {
    let data = selectDate;
    const realDataFlag = props.partialContainerFilterId
      ? getLInkChartRealDataFlagById(filterId)
      : reportStore.get(uuid)?.realDataFlag;
    if (!selectDate) {
      if (defaultDateItem?.value) {
        data = [
          defaultDateItem.value[0]?.getTime(),
          defaultDateItem.value[1]?.getTime(),
        ];
      }
    }
    setRealDataFlag(realDataFlag);
    setHoverDate(data);
  };
  return (
    <FilterWrapper
      componentProps={props}
      label={isEdit || reportMeta.oldVersion > '2.6.0'}
      defaultValue={handleDefaultValue(defaultDateItem?.value)}
      onChange={handleDateChange}
      handleFieldLabel={handleFieldLabel}
      filterExtendField={(field, resetType) => {
        return {
          dateFilterRelativeUnit: resetType
            ? defaultDateItem.timeType
            : dateUnitRef.current,
        };
      }}
    >
      {({ value, onChange, fieldItem }) => {
        let valueChange = [] as any;
        if (value?.length) {
          valueChange = [dayjs(value[0]).toDate(), dayjs(value[1]).toDate()];
        }
        return (
          <div ref={dateRef}>
            <DatePickerFilterView
              key={key}
              pickerType="picker-filter"
              handleChange={(date, dateUnit) => {
                dateUnitRef.current = dateUnit;
                onChange(date, dateUnit);
                setSelectDate(date);
              }}
              open
              defaultValue={defaultDateItem}
              maxStep={dateRange - 1}
              value={valueChange}
              placeholder={
                reportMeta.oldVersion > '2.6.0' &&
                (props.title || fieldItem.title || '日期')
              }
              onOpenChange={onOpenChange}
              renderExtraFooter={(value: any) => {
                return <FooterRender defaultValue={defaultDateItem} />;
              }}
            />
          </div>
        );
      }}
    </FilterWrapper>
  );
};

DatePickerFilter.displayName = '时间筛选器';
