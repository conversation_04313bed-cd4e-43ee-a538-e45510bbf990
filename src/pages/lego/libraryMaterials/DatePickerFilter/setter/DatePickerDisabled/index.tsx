import React from 'react';
import { Switch } from '@blmcp/ui';
import { isHubble } from '@/utils/hubble';
import styles from './index.less';
import './index.scss';

interface SetterProps {
  value: any;
  onChange: any;
  type: string;
  field: any;
}

export default ({ onChange, value }: SetterProps) => {
  const selectVal = value || false;
  const switchChange = (value: any) => {
    onChange(value);
  };

  return (
    <div>
      {isHubble ? (
        <div className={styles['setter-date-picker-default-value']}>
          <div className={styles['title']}>是否禁用时间组件</div>
          <div className={styles['span-text']}>
            否
            <Switch defaultChecked={selectVal} onChange={switchChange} />是
          </div>
        </div>
      ) : null}
    </div>
  );
};
