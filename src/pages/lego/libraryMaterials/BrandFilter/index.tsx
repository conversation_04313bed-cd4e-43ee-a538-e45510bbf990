import { BLMTenantList } from '@blmcp/peento-businessComponents';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch } from '@/utils/store';
import FilterWrapper from '../wrapper/FilterWrapper';
import stateContext from '../../context/filterContext';

interface NewTenantFilterProps {
  __id?: string; // 预览模式
  componentId?: string; // 编辑模式
  uuid: string; // 唯一标识
}

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  // 不参与获取数据集相关事情
  notQueryData: true,
};

export const BrandFilter = function (props: NewTenantFilterProps) {
  const BusinessType = Number(sessionStorage.currentBusinessGroup || '1');
  const ref = useRef(null);
  const context = stateContext(props.uuid);
  const [_, dispatch] = useDispatch(context);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    function get() {
      setTimeout(() => {
        const data = ref.current?.getOptions() || [];
        if (data.length === 0) {
          get();
        } else {
          dispatch({
            type: 'tenant',
            payload: [data[0].value],
          });
          setLoading(false);
        }
      }, 100);
    }
    if (!context.context.get(context.useKey)?.tenant?.length) {
      get();
    }
  }, []);

  return (
    <FilterWrapper
      storeField="tenant"
      loading_data={true}
      loading={loading}
      // defaultValue={Number(config.get('basicInfo.defaultTenantId')) || 1}
      componentProps={props}
      fieldProps={useMemo(() => {
        return {
          key: 'tenant_id',
          columnId: 100000013,
          dataType: 1,
        };
      }, [])}
    >
      <BLMTenantList
        style={{ maxWidth: '100%' }}
        platformUsageList={[1, 2]}
        isBusinessGroup={true}
        isShowTestTenantsOnline={true}
        businessGroup={[BusinessType]}
        businessType={[BusinessType]}
        platformStatusEnum={[1, 2]}
        clearable={false}
        ref={ref}
      />
    </FilterWrapper>
  );
};
