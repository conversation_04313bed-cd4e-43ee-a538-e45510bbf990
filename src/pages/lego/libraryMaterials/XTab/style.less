.xtab {
  // min-height: 300px;
  width: 100%;
  height: 100%;
  // background-color: #f0f2f5;
  background: #fff;
  border-radius: 8px;
  &.mobile {
    .xtab-list {
      .xtab-list-wrapper {
        > div {
          > span {
            max-width: 130px;
          }
        }
        > div.selected > span {
          max-width: initial;
        }
      }
    }
    .xtab-item-wrapper {
      height: auto;
      .xtab-item-inner {
        padding-bottom: 0;
        &.empty {
          height: 150px;
        }
      }
    }
    .lego-xtab-item-list-dropdown {
      ul {
        max-height: 150px;
        overflow-y: auto;
      }
    }
  }
  &.xtab-switching .fd-layout-cell {
    overflow: hidden;
  }
}

.fdCell-XTab {
  overflow: hidden;
  min-height: 130px;
  height: auto !important;
}

.xtab-list {
  position: relative;
  background: #fff;
  border-bottom: 1px solid #e7e8eb;
  border-radius: 8px 8px 0 0;
  padding: 0 10px;
  display: flex;
  .ant-dropdown-menu {
    max-height: 200px;
    overflow-y: auto;
  }
  .xtab-list-operations {
    width: 50px;
    height: 50px;
    text-align: center;
    line-height: 50px;
  }
}

.xtab-list-wrapper {
  white-space: nowrap;
  overflow: auto;
  overflow-y: hidden; // 隐藏滚动条 纵向
  flex: 1;
  line-height: 1;
  > div {
    // display: inline-block;
    padding: 0 10px;
    cursor: pointer;
    height: 50px;
    // line-height: 60px;
    display: inline-flex;
    align-items: center;
    > span {
      font-size: 16px;
      font-weight: 500;
      line-height: 1;
      font-family: PingFang SC;
      font-weight: normal;
      color: rgba(0, 0, 0, 0.6);
      overflow: hidden;
      max-width: 275px;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
      height: 18px;
    }
  }
  .selected {
    position: relative;
    > span {
      color: #366cfe;
      max-width: initial;
    }
    &::after {
      content: '';
      width: 100%;
      height: 2px;
      background: #366cfe;
      position: absolute;
      bottom: 0;
      left: 0;
    }
  }
}

// 隐藏滚动条
.xtab-list-wrapper {
  scrollbar-color: transparent;
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar-thumb {
    background: transparent;
  }
  &::-webkit-scrollbar {
    width: 0;
    height: 0;
    display: none;
  }
  &::scrollbar-thumb {
    background: transparent;
  }
  &::scrollbar {
    width: 0;
    height: 0;
    display: none;
  }
}

// 每个 slot 盒子
.xtab-item-wrapper {
  padding: 16px 16px 0 16px;
  height: calc(100% - 50px);
  // height: 100%;
  overflow: auto;
  > div {
    min-height: calc(100% - 1px);
    position: relative;
    padding-bottom: 16px;
    // 重写slot 样式
    .lc-container-placeholder {
      // background: #fff url(./icon/empty.png) no-repeat center;
      border: none;
      // background-size: 200px;
      position: absolute;
      background: #fff;
      &::before {
        content: '请点击或拖拽添加图表/控件';
        position: absolute;
        top: calc(50% - 6px);
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
        z-index: 2;
        width: 100%;
        text-align: center;
        font-family: PingFang SC;
      }
      &::after {
        content: '';
        position: absolute;
        background: #f3f3f4;
        border: 1px dashed #dcdde1;
        height: calc(100% - 16px);
        border-radius: 8px;
        width: 100%;
        top: 0;
      }
    }
    &:empty::before {
      content: '';
      position: absolute;
      width: 100%;
      height: calc(100% - 2px);
      background: #fff url(./icon/emptynotext.png) no-repeat center;
      border: none;
      background-size: 100px;
      position: absolute;
    }
  }
  .fd-layout-cell > div {
    border: 1px solid #eee;
    border-radius: 8px;
  }
  .fd-layout-cell > .fd-layout-row-flex {
    border: none;
    border-radius: 0;
  }
  .m-fd-cell {
    > div {
      border: 1px solid #eee;
      border-radius: 8px;
    }
    > .fd-layout-row-flex {
      border: none;
      border-radius: 0;
    }
  }
}
.ant-popover-inner:has(.tab-rich-tips) {
  /* 样式 */
  max-height: 300px;
  max-width: 263px;
  overflow-y: auto;
  padding: 12px !important;
  word-wrap: break-word;
}
.ant-popover-inner {
  .tab-rich-tips {
    color: #1d2129;
    p:last-child {
      margin-bottom: 0;
    }
  }
}

.item-tips-btn {
  width: 22px;
  height: 22px;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  border-radius: 4px;
  margin-left: 4px;
  &:hover {
    background: #f2f3f5;
  }
  color: #4e5969;
}
