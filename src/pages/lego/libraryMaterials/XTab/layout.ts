import { Node } from '@alilc/lowcode-engine';
import { store } from '@/pages/lego/hooks/useComponent';

const tabsPadding = 16 * 2;

/**
 * 获取画布宽度
 */
export function getDesignerWidth() {
  // 画布容器
  const designerContainer = document.getElementsByClassName(
    'lc-workbench-center',
  );
  let designerWidth = window.innerWidth - 400;
  if (
    designerContainer &&
    designerContainer[0] &&
    designerContainer[0].offsetWidth
  ) {
    designerWidth = designerContainer[0].offsetWidth;
  }
  return designerWidth;
}

/**
 * 根据屏幕宽度获取物料最小宽度
 */
function getMinWidthByScreenWidth() {
  // 浏览器窗口宽度
  const screenWidth = window.innerWidth;
  // 画布宽度
  // const designerWidth = getDesignerWidth() - 20;
  // let minWidth = (designerWidth - 20) * 0.33333333;
  // if (screenWidth >= 1680) {
  //   minWidth = (designerWidth - 40) * 0.2;
  // }
  // return minWidth;
  return screenWidth >= 1500 ? 291 : 195;
}

/**
 * 判断当前CELL 内是否存在ROW
 */
export function checkCellNodeHasRow(cellNode) {
  const childNodes = cellNode.schema.children || [];
  return !!childNodes.find((f) => f.componentName === 'FDRow');
}

// 获取 node 宽度
function getNodeWidth(node: any, minWidth: number, boxWidth: number) {
  const rect = node.getRect();
  if (!rect && node.schema.props?.style?.width) {
    const width = node.schema.props.style.width.slice(0, -1);
    return (width / 100) * boxWidth;
  }
  return rect?.width || minWidth;
}

// 获取cell 下所有的子集的宽度
function getCellChildWidth(
  cellNode: Node,
  minWidth: number,
  boxWidth: number,
  callback: () => void,
) {
  if (checkCellNodeHasRow(cellNode) && !cellNode.schema.props?.style?.width) {
    // 循化当前子集，取最大宽度和
    let widthList: number[] = [];
    cellNode?.children?.forEach((row: any) => {
      let num = (row.children.size - 1) * 10;
      row.children.forEach((cell: Node) => {
        num += getCellChildWidth(cell, minWidth, boxWidth, callback);
      });
      widthList.push(num);
    });
    // 这个得求最小的
    return Math.max(...widthList);
  } else {
    const width = getNodeWidth(cellNode, minWidth);
    // 如果当前组件是tab 组件需要加上padding
    if (
      (cellNode.componentName === 'XTab' && width + tabsPadding < minWidth) ||
      width < minWidth
    ) {
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      callback && callback();
    }

    return width;
  }
}

/**
 * 获取当前拖拽节点及其兄弟节点的宽度+schema
 * @param currentNode
 * @returns
 */
export function getBrotherWidth(currentNode, boxWidth: number) {
  const res = {
    prev: [],
    next: [],
    curr: [],
    isbrak: false,
  };
  const minWidth = getMinWidthByScreenWidth();
  function handBrak() {
    res.isbrak = true;
  }
  function handlePrev(prev) {
    const { id, schema, prevSibling } = prev;
    // const styleWidth = getDomStyleWidth(prev);
    const info = {
      id,
      schema,
      node: prev,
      // width: styleWidth || getMinWidthByScreenWidth(),
      width: getCellChildWidth(prev, minWidth, boxWidth, handBrak),
    };
    res['prev'].unshift(info);
    if (prevSibling) {
      handlePrev(prevSibling);
    }
  }
  function handleNext(next) {
    const { id, schema, nextSibling } = next;
    // const styleWidth = getDomStyleWidth(next);
    const info = {
      id,
      schema,
      node: next,
      // width: styleWidth || getMinWidthByScreenWidth(),
      width: getCellChildWidth(next, minWidth, boxWidth, handBrak),
    };

    res['next'].push(info);
    if (nextSibling) {
      handleNext(nextSibling);
    }
  }
  const { id, schema, prevSibling, nextSibling } = currentNode;
  // const styleWidth = getDomStyleWidth(currentNode);
  const info = {
    id,
    schema,
    node: currentNode,
    // width: styleWidth || getMinWidthByScreenWidth(),
    // 这里必须精确的
    width: getNodeWidth(currentNode, minWidth, boxWidth),
  };

  if (info.width < minWidth) {
    handBrak();
  }

  res['curr'].push(info);
  if (prevSibling) {
    handlePrev(prevSibling);
  }
  if (nextSibling) {
    handleNext(nextSibling);
  }
  return res;
}

// 换行
function wrapNode(nextRowNode: any, currentNode: any) {
  function getParentNode(currNode) {
    if (currNode.parent.componentName === 'FDRow') {
      return currNode.parent.parent;
    } else {
      return getParentNode(currNode.parent);
    }
  }

  // 获取当前页面node
  const currPage = getParentNode(nextRowNode);
  // 当前元素所在行 ✨✨✨ 换行逻辑调整地方，需判断👴辈是不是Page， 不是需要追溯
  let currRow;
  if (currentNode?.parent?.parent?.componentName === 'Page') {
    currRow = currentNode?.parent;
  } else {
    currRow = currentNode.parent;
  }
  // const currRow = cbNode?.parent;
  // 当前元素所在行索引
  const currRowIndex = currRow?.index;

  const nextRowNodeSchema = nextRowNode.schema;
  const nextRowNodeSchemaChildren = nextRowNodeSchema.children?.map((c) => {
    c.props['__regeneration__'] = true;
    return c;
  });
  nextRowNode.remove();
  // 判断 nextRowNodeSchemaChildren 数组是不是 row，如果是，则不再嵌套一层
  if (nextRowNodeSchemaChildren.find((f) => f.componentName === 'FDRow')) {
    window.__lego_insertNodes(
      currPage,
      nextRowNodeSchemaChildren,
      currRowIndex + 1,
    );
  } else {
    nextRowNodeSchema['children'] = nextRowNodeSchemaChildren;
    // 换行元素嵌套FDRow
    const newRowSchema = {
      componentName: 'FDRow',
      children: [nextRowNodeSchema],
    };
    window.__lego_insertNode(currPage, newRowSchema, currRowIndex + 1);
  }
}

// 比较一行宽度累计是否大于box宽度
function checkWidth(infoNode: any, boxWidth: number) {
  const gridWidth = 10;
  const { curr, next, prev } = infoNode;
  const list = [...curr, ...next, ...prev];
  let width = (list.length - 1) * gridWidth;
  list.forEach((p) => {
    width += Number(p.width);
  });
  return width > boxWidth;
}

export default function (currentNode: Node, slotKey: string) {
  // const slotNode = currentNode.children?.find(
  //   (f) => f?.propsData?.key === slotKey,
  // );

  // if (!slotNode) return false;
  const boxWidth = currentNode.getRect()?.width || 0;

  currentNode.children?.forEach((slotNode) => {
    slotNode.children?.forEach((node) => {
      const childrenNode = node.children?.get(0);
      // console.log('childrenNode', childrenNode);
      if (!childrenNode) return false;
      const infoNode = getBrotherWidth(childrenNode, boxWidth);

      if (
        (infoNode.isbrak || checkWidth(infoNode, boxWidth)) &&
        infoNode.next.length
      ) {
        wrapNode(infoNode.next[infoNode.next.length - 1].node, node);
      }
    });
  });
}
