import { But<PERSON>, Modal, Tooltip } from '@blmcp/ui';
import { PlusOutlined } from '@ant-design/icons';
import { ReactComponent as WarningIcon } from '@/assets/lego/warning.svg';

import styles from './index.less';
import { CardList } from './CardList';

const { confirm } = Modal;

const mergeChildren = function (node: any, items: any[]) {
  const map: any = {};

  items.forEach((item) => {
    const tabItem = Object.assign({}, item);
    map[item.key] = tabItem;
  });
  node.children.mergeChildren(
    (child: any) => {
      const key = String(child.getPropValue('key'));
      if (Object.hasOwnProperty.call(map, key)) {
        child.setPropValue('label', map[key].label);
        child.setPropValue('key', map[key].key);
        child.setPropValue('selected', 'parent');
        child.setPropValue('placeholder', ' ');
        delete map[key];
        return false;
      }
      return true;
    },
    () => {
      const items = [];
      for (const key in map) {
        if (Object.hasOwnProperty.call(map, key)) {
          items.push({
            componentName: 'XTabItem',
            props: map[key],
            selected: 'parent',
            placeholder: ' ',
          });
        }
      }
      return items;
    },
    (child1: any, child2: any) => {
      const a = items.findIndex(
        (item) => String(item.key) === String(child1.getPropValue('key')),
      );
      const b = items.findIndex(
        (item) => String(item.key) === String(child2.getPropValue('key')),
      );
      return a - b;
    },
  );
};

export default function ({ onChange, value = [], selected }) {
  console.log(value, 'resetresetresetresetresetresetreset');
  const inputChange = (index: number) => (val: string) => {
    const newValue = [...value];
    newValue[index]['label'] = val;
    onChange(newValue);
    mergeChildren(selected, newValue);
  };

  const editTipsItem = (index: number, str: string) => {
    const newValue = [...value];
    newValue[index].richTips = str;
    console.log(newValue, index, str, '富文本信息');
    onChange(newValue);
    mergeChildren(selected, newValue);
  };

  // 删除某个元素
  const deleteItem = (index: number) => () => {
    confirm({
      title: '确认删除吗？',
      icon: <WarningIcon className={styles['warning-icon']} />,
      content: '是否删除当前页签和页签内的所有内容',
      autoFocusButton: null,
      onOk() {
        const newValue = [...value];
        newValue.splice(index, 1);
        onChange(newValue);
        mergeChildren(selected, newValue);
      },
      onCancel() {},
    });
  };

  // 移动卡片
  const moveCard = (dragIndex: number, hoverIndex: number) => {
    const newValue = [...value];
    const item = newValue.splice(dragIndex, 1);
    newValue.splice(hoverIndex, 0, item[0]);
    onChange(newValue);
    mergeChildren(selected, newValue);
  };

  return (
    <div>
      <div className={styles['setting-title']}>选项卡</div>
      {value.length > 0 ? (
        <CardList
          value={value}
          moveCard={moveCard}
          deleteItem={deleteItem}
          inputChange={inputChange}
          editTipsItem={editTipsItem}
        />
      ) : null}

      <Tooltip
        title={'最多支持配置10个选项卡'}
        open={value?.length >= 10 ? undefined : false}
      >
        <Button
          type="dashed"
          disabled={value?.length >= 10}
          icon={<PlusOutlined />}
          className={styles['add-btn']}
          onClick={() => {
            const newValue = [
              ...value,
              {
                label: '未命名',
                key: 'tab' + Date.now(),
                placeholder: ' ',
                richTips: '',
              },
            ];
            onChange(newValue);
            mergeChildren(selected, newValue);
          }}
        >
          添加选项卡
        </Button>
      </Tooltip>
    </div>
  );
}
