import { BLMFleetSelect } from '@dj-blm/lego-components';
import { useRef, useMemo } from 'react';
import { config } from '@blm/bi-lego-sdk/dist/es/utils';
import { useDispatch } from '@/utils/store';
import FilterWrapper from '../wrapper/FilterWrapper';
import stateContext from '../../context/filterContext';

interface CityFilterProps {
  __id?: string; // 预览模式
  componentId?: string; // 编辑模式
  link?: string;
  uuid: string;
}

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  // 不参与获取数据集相关事情
  notQueryData: true,
};

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {},
  },
};

export const DjFleetFilter = function (props: CityFilterProps) {
  const context = stateContext(props.uuid);
  const [state] = useDispatch(context);
  const ref = useRef(null);

  return (
    <FilterWrapper
      storeField="fleet"
      componentProps={props}
      fieldProps={useMemo(() => {
        return {
          key: 'car_team_id',
          columnId: 100000014,
          dataType: 0,
        };
      }, [])}
      handleFieldLabel={(val) => {
        return val.length ? ref.current?.getSelectedLabelName?.() : [];
      }}
      filterIdBK="car_team_id"
    >
      <BLMFleetSelect
        ref={ref}
        multiple
        tenantId={state.tenant}
        adCodeList={state.city}
        request={config.get('setting.request')}
      />
    </FilterWrapper>
  );
};
