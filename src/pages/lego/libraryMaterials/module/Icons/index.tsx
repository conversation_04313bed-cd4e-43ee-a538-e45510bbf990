// 存储各个组件的图标，不存在schema里，留在编辑页实时渲染

import { ReactComponent as Bar<PERSON><PERSON> } from '../../BarChart/icon.svg';
import { ReactComponent as BarLineChart } from '../../BarLineChart/icon.svg';
import { ReactComponent as Card } from '../../Card/icon.svg';
import { ReactComponent as DateFilterGlobal } from '../../DateFilterGlobal/icon.svg'
import { ReactComponent as DatePickerFilter } from '../../DatePickerFilter/icon.svg'
import { ReactComponent as FunnelChart } from '../../FunnelChart/icon.svg'
import { ReactComponent as IndexCard } from '../../IndexCard/icon.svg'
import { ReactComponent as LineChart } from '../../LineChart/icon.svg'
import { ReactComponent as ListFilter } from '../../ListFilter/icon.svg'
import { ReactComponent as PieChart } from '../../PieChart/icon.svg'
import { ReactComponent as Rank } from '../../Rank/icon.svg'
import { ReactComponent as Table } from '../../Table/icon.svg'
import { ReactComponent as TableSheet } from '../../TableSheet/icon.svg'

export default {
  BarChart, BarLineChart, Card,
  DateFilterGlobal, DatePickerFilter,
  FunnelChart, IndexCard, LineChart,
  ListFilter, PieChart, Rank, Table, TableSheet
};
