/**
 * 列表组件
 */
import { useCallback, useEffect, useRef, useState } from 'react';
import { MultipleSelect } from '@blmcp/web-ui';
import { reportStore } from '@blm/bi-lego-sdk/dist/es/utils';
import { globalCache } from '@/pages/lego/utils/cache';
import relationCenterExp from '@/pages/lego/libraryMaterials/module/RelationCenter';
import useComponent from '../../hooks/useComponent';
import { DataSetConfig } from '../../type';
import './index.less';
import { getFilters } from '../../api';
import isMobile from '../../utils/isMobile';
import FilterWrapper from '../wrapper/FilterWrapper';

interface ListFilterProps {
  dataSetConfig?: DataSetConfig;
  __id?: string; // 预览模式
  componentId?: string;
  __designMode?: string; // 编辑模式
  disabled?: boolean; // 禁用
  defaultValue?: string[];
  uuid: string;
  options: any;
  reportId?: any;
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      list: [
        {
          ignoreSetDefaultComputeType: true,
          label: '维度',
          key: 'dimensionInfo',
          onlyOne: true,
          placeholder: '仅支持拖入 1 个字段',
        },
      ],
      dateDisabled: true, // 不能选日期
      indexDisabled: true,
    },
  },
  defaultValue: {
    componentName: 'ListFilterDefaultValueSetter',
  },
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {
      noPadding: true,
    },
  },
  // 别名
  title: {
    componentName: 'AliasSetter',
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  dataType: 1,
};
// 逻辑层
export const ListFilter = (props: ListFilterProps) => {
  const {
    dataSetConfig,
    __id,
    componentId,
    __designMode,
    disabled,
    defaultValue,
    uuid,
    reportId,
    options: _options,
  } = props;
  const { dimensionInfo } = dataSetConfig ?? {};
  const filterId = __id ?? componentId ?? '';
  const relationCenter = relationCenterExp(uuid);
  const editable =
    __designMode === 'design' || reportStore.get(uuid).publishStatus === 0;
  // defaultValue 为组件筛选传入的，优先级更高
  const defaultVal =
    (defaultValue?.length && defaultValue) ||
    dimensionInfo?.[0]?.defaultVal?.stringVal ||
    [];

  const [meta, setMeta] = useComponent(filterId);
  const [options, setOption] = useState<{ label: string; value: string }[]>([]);
  const getData = useCallback(async () => {
    const params = {
      // 报表ID 必填
      reportId,
      elementId: meta.elementId, // 组件服务ID 必填
      componentType: 4, // 必填 组件类型枚举 1:indicator//指标卡,2:numberTable//表格,3:chart//图表,4:filter//筛选器5:text//文本
      requestDataType: 1,
      publishStatus: editable ? 0 : 1,
      filterInfo: [],
    };
    // 缓存 key
    const cacheKey = [filterId, meta.elementId, JSON.stringify(params || {})];

    const res = await getFilters(params);
    setMeta({ queryState: true }, true);

    // const options: { label: string; value: string }[] = [];
    const optionsMap = new Map<string, { label: string; value: string }>();
    ((res.data?.values as []) || []).forEach((item) => {
      const data: any = Object.values(item);
      optionsMap.set(data[0], {
        label: data[0],
        value: data[0],
      });
    });
    const options = Array.from(optionsMap.values());

    // 缓存
    globalCache.add(cacheKey, options);

    setOption(options);

    // setValue?.(defaultVal);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editable, meta.elementId]);

  const handleFilterData = useCallback(
    async (fieldValue: string) => {
      const params = {
        // 报表ID 必填
        reportId,
        elementId: meta.elementId, // 组件服务ID 必填
        componentType: 4, // 必填 组件类型枚举 1:indicator//指标卡,2:numberTable//表格,3:chart//图表,4:filter//筛选器5:text//文本
        requestDataType: 1,
        publishStatus: editable ? 0 : 1,
        filterInfo: [
          {
            columnId: dimensionInfo?.[0]?.columnId,
            key: dimensionInfo?.[0]?.key,
            dataType: dimensionInfo?.[0]?.dataType,
            symbol: dimensionInfo?.[0]?.dataType === 1 ? 'IN' : 'LIKE',
            fieldValue: [fieldValue],
          },
        ],
      };

      return getFilters(params)
        .then((res) => {
          const optionsMap = new Map<
            string,
            { label: string; value: string }
          >();
          ((res.data?.values as []) || []).forEach((item: any) => {
            const data: any = Object.values(item);
            optionsMap.set(data[0], {
              label: data[0],
              value: data[0],
            });
          });
          return Array.from(optionsMap.values());
        })
        .catch((e) => {
          if (e === '请勿重复请求') {
            return Promise.reject({
              loading: true,
            });
          } else {
            return [];
          }
        });
    },
    [editable, meta.elementId, dimensionInfo],
  );

  useEffect(() => {
    setMeta(
      {
        query(...arg) {
          // 当改变维度时触发, 设置默认值
          getData(...arg);
          relationCenter.notify('all');
        },
      },
      true,
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [getData, dimensionInfo /** 别配置 setMeta */]);

  // 初始化判断是否可查询，设计模式下 拖拽会通过 meta.query 进行查询
  useEffect(() => {
    if (dimensionInfo?.[0] && meta.elementId && !_options) {
      getData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (dimensionInfo?.[0] && meta.elementId !== undefined) {
    return (
      <FilterWrapper
        componentProps={props}
        label={true}
        defaultValue={defaultVal}
        filterKey="dimensionInfo"
        onFieldIdChange={() => {
          setMeta({ componentTempProps: { selects: [] } }, true);
        }}
      >
        {({ value, onChange, fieldItem }) => {
          return (
            <MultipleSelect
              // key={defaultVal?.toString()}
              value={value}
              placeholder={`${props?.title || dimensionInfo?.[0].title} ${
                dimensionInfo?.[0]?.dataType === 1 &&
                options?.length >= 1000 &&
                !isMobile()
                  ? '（精确搜索）'
                  : ''
              }`}
              onChange={onChange}
              options={_options || options}
              selectAllText={['全选']}
              disabled={disabled}
              showSearch={{
                filter: (val: string, option: any) => {
                  const labelName = option.label;
                  return String(labelName)?.includes(val);
                },
                handleRemote: options?.length >= 1000 && handleFilterData,
              }}
              defaultValue={value}
            />
          );
        }}
      </FilterWrapper>
    );
  } else {
    if (isMobile()) {
      return <div className="lego-filter-wrap mobile">请选择列表维度</div>;
    }
    return <div className="lego-filter-wrap">请选择列表维度</div>;
  }
};

ListFilter.displayName = '列表筛选器';
