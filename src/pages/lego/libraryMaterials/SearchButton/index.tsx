import { SearchButton as SearchButtonView } from '../../components/index';

interface SearchButtonProps {
  title?: string;
  uuid: string;
}

// 关联组件参数与 setter 组件
export const LinkSetterComponent = {
  // 是否隐藏
  hidden: {
    componentName: 'HiddenSetter',
    // 该组件其他配置项
    props: {},
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 4,
  // 不参与获取数据集相关事情
  notQueryData: true,
};

// 逻辑层
export const SearchButton = (props: SearchButtonProps) => {
  return <SearchButtonView {...props} />;
};
SearchButton.displayName = '搜索按钮';
