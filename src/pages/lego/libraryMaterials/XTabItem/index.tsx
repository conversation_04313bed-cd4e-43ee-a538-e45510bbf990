export const ComponentRule = {
  isContainer: true,
  nestingRule: {
    parentWhitelist: ['Tab'],
  },
};
export const LinkSetterComponent = {};

export const ComponentBehavior = {
  // 组件icon
  // icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本 6选项卡
  componentType: 6,
};

export const MergedComponentMate = {
  snippets: [
    {
      title: 'XTabItem',
      screenshot: '',
      schema: {
        componentName: 'XTabItem',
        props: {},
        selected: 'parent',
      },
    },
  ],
};

interface XTabItemProps {
  children: any;
  key: string;
  label: string;
}

export const XTabItem = function (props: XTabItemProps) {
  return (
    <div
      className={`xtab-item-inner ${props.children?.length > 0 ? '' : 'empty'}`}
    >
      {props.children}
    </div>
  );
};

XTabItem.displayName = '选项卡';
