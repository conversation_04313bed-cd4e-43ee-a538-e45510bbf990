import { IndexCard as IndexCardView } from '@/pages/lego/components';
import ComponentWrapper from '../../components/module/ComponentWrapper';
import { ComponentProps } from '../../type';
import isMobile from '../../utils/isMobile';

export const LinkSetterComponent = {
  dataSetConfig: {
    // 用到的组件
    componentName: 'AnalysisConfig',
    // 该组件其他配置项
    props: {
      dimDisabled: true,
      list: [
        {
          label: '指标',
          key: 'measureInfo',
          onlyOne: true,
          placeholder: '仅支持拖入 1 个字段',
          limit: [1, 1],
          numberFormatConfig: true,
          // 指标聚合方式
          combineModeIndex: {
            aggregate: [
              {
                key: 6,
                label: '求和',
              },
              {
                key: 1,
                label: '计数',
              },
              {
                key: 5,
                label: '平均值',
              },
              {
                key: 2,
                label: '去重计数',
              },
              {
                key: 3,
                label: '最大值',
              },
              {
                key: 4,
                label: '最小值',
              },
            ],
          },
          // 是否配置字段名称
          fieldAliasConfig: true,
        },
      ],
    },
  },
};

// 定义组件的行为
export const ComponentBehavior = {
  // 组件icon
  icon: './icon/i.png',
  // 组件分类 1:指标卡 2:表格 3:图表 4:筛选器 5:文本
  componentType: 1,
  // 该组件用到的数据类型
  dataType: 3,
  // 组件列表次序（非必填）
  sortIndex: 1,
};

export const IndexCard = (props: ComponentProps<any>) => {
  return ComponentWrapper(IndexCardView, {
    // 增加数据处理层
    // handleData: () => { },
    titleConfig: false,
    // 如果是移动版，则不设置高度
    defaultHeight: 'auto',
  })(props);
};

IndexCard.displayName = '指标卡';
