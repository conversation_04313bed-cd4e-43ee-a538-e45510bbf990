// const req = require.context('./', true, /\/setter\/[^]*?Setter.tsx$/);
// console.log('require.context',require.context('@/components', true,/\.tsx$/).keys())
// const registerSetter = window?.AliLowCodeEngine?.setters?.registerSetter;
// console.log('req.keys()req.keys()',req.keys(),registerSetter)
// if(registerSetter){
//   req.keys().forEach((key)=>{
//     const comp = req(key).default
//     console.log(comp.displayName,'omp.displayName')
//     registerSetter(comp.displayName, comp);
//   })
// }
// import { setters } from '@alilc/lowcode-engine';
const { setters } = window.AliLowCodeEngine || {};

import AltStringSetter from './LineChart/setter/AltStringSetter';
import LinkComponentSetter from './setter/LinkComponentSetter';
import DatePickerDefaultValue from './DatePickerFilter/setter/DatePickerDefaultValue';
import DatePickerDisabled from './DatePickerFilter/setter/DatePickerDisabled';
import {
  AnalysisConfig,
  DescriptionConfig,
  OperationColumnConfig,
} from './setter/index';

import TabSetter from './XTab/setter';
import ListFilterDefaultValueSetter from './ListFilter/setter/ListFilterDefaultValueSetter';

setters.registerSetter('AltStringSetter', AltStringSetter);
setters.registerSetter('AnalysisConfig', AnalysisConfig);
setters.registerSetter('DescriptionConfig', DescriptionConfig);
setters.registerSetter('LinkComponentSetter', LinkComponentSetter);
setters.registerSetter('DatePickerDefaultValue', DatePickerDefaultValue);
setters.registerSetter('DatePickerDisabled', DatePickerDisabled);
setters.registerSetter('TabSetter', TabSetter);
setters.registerSetter('OperationColumnConfig', OperationColumnConfig);
setters.registerSetter(
  'ListFilterDefaultValueSetter',
  ListFilterDefaultValueSetter,
);
