import React, { useEffect, useMemo, useLayoutEffect, useState } from 'react';
import queryCenterExp from '@/pages/lego/libraryMaterials/module/Query';
import { DatasetItem } from '@/pages/lego/type';
import linkageCenterExp from '@/pages/lego/libraryMaterials/module/Linkage';
import relationCenterExp from '@/pages/lego/libraryMaterials/module/RelationCenter';
import './index.less';
import { useDispatch } from '@/utils/store';
import useComponent, {
  store as componentStore,
} from '@/pages/lego/hooks/useComponent';
import filterContext, { StateProps } from '../../context/filterContext';
import { useUpdateEffect } from 'ahooks';
import dayjs from 'dayjs';
// import { globalCache } from '@/pages/lego/utils/cache';

interface FieldProps extends DatasetItem {
  elementId?: number;
}

interface Props {
  defaultValue?: any;
  loading_data: boolean;
  loading: boolean;
  filterKey?: string;
  handleInject?: (setting: { fieldItem: FieldProps; value: any }) => void;
  children: React.ReactNode | ((props: any) => React.ReactNode);
  componentProps: any;
  fieldProps?: FieldProps;
  handleFieldLabel?: (value: any[]) => (string | number)[];
  onChange?: (value: any, field: any) => void;
  value?: any;
  filterIdBK?: string;
  filterExtendField?: (field: any, resetType?: boolean) => any;
  emptyTip?: string;
  storeField?: string;
  label?: string | boolean | ((field: any) => string);
  onFieldChange?: (op: { fieldItem: FieldProps }) => void;
  onFieldIdChange?: (op: { fieldItem: FieldProps }) => void;
}

const LabelWrapper = (props: any) => {
  const { label, children } = props;
  const isMobile =
    /phone|pod|iPhone|iPod|ios|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone/i.test(
      navigator.userAgent,
    );
  if (isMobile) {
    return children;
  }
  return (
    <div className={`lego-filter-label-box ${label ? 'isLabel' : ''}`}>
      {label && (
        <div className="lego-filter-label" title={label}>
          {label}
        </div>
      )}
      <div className="lego-filter-label-wrap">{children}</div>
    </div>
  );
};

const handleSearchParams = (searchParams, componentProps, fieldProps) => {
  const value =
    searchParams.get(componentProps.title) ||
    searchParams.get(fieldProps?.key || '');
  let newValue: any = undefined;

  if (value) {
    if (componentProps._componentName === 'RangeOfIntervalsFilter') {
      newValue = value.split(',').map((item) => Number(item) || 0);
    } else if (
      componentProps._componentName === 'DatePickerFilter' ||
      componentProps._componentName === 'DateFilterGlobal'
    ) {
      const v = value.split(',').map((v) => Number(v));
      newValue = [
        dayjs(v[0]).startOf('day').valueOf(),
        dayjs(v[1]).endOf('day').valueOf(),
      ];
    } else {
      newValue = value.split(',');
    }
    return newValue;
  }
};

export default function (props: Props) {
  const {
    children,
    loading_data = false,
    loading,
    filterKey = 'dimensionInfo',
    handleInject,
    componentProps,
    fieldProps,
    handleFieldLabel,
    filterIdBK,
    filterExtendField = () => ({}),
    emptyTip = '请选择字段',
  } = props;

  const { __id, componentId, dataSetConfig, uuid } = componentProps;

  const filterId = __id || componentId || filterIdBK || '';
  const linkageCenter = linkageCenterExp(uuid);
  const queryCenter = queryCenterExp(uuid);
  const relationCenter = relationCenterExp(uuid);
  const [store, stateDispatch] = useDispatch<StateProps>(filterContext(uuid));
  const [componentMeta] = useComponent(filterId);

  // const [value, _setValue] = useState<any>();
  // 当前组件绑定值
  const value = useMemo(() => {
    return store[props.storeField || filterId];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [store]);

  const fieldItem: FieldProps = useMemo(() => {
    if (componentProps.partialContainerFilterId) {
      return {
        // ...(dataSetConfig?.[filterKey]?.[0] || {}),
        elementId: componentMeta?.elementId,
        columnId: dataSetConfig?.[filterKey]?.[0]?.columnId,
      };
    }
    if (fieldProps?.key) {
      return fieldProps;
    }
    return dataSetConfig?.[filterKey]?.[0] || {};
  }, [fieldProps, dataSetConfig, filterKey, componentProps, componentMeta]);

  const [searchParams] = useState(
    () => new URLSearchParams(location.search.slice(1)),
  );

  const defaultValue =
    handleSearchParams(searchParams, componentProps, fieldItem) ||
    props.defaultValue ||
    componentProps.defaultValue;
  const memoizedDefaultValue = useMemo(
    () => defaultValue,
    [JSON.stringify(defaultValue)],
  );
  const memoizedFieldItem = useMemo(
    () => fieldItem,
    [JSON.stringify(fieldItem)],
  );

  const fieldItemColumnId = componentProps.partialContainerFilterId
    ? fieldItem?.elementId
    : fieldItem?.columnId;

  const dispatch = (val: any, resetType = false) => {
    if (fieldItemColumnId === undefined) return;
    // 乐高quēry 更新，数组形式
    const handleValue = Array.isArray(val) ? val : (val && [val]) || [];
    console.log(handleValue, value, 'datevalue-----44444');
    if (handleValue !== value) {
      // 当前state 更新
      stateDispatch({
        type: props.storeField || filterId,
        payload: handleValue,
      });
      const Field = {
        columnId: fieldItem.columnId,
        elementId: fieldItem.elementId,
        key: fieldItem.key,
        dataType: fieldItem.dataType,
        fieldValue: handleValue,
        fieldLabel: handleFieldLabel?.(handleValue) || handleValue, // 目前 fieldValue\fieldLabel 值都一致
      };
      const newField = {
        ...Field,
        ...filterExtendField(Field, resetType),
      };

      queryCenter.setQuery(filterId, newField);

      props.onChange?.(val, newField);
    }
  };

  // 初始化状态录入
  useLayoutEffect(() => {
    dispatch(defaultValue, true);
    componentStore.attr(filterId, {
      ...componentStore.get(filterId),
      title: componentProps.title,
      filterLinkComponents: componentProps.filterLinkComponents,
      uuid,
    });
    return () => {
      queryCenter.deleteQuery(filterId);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // 重置注册
  useEffect(() => {
    // 当字段信息\defaultValue 变化时, 重新设置 value
    dispatch(defaultValue, true);

    // 当字段删除时，去除之前筛选条件
    if (fieldItemColumnId === undefined) {
      queryCenter.deleteQuery(filterId);
      return;
    }

    const resetFilter = () => {
      // 不进行diff ，直接进行修改
      dispatch(defaultValue, true);
    };
    const resetKey = `reset${componentProps.partialContainerFilterId || ''}`;
    linkageCenter.subscribe(resetKey, resetFilter);

    return () => {
      linkageCenter.unsubscribe(resetKey, resetFilter);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [memoizedFieldItem, memoizedDefaultValue]);

  useEffect(() => {
    props.onFieldChange?.({ fieldItem });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [memoizedFieldItem]);

  useEffect(() => {
    props.onFieldIdChange?.({ fieldItem });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fieldItem.columnId]);

  // 检测外部 loading_data 状态 来判断当前筛选器是否可用
  useLayoutEffect(() => {
    if (fieldItem && loading_data) {
      if (loading) {
        relationCenter.registerFilter(filterId);
      } else {
        relationCenter.readyFilter(filterId);
      }
    } else {
      relationCenter.registerFilter(filterId);
      setTimeout(() => {
        relationCenter.readyFilter(filterId);
      }, 10);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [filterId, loading_data, loading]);

  // 监听 fieldItem 变化，清空label
  useUpdateEffect(() => {
    if (window.__setSelectComponentPropsDataById) {
      window.__setSelectComponentPropsDataById(filterId, 'title', '');
    }
  }, [fieldItem?.columnId]);

  // 处理外部传入的 props 注入
  const otherProps = useMemo(() => {
    if (handleInject) {
      return handleInject({ fieldItem, value });
    } else {
      return {};
    }
  }, [fieldItem, handleInject, value]);

  const prefixLabel = useMemo(() => {
    if (props.label === true) {
      return componentProps.title || fieldItem.title;
    }
    return typeof props.label === 'function'
      ? props.label({ fieldItem })
      : props.label || undefined;
  }, [componentProps, fieldItem, props]);

  const childProps = {
    value,
    fieldItem,
    onChange: function (value: any) {
      dispatch(value);
    },
    ...otherProps,
  };

  if (fieldItemColumnId === undefined) {
    // @ts-expect-error
    return <div className="lego-filter-wrap">{emptyTip}</div>;
  }

  if (typeof children === 'function') {
    return (
      <LabelWrapper label={prefixLabel}>{children(childProps)}</LabelWrapper>
    );
  }

  return React.Children.map(children, (child) => {
    if (!React.isValidElement(child)) {
      return child;
    }

    return (
      <LabelWrapper label={prefixLabel}>
        {React.cloneElement(child, childProps)}
      </LabelWrapper>
    );
  });
}
