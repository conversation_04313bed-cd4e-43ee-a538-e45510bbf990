import React, { useMemo, useEffect, useLayoutEffect } from 'react';
import { useDispatch } from '@/utils/store';

interface Props<T> {
  store: string;
  dispatch: string;
  context: any;
  onChange?: (value: T) => void;
  value: T;
  children: React.ReactElement;
}

export default function <T>(props: Props<T>) {
  const { context, children } = props;
  const [store, dispatch] = useDispatch<any>(context);
  // 当前组件绑定值
  const storeValue = useMemo(() => {
    return store[props.store];
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [store]);

  // dispatch 函数
  const dispatchFn = function (value: any, dep = true) {
    // diff store 和 props.value 是否一致
    if (value !== storeValue) {
      dispatch({
        type: props.dispatch || props.store,
        payload: value,
      });
      if (dep) {
        props.onChange?.(value);
      }
    }
  };

  // 检测 value 变化 触发 dispatch
  // useEffect(() => {
  //   dispatchFn(props.value, false);
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [props.value]);

  // 检测 store 变化 触发 onchange
  // useEffect(() => {
  //   if (props.store === 'city') {
  //     console.log('检测 store 变化 触发 onchange', storeValue);
  //   }
  //   if (props.value !== storeValue) {
  //     props.onChange?.(storeValue);
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [storeValue]);

  const childProps = {
    value: storeValue,
    onChange: function (value: any) {
      dispatchFn(value);
    },
  };

  return React.Children.map(children, (child) => {
    if (!React.isValidElement(child)) {
      return child;
    }

    return React.cloneElement(child, childProps);
  });
}
