import React, { useRef, useMemo, useEffect } from 'react';
import useGlobalState from '@/pages/lego/hooks/useGlobalState';

interface Option {
  store: string;
  params?: {
    key: string;
    store: string;
  }[];
  dispatch?: string;
  deep: string;
  initiateStore?: { [key: string]: any };
}

export default function <T, R>(
  Component: any,
  option: Option = {
    deep: 'deep',
    store: 'store',
    dispatch: 'onChange',
  },
): any {
  return function (
    props: T & {
      mobile?: boolean;
      value: string | string[];
      multiple: boolean;
    },
    ref: React.Ref<R>,
  ) {
    const [store, setStore] = useGlobalState<{ [key: string]: any }>(
      option.deep,
      option.initiateStore || {},
    );
    const oldParams = useRef({});

    // 用到的参数
    const storeProps = useMemo(() => {
      const newParams: any = {};
      option.params?.forEach?.((item) => {
        newParams[item.key] = store[item.store];
      });

      if (JSON.stringify(oldParams.current) !== JSON.stringify(newParams)) {
        oldParams.current = newParams;
        return newParams;
      } else {
        return oldParams.current;
      }
    }, [store]);

    // dispatch 函数
    const dispatchFn = function (value: any) {
      if (option.store) {
        // diff store 和 props.value 是否一致
        const diff =
          JSON.stringify(value || {}) !==
          JSON.stringify(store[option.store] || {});
        if (diff) {
          setStore(
            {
              [option.store]: value,
            },
            true,
          );
        }
      }
    };

    const onChange = (value: string[]) => {
      dispatchFn(value);
      // @ts-expect-error ignore
      props?.[option.dispatch]?.(value);
    };

    // 检测 value 变化 触发 dispatch
    useEffect(() => {
      dispatchFn(props.value);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [props.value]);

    // 当前组件绑定值
    const storeValue = useMemo(() => {
      if (option.store) {
        return store[option.store];
      } else {
        return props.value;
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [store, props.value]);

    // 检测 store 变化 触发 onchange
    useEffect(() => {
      if (option.store) {
        // @ts-expect-error ignore
        props?.[option.dispatch]?.(value);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [storeValue]);

    return (
      <Component
        ref={ref}
        {...props}
        {...storeProps}
        onChange={onChange}
        value={storeValue}
      />
    );
  };
}
