import { useEffect, useRef, useState } from 'react';
import Cookies from 'js-cookie';
import cloudByTenantId from '@/pages/lego/utils/cloudByTenantId';
import { isHubble } from '@/utils/hubble';

let configMapCache = null;

export const useCloudsConfig = (...keys: string[]) => {
  const [configMap, setMap] = useState(null);

  useEffect(() => {
    const getData = async () => {
      const config = await cloudByTenantId();
      configMapCache = config;
      setMap(config);
    };
    if (!configMapCache && !isHubble) {
      getData();
    }
  }, [configMap]);

  return keys?.map((key) => {
    if (isHubble) return true;
    const { whiteList, isAll } = configMapCache?.[key] ?? {};
    return isAll || whiteList?.includes(Number(Cookies.get('_tenant_id')));
  });
};
