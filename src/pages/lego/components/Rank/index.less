.lego-rank-wrap {
  display: block;

  .BLM_Antd_content .ant-table .ant-table-thead>tr>th {
    border-top: 0.5px solid rgba(37, 52, 79, 0.08) !important;
  }

  .ant-table {
    .ant-table-tbody {
      .ant-table-measure-row {
        td {
          padding: 0 !important;
        }
      }

      .ant-table-row {
        td {
          height: 38px;
          padding: 4px 10px !important;

          .dimension-value-wrapper {
            .dimension-value {
              overflow: hidden;
              white-space: nowrap;
              text-overflow: ellipsis;
            }

            .ant-progress-inner {
              top: -1px;
            }
          }

          .ant-progress-line {
            margin-bottom: 0;
          }
        }
      }
    }
  }

  // 控制组件在正确位置
  .react-resizable {
    position: relative;
    background-clip: padding-box;
  }

  .react-resizable-handle {
    position: absolute;
    right: -5px;
    bottom: 0;
    z-index: 1;
    width: 10px;
    height: 100%;
    cursor: col-resize;
  }
}

.lego-rank-wrap .ant-table-column-sorters {
  width: 100% !important;
}

// 滚动条默认隐藏，滚动显示
.lego-rank-scroll-hide .ant-table-body::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.lego-rank-scroll-hide .ant-table-body::-webkit-scrollbar-thumb {
  background-color: transparent;
}

.lego-rank-scroll-hide:hover .ant-table-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, .3);
}
