import { Table as BLMTable, ColumnType, ColumnsType } from '@blmcp/ui';
import { ResizeCallbackData } from 'react-resizable';
import { DataSourceItemType } from 'antd/es/auto-complete';
import { useImmer } from 'use-immer';
import { useEffect } from 'react';
import queryCenter from '@/pages/lego/libraryMaterials/module/Query';
import SortDownIcon from '@/assets/lego/sort-down.svg';
import SortUpIcon from '@/assets/lego/sort-up.svg';
import SortNoIcon from '@/assets/lego/sort-no.svg';
import isMobile from '../../utils/isMobile';
import { BaseChart, SortType } from '../../components/types';
import { ResizableTitle } from './ResizableTitle';
import './index.less';

interface RankData {
  columns: ColumnType<unknown>[];
  dataSource: Record<string, unknown>;
  pageNum: number;
  pageSize: number;
  total: number;
}

interface RankProps extends Partial<BaseChart> {
  dataSource: RankData;
  query: (params: { paging: { pageNum: number; pageSize: number } }) => void;
}

export const Rank = ({
  height = 380,
  width,
  dataSource,
  query,
  chartId,
}: RankProps) => {
  const { columns, dataSource: dataS } = dataSource;
  const [finalColumns, setColumns] =
    useImmer<ColumnsType<DataSourceItemType>>(columns);

  useEffect(() => {
    setColumns(columns);
  }, [columns]);

  const tableChange = (
    page: { pageSize: number; current: number },
    filters: any,
    sorter: any,
  ) => {
    const sort = [];
    if (sorter.order) {
      sort.push({
        // 排序
        index: sorter.column.index + 1, // 排序列
        sortOrder: sorter.order === 'ascend' ? SortType.ASC : SortType.DESC,
        key: sorter.field,
      });
    }
    const filterInfo = queryCenter.getQuery(chartId);
    const paging = {
      // 分页配置
      pageSize: 50,
      pageNum: 1,
    };
    query(undefined, { ...filterInfo, paging, sort });
  };

  const iconStyle = {
    display: 'flex',
    alignItems: 'center',
    height: '100%',
    marginLeft: '2px',
  };

  const getSortIcon = ({ sortOrder }: { sortOrder: string | null }) => {
    if (sortOrder === 'ascend') {
      return (
        <div style={iconStyle}>
          <SortUpIcon />
        </div>
      );
    } else if (sortOrder === 'descend') {
      return (
        <div style={iconStyle}>
          <SortDownIcon />
        </div>
      );
    } else {
      return (
        <div style={iconStyle}>
          <SortNoIcon />
        </div>
      );
    }
  };

  // 动态修改宽度
  const handleResize =
    (index: number) =>
    (_: React.SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
      setColumns((prevColumns) => {
        prevColumns[index].width = size.width;
      });
    };
  // 设置侦听函数
  const mergeColumns: ColumnsType<DataSourceItemType> = finalColumns.map(
    (col, index) => ({
      ...col,
      onHeaderCell: (column) => ({
        width: (column as ColumnType<DataSourceItemType>).width,
        onResize: handleResize(index),
      }),
      sorter: false,
      sortIcon: getSortIcon,
    }),
  );
  return (
    <div
      className="lego-rank-wrap lego-rank-scroll-hide"
      style={{ maxWidth: width }}
    >
      <BLMTable
        columns={mergeColumns}
        dataSource={dataS}
        onChange={tableChange}
        scroll={{ y: isMobile() ? 240 : height - 42, x: 'max-content' }}
        tableLayout="auto"
        showSorterTooltip={false}
        // 拖拽头部组件
        components={{
          header: {
            cell: ResizableTitle,
          },
        }}
        pagination={false}
      />
    </div>
  );
};
