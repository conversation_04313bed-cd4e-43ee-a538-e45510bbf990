import { useCallback, useEffect, useRef, useState } from 'react';
import { cloneDeep } from 'lodash-es';
import { Funnel } from '@blmcp/charts';
import { Input, Popconfirm } from '@blmcp/ui';
import { ComponentProps } from '@/pages/lego/type';
import isMobile from '../../utils/isMobile';
import { Colors } from './colors';
import './index.less';

export const FunnelChart = ({
  dataSource,
  updateComponentConfig,
  isEdit,
  width,
}: ComponentProps<any>) => {
  const { data, text, showFTP, measureInfo, disabledEdit } = dataSource;
  const chart = useRef<any>();
  const [open, setOpen] = useState(false);
  const [labelText, setText] = useState('');
  const [dataIndex, setIndex] = useState(0);
  const funnelRef = useRef({});
  const [{ top, left }, setOffset] = useState({ top: 0, left: 0 });
  const virtualData = cloneDeep(data);
  let halfWidth = (chart?.current?.getWidth?.() ?? width) / 2;
  if (virtualData[virtualData?.length - 1]) {
    virtualData[virtualData?.length - 1].labelLine = { show: false };
  }
  virtualData.forEach((data, index) => {
    data.label = {
      width: isMobile() ? halfWidth * 0.8 : halfWidth - 150 + 30 * index,
      overflow: 'truncate',
    };
  });
  useEffect(() => {
    if (funnelRef.current) {
      funnelRef.current.disabledEdit = disabledEdit;
      funnelRef.current.isEdit = isEdit;
    }
  }, [disabledEdit, isEdit]);

  const baseURL = window?.publicPath ?? window.parent.publicPath;
  const imgURL =
    process.env.NODE_ENV === 'development'
      ? '/static/blm-edit-o.png'
      : baseURL + 'static/blm-edit-o.png';

  const option = {
    color: Colors,
    title: {
      text: '',
      subtext: '',
      left: 'center',
    },
    tooltip: {
      trigger: 'item',
      borderColor: '#fff',
      confine: true,
      formatter: function (param) {
        const seriesName = param.seriesName;
        const matchColor = 0;
        const color =
          (matchColor && matchColor[1]) ||
          Colors[param.dataIndex % Colors.length];

        return `<div style="z-index:200">${seriesName}<br /> <span style="display:inline-block;width: 8px;height: 8px;border-radius: 2px;background-color: ${color}"></span> <span style="margin-right:20px;">${param.name}</span>${param?.data?.valueText}</div>`;
      },
    },
    legend: {
      orient: 'horizontal',
      left: 'left',
      top: 'top',
      type: 'scroll',
      itemHeight: 8,
      itemWidth: 8,
      selectedMode: false,
    },
    series: [
      showFTP
        ? {
            name: '',
            type: 'funnel',
            data: virtualData,
            left: isMobile() ? 'right' : 'center',
            top: 30,
            bottom: 0,
            width: isMobile() ? halfWidth * 1.3 + 'px' : '380px',
            sort: 'none',
            minSize: '0%',
            maxSize: isMobile() ? '100%' : '80%',
            label: {
              color: 'black',
              position: 'leftBottom',
              show: true,
              formatter: function (param) {
                if (param.dataIndex === data.length - 1) {
                  return '';
                }
                if (isEdit && !disabledEdit) {
                  return `{term|${param?.data
                    ?.nameFTP}}{fragment1|${' '}} ${param?.data?.valueFTP}`;
                } else {
                  return `{term|${param?.data?.nameFTP}} ${param?.data?.valueFTP}`;
                }
              },
              rich: {
                term: {
                  fontSize: 12,
                  color: 'rgb(0,0,0)',
                },
                fragment1: {
                  width: 8,
                  height: 8,
                  padding: [0, 4, 0, 2],
                  backgroundColor: {
                    image: imgURL,
                  },
                },
              },
              labelLine: {
                show: true,
              },
            },
            tooltip: {
              show: false,
            },
          }
        : null,
      {
        z: 100,
        zlevel: 100,
        name: text,
        type: 'funnel',
        sort: 'none',
        data: data,
        left: isMobile() ? 'right' : 'center',
        top: 30,
        bottom: 0,
        width: isMobile() ? halfWidth * 1.3 + 'px' : '380px',
        minSize: '0%',
        maxSize: isMobile() ? '100%' : '80%',

        label: {
          color: 'white',
          position: 'inside',
          overflow: 'truncate',
          show: true,
          formatter: function (param) {
            return `${param.name} ${param?.data?.valueText}`;
          },
        },
        itemStyle: {
          borderRadius: 2,
          borderColor: '#fff',
          borderWidth: 0,
        },
      },
    ],
  };

  const confirm = () => {
    const newMeasureInfo = [...measureInfo];
    newMeasureInfo[dataIndex].feConfig = JSON.stringify({
      funnelFTPLabel: labelText,
    });

    setOpen(false);
    updateComponentConfig('dataSetConfig.measureInfo', newMeasureInfo);
  };

  const cancel = () => {
    setOpen(false);
  };

  const handleOpenChange = (newOpen: boolean) => {
    if (!newOpen) {
      setOpen(newOpen);
      return;
    }
    setOpen(newOpen);
  };

  const onLabelTextChange = (e) => {
    setText(e.target.value);
  };

  const showEditModal = useCallback((params) => {
    const node = params.event.event.srcElement;
    const isTextLabel =
      node &&
      node.nodeName === 'text' &&
      node?.getAttribute('text-anchor') === 'end';
    const isImageEdit =
      node &&
      node.nodeName === 'image' &&
      node?.getAttribute('href')?.includes('static/blm-edit-o.png');
    if (!(funnelRef.current.isEdit && !funnelRef.current.disabledEdit)) return;
    if (isTextLabel || isImageEdit) {
      setOffset({
        top: params.event.offsetY + 40,
        left: params.event.offsetX,
      });
      setOpen(true);
      setText(params.data.nameFTP);
      setIndex(params.dataIndex);
    }
  }, []);

  const init = (chartInstance) => {
    chart.current = chartInstance;
    chart?.current?.on('click', (params) => {
      showEditModal(params);
    });
  };

  return (
    <div className="lego-funnel-chart-wrap">
      <Funnel dataSource={{}} option={option} init={init} />

      <Popconfirm
        overlayInnerStyle={{ padding: '8px 16px' }}
        title=""
        description={
          <div>
            <Input
              value={labelText}
              data-edit
              onChange={onLabelTextChange}
              showCount
              maxLength={20}
            />
          </div>
        }
        open={open}
        icon={null}
        onOpenChange={handleOpenChange}
        onConfirm={confirm}
        onCancel={cancel}
        okText="保存"
        cancelText="取消"
      >
        <div style={{ top, left }} className="lego-funnel-label-panel"></div>
      </Popconfirm>
    </div>
  );
};
