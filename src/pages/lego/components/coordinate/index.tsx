import { useEffect, useRef } from 'react';
import {
  ComponentProps,
  IndexData,
  IndexDataKeyValue,
  DatasetItem,
} from '@/pages/lego/type';
import {
  formatValue,
  getText,
} from '@/pages/lego/libraryMaterials/module/utils';
import echarts from '../echarts';
import { Colors } from '../../components/constant';
import './style.less';

interface Option {
  measureCallback: (dl: DatasetItem, index: number) => any;
}

function getTextWidth(text: string, font: string) {
  const canvas = document.createElement('canvas');
  const context: any = canvas.getContext('2d');
  context.font = font;
  const metrics = context.measureText(text);
  return metrics.width;
}

const chartStyle: any = function (option: any) {
  const { index, color } = option;
  return {
    line: {
      legend: {
        icon: 'path://m0.010277,5.945418l24.979446,0l0,2.109164l-24.979446,0l0,-2.109164z',
        itemStyle: {
          borderColor: Colors[index % Colors.length],
          borderWidth: 1,
        },
      },
      series: {
        smooth: true,
        showSymbol: false,
      },
      marker: `<span style="display:inline-block;width: 8px;height: 2px;vertical-align: middle;background-color: ${color}"></span>`,
    },
    bar: {
      legend: {
        icon: 'path://M123 99h800v800H123z',
      },
      series: {
        smooth: true,
        itemStyle: {
          borderRadius: [2, 2, 0, 0],
        },
        // barMinWidth: 20,
        barMaxWidth: 40,
      },
      marker: `<span style="display:inline-block;width: 8px;height: 8px;border-radius: 2px;background-color: ${color}"></span>`,
    },
  };
};

const Aggregate: any = {
  6: '求和',
  1: '计数',
  5: '平均值',
  2: '去重计数',
  3: '最大值',
  4: '最小值',
};

// const formatValue = function (value: number, type: number) {
//   if (typeof value !== 'number') return value;
//   if (type === 30) {
//     return Number((value * 100).toFixed(2)) + '%';
//   } else {
//     return value;
//   }
// };

export default (Option: Option) =>
  ({ dataSource, useResize }: ComponentProps<IndexData<IndexDataKeyValue>>) => {
    const ref = useRef<HTMLDivElement>(null);
    const chart = useRef<any>();
    const mainAxis = useRef<any[]>([]);
    const secondaryAxis = useRef<any[]>([]);
    const xAxisData = useRef<any[]>([]);
    const style = {
      width: `100%`,
      height: `100%`,
    };

    const layoutFn = function () {
      try {
        const option = chart.current.getOption();
        const xAxis = chart.current.getModel().getComponent('xAxis').axis;
        const barWidth = xAxis.grid._rect.width / xAxisData.current.length; //chart.current._chartsViews[0]._data._layout.bandWidth;
        const barLeft = xAxis.grid._rect.x; //chart.current._chartsViews[0]._data._itemLayouts[0].x;
        const barRight =
          chart.current.getWidth() - barLeft - xAxis.grid._rect.width;

        if (secondaryAxis.current.length) {
          const yAxis1Rang = chart.current.getModel().getComponent('yAxis').axis
            .scale._extent;
          const yAxis2Rang = chart.current.getModel().getComponent('yAxis', 1)
            .axis.scale._extent;

          option.yAxis[0].interval =
            (Math.abs(yAxis1Rang[0]) + Math.abs(yAxis1Rang[1])) / 5;
          option.yAxis[1].interval =
            (Math.abs(yAxis2Rang[0]) + Math.abs(yAxis2Rang[1])) / 5;
        }

        option.xAxis[0].axisLabel.formatter = function (
          value: string,
          index: number,
        ) {
          const isLeft = index === 0;
          const isRight = index === xAxisData.current.length - 1;
          const islastTwo = index === xAxisData.current.length - 2;

          if (isLeft || isRight || islastTwo) {
            let textWidth = getTextWidth(value, '12px PingFang SC');
            const offset = isLeft
              ? barLeft
              : islastTwo
              ? barRight + barWidth * 2
              : barRight;

            if (textWidth <= barWidth + offset) {
              return value;
            }
            let i = 0;

            while (textWidth > barWidth + offset && i < value.length - 1) {
              i += 1;
              textWidth = getTextWidth(value.slice(0, -i), '12px PingFang SC');
            }

            return value.slice(0, -i) + (i === 0 ? '' : '...');
          }
          return value;
        };
        chart.current.setOption(option);
      } catch (e) {
        console.log('optionoption', e);
      }
    };

    useEffect(() => {
      return useResize(function () {
        chart?.current?.resize?.();
        layoutFn();
      });
    }, [useResize]);

    useEffect(() => {
      if (!dataSource.dimensionInfo || !dataSource.measureInfo) {
        return () => {};
      }
      const seriesValues: any[] = [];
      // 维度只有一个
      const wdKey = dataSource.dimensionInfo[0].key;
      const wdKey2 = dataSource.dimensionInfo[0].key + '_D' + 0;
      xAxisData.current = dataSource.values.map((v) => v[wdKey] ?? v[wdKey2]);
      // xAxisData.current[0] += '1234556678990';
      const legend: any[] = [];

      // 主轴
      // const mainAxis: any[] = [];
      // 次轴
      // const secondaryAxis: any[] = [];

      // 坐标列
      dataSource.measureInfo.forEach((dl, index) => {
        const key = dl.key;
        const key2 = dl.key + '_M' + index;
        const { type, yAxisIndex } = Option.measureCallback(dl, index);
        const title = getText(dl.title, dl);

        const serie = {
          name: title,
          data: dataSource.values.map((v) => {
            const vv = v[key] ?? v[key2];
            return {
              value: typeof vv === 'string' ? '-' : vv,
              _value: vv,
            };
          }),
          type: type,
          yAxisIndex,
          ...chartStyle({ index })[type].series,
        };
        if (type === 'line' && dataSource.values.length === 1) {
          serie.showSymbol = true;
          serie.label = {
            show: true,
            formatter(item: any) {
              return formatValue(item.data._value, dl);
            },
          };
        }
        legend.push({
          name: title,
          ...chartStyle({ index })[type].legend,
        });
        seriesValues.push(serie);

        // 区分主次
        const config = JSON.parse(dl.feConfig || '{}');
        if (config.source === 'secondary') {
          secondaryAxis.current.push(dl);
        } else {
          mainAxis.current.push(dl);
        }
      });

      // y轴配置
      const yAxis: any = [
        {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: 'rgba(0,0,0,0.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(37, 52, 79, 0.12)',
              type: [3, 3],
            },
          },
          axisLabel: {
            formatter(value: number) {
              if (
                mainAxis.current.every(
                  (e) =>
                    e.advanceComputeModeId === 30 ||
                    e.valueFormat === 2 ||
                    e.numberFormat === 2,
                )
              ) {
                return Number((value * 100).toFixed(2)) + '%';
              }
              return Number(value.toFixed(2));
            },
          },
        },
        {
          type: 'value',
          axisLine: {
            lineStyle: {
              color: 'rgba(0,0,0,0.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(37, 52, 79, 0.12)',
              type: [3, 3],
            },
          },
          axisLabel: {
            formatter(value: number) {
              if (
                secondaryAxis.current.every(
                  (e) =>
                    e.advanceComputeModeId === 30 ||
                    e.valueFormat === 2 ||
                    e.numberFormat === 2,
                )
              ) {
                return Number((value * 100).toFixed(2)) + '%';
              }
              return Number(value.toFixed(2));
            },
          },
        },
      ];

      // 基于准备好的dom，初始化echarts实例
      chart.current = echarts().init(ref.current);
      chart.current.setOption({
        color: Colors,
        textStyle: {
          fontFamily: 'PingFang SC',
        },
        tooltip: {
          trigger: 'axis',
          confine: true,
          axisPointer: {
            type: 'shadow',
            label: {
              backgroundColor: '#6a7985',
            },
          },
          enterable: true,
          extraCssText: 'max-height: 210px; overflow: auto;',
          className: 'lego-bi-scroll-hide lego-bi-chart-tooltip',
          formatter: function (item: any) {
            //先将滚动条致 0
            const tooltipDom = ref.current?.querySelector?.(
              '.lego-bi-chart-tooltip',
            );
            if (tooltipDom) {
              tooltipDom.scrollTop = 0;
            }

            const title = item[0].name;
            let html: string[] = [];
            item.forEach((axis: any, index: number) => {
              const item = dataSource.measureInfo[index];
              // html.push(
              //   `${style[axis.seriesType].marker} ${
              //     axis.seriesName
              //   } ${formatValue(axis.data, item.advanceComputeModeId)}`,
              // );
              const matchColor = axis.marker?.match(
                /background-color:([^]*?);/,
              );
              const color =
                (matchColor && matchColor[1]) || Colors[index % Colors.length];
              html.push(
                `<tr><td>${
                  chartStyle({ index, color: color })[axis.seriesType].marker
                } ${
                  axis.seriesName
                }</td><td style="text-align: right;font-weight: 500;">${formatValue(
                  axis.data._value,
                  item,
                )}</td></tr>`,
              );
            });
            // return `<span style="font-weight: 500;">${title}</span><div style="height:10px"></div>${html.join(
            //   '<div style="height:10px"/></div>',
            // )}`;
            const table = `<table>
            <tr colspan="2"><td style="font-weight: 500;">${title}</td></tr>
            ${html.join('')}
          </table>`;
            return table;
          },
        },
        grid: {
          containLabel: true,
          top: 32,
          left: 5,
          right: 0,
          bottom: 0,
        },
        legend: {
          type: 'scroll',
          left: 0,
          top: 0,
          data: legend,
          itemWidth: 8,
          itemHeight: 8,
          textStyle: {
            color: 'rgba(0,0,0,0.6)',
          },
        },
        xAxis: {
          type: 'category',
          data: xAxisData.current,
          axisLine: {
            lineStyle: {
              color: 'rgba(37, 52, 79, 0.12)',
              type: [3, 3],
            },
          },
          axisTick: {
            show: false,
            alignWithLabel: false,
          },
          axisLabel: {
            color: 'rgba(0,0,0,0.3)',
          },
        },
        yAxis,
        series: seriesValues,
      });
      layoutFn();
    }, [dataSource]);
    return <div ref={ref} style={style}></div>;
  };
