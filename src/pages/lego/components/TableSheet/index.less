.lego-tablesheet-wrap {
  display: block;
  &.mobile {
    .ant-pagination-simple {
      align-items: center;
      li {
        vertical-align: middle;
        * {
          vertical-align: middle;
          position: relative;
          transform: translate3d(0, 0, 0);
        }
      }
    }
  }
  /* stylelint-disable selector-class-pattern */

  .BLM_Antd_content .ant-table .ant-table-thead > tr > th {
    border-top: 0.5px solid rgba(37, 52, 79, 0.08) !important;
    border-left: 0.5px solid rgba(37, 52, 79, 0.08) !important;
  }
  .BLM_Antd_content .ant-pagination li:nth-child(2) {
    margin-left: 70px;
  }
  /* stylelint-enable selector-class-pattern */
  .lego-operation-item {
    margin: 0px 5px;
    cursor: pointer;
    white-space: nowrap;
  }
  .lego-operation-wrapper {
    width: 100%;
    overflow: auto;
    // 滚动条默认隐藏，滚动显示
    &::-webkit-scrollbar {
      width: 2px !important;
      height: 2px !important;
    }

    &::-webkit-scrollbar-thumb {
      background-color: transparent;
    }

    &:hover::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.3);
    }
  }
}

// 滚动条默认隐藏，滚动显示
.lego-bi-scroll-hide .ant-table-body::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

.lego-bi-scroll-hide .ant-table-body::-webkit-scrollbar-thumb {
  background-color: transparent;
}

.lego-bi-scroll-hide:hover .ant-table-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
}
