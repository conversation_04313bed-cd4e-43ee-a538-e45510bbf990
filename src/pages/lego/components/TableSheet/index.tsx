import {
  Table as BLMTable,
  ColumnType,
  ColumnsType,
  Dropdown,
  MenuProps,
  PaginationProps,
} from '@blmcp/ui';
import { useImmer } from 'use-immer';
import { useEffect, useMemo } from 'react';
// import { original } from 'immer';
import { ResizeCallbackData } from 'react-resizable';
import { DataSourceItemType } from 'antd/es/auto-complete';
import { BaseChart, SortType } from '../../components/types';
import queryCenterExp from '../../libraryMaterials/module/Query';
// 需要手工导入@blmcp/ui的样式，否则其会以umi.css的形式挂载在iframe之外
import { ResizableTitle } from '../Table/ResizableTitle';
import isMobile from '../../utils/isMobile';
import { getResizeColumns } from './utils';
import './index.less';
import styles from './index.module.less';

interface TableData {
  columns: ColumnType<unknown>[];
  dataSource: Record<string, unknown>[];
  total: number;
  pageNum: number;
  pageSize: number;
  contrastLength?: number;
}
interface OperationColsItem {
  label: string;
  key: string;
  placeholder: string;
  url: string;
}
interface TableProps extends Partial<BaseChart> {
  dataSource: TableData;
  dataSetConfig: { contrastInfo: any[] };
  operationCols: { freeze: boolean; operationList: OperationColsItem[] };
  query: (params: { pageNum: number; pageSize: number }) => void;
  chartId: string;
  uuid: string;
}

const render = (template: string, obj: Record<string, any>) => {
  const variableRegex = /\[([^[\]]+)\]/g;
  const getVariableValue = (variable: any) => {
    // [ 'name' ]、[ 'age' ]、[ 'job', 'name' ]
    variable = variable.split('.');
    let variableValue = obj;
    // For example, if we want to get the value of job.name, we will go through the following steps
    // Initialization: variableValue = { name: 'fatfish', age: 100, job: { name: "front end development" } }
    // first loop: variableValue = { name: "front end development" }
    // Second loop: variableValue = 'front end development'
    // Third loop: finished, return 'front end development'
    while (variable.length) {
      variableValue = variableValue[variable.shift()];
    }
    return variableValue;
  };
  const renderStr = template.replace(variableRegex, ($0, variable) => {
    return getVariableValue(variable);
  });
  return renderStr;
};

const showTotal: PaginationProps['showTotal'] = (total) => `共 ${total} 条`;

export const TableSheet = ({
  height = 480,
  width = 380,
  dataSource,
  operationCols,
  dataSetConfig,
  query,
  chartId,
  uuid,
}: TableProps) => {
  const {
    columns,
    dataSource: dataS,
    total,
    pageNum,
    pageSize,
    contrastLength = 0,
  } = dataSource;
  const queryCenter = queryCenterExp(uuid);
  const { freeze = true, operationList } = operationCols ?? {};
  const [finalColumns, setColumns] =
    useImmer<ColumnsType<DataSourceItemType>>(columns);

  const haveContrastInfo = dataSetConfig?.contrastInfo?.length > 0;
  const operationColumn = useMemo(
    () => ({
      key: 'operation',
      title: '操作',
      dataIndex: 'operation',
      width: 180,
      fixed: !isMobile() && freeze ? 'right' : false, // 移动端不冻结
      render(text, record, index) {
        const mapObj = {};
        columns.forEach((item) => {
          mapObj[item.title] = record[item.key];
        });
        if (operationList.length <= 3) {
          // 全部依次展示
          return (
            <div className="lego-operation-wrapper">
              {operationList?.map((oper) => {
                return (
                  <a
                    key={oper.url}
                    target="_blank"
                    href={oper.url ? render(oper.url, mapObj) : null}
                    rel="noreferrer"
                  >
                    <span className="lego-operation-item">{oper.label}</span>
                  </a>
                );
              })}
            </div>
          );
        } else {
          // 只展示三个，剩余放在更多里边

          const head = operationList.slice(0, 3);
          const tail = operationList.slice(3);

          const items: MenuProps['items'] = tail?.map((oper) => {
            return {
              key: oper.url + oper.label,
              label: (
                <a
                  key={oper.url}
                  target="_blank"
                  href={oper.url ? render(oper.url, mapObj) : null}
                  rel="noreferrer"
                >
                  <span className="lego-operation-item">{oper.label}</span>
                </a>
              ),
            };
          });
          return (
            <div className="lego-operation-wrapper">
              {head?.map((oper) => {
                return (
                  <a
                    key={oper.url}
                    target="_blank"
                    href={oper.url ? render(oper.url, mapObj) : null}
                    rel="noreferrer"
                  >
                    <span className="lego-operation-item">{oper.label}</span>
                  </a>
                );
              })}
              <span className={styles.more}>
                <Dropdown menu={{ items }}>
                  <a onClick={(e) => e.preventDefault()}>更多</a>
                </Dropdown>
              </span>
            </div>
          );
        }
      },
    }),
    [columns, freeze, operationList],
  );

  useEffect(() => {
    const finalOperationColumn =
      haveContrastInfo || !(operationList?.length > 0) ? [] : [operationColumn];
    setColumns([...columns, ...finalOperationColumn]);
  }, [columns, haveContrastInfo, operationColumn, setColumns]);

  // 动态修改宽度
  const handleResize =
    (indexStr: string) =>
    (_: React.SyntheticEvent<Element>, { size }: ResizeCallbackData) => {
      const indexArr = indexStr.split('-');
      if (indexArr.length === 1) {
        // 说明是单列，不带分组的
        setColumns((prevColumns) => {
          prevColumns[indexArr[0]].width = size.width;
        });
      } else if (indexArr.length === 2) {
        // 设置最后一列宽度
        setColumns((prevColumns) => {
          // 说明是多列需要根据index 路径来修改具体的值
          prevColumns[indexArr[0] as number].children[
            indexArr[1] as number
          ].width = size.width;
        });
      } else if (indexArr.length === 3) {
        setColumns((prevColumns) => {
          // 说明是多列需要根据index 路径来修改具体的值

          prevColumns[indexArr[0] as number].children[
            indexArr[1] as number
          ].children[indexArr[2]].width = size.width;
        });
      } else if (indexArr.length === 4) {
        setColumns((prevColumns) => {
          // 说明是多列需要根据index 路径来修改具体的值

          prevColumns[indexArr[0] as number].children[
            indexArr[1] as number
          ].children[indexArr[2]].children[indexArr[3]].width = size.width;
        });
      }
    };
  // 设置侦听函数
  const mergeColumns: ColumnsType<DataSourceItemType> = getResizeColumns(
    finalColumns,
    handleResize,
  );

  const tableChange = (
    { pageSize, current }: { pageSize: number; current: number },
    filters,
    sorter,
  ) => {
    const sort = [];
    if (sorter.order) {
      sort.push({
        // 排序
        index: sorter.column.index + 1, // 排序列
        sortOrder: sorter.order === 'ascend' ? SortType.ASC : SortType.DESC, // 排序方式，0:asc 升序  1:desc 降序
        key: sorter.field,
      });
    }
    const filterInfo = queryCenter.getQuery(chartId);
    const paging = {
      pageSize, // 分页阈值
      pageNum: current, // 当前页数
    };
    if (isMobile()) {
      if (!paging.pageSize) {
        paging.pageSize = 20;
      }
      if (!paging.pageNum) {
        paging.pageNum = 1;
      }
    }
    queryCenter.setTableQuery(chartId, { paging, sort });
    query(undefined, { ...filterInfo, paging, sort });
  };
  let pagination = {
    simple: isMobile() ? { readOnly: true } : undefined,
    total,
    pageSize,
    current: pageNum,
    pageSizeOptions: [20, 50, 100],
    showTotal: isMobile() ? () => false : showTotal,
    showSizeChanger: !isMobile(),
  };
  if (isMobile() && total <= 20) {
    // 如果在移动端，仅有一页，则不显示分页
    pagination = false;
  }
  return (
    <div
      className={`lego-tablesheet-wrap lego-bi-scroll-hide ${
        isMobile() ? 'mobile' : ''
      }`}
      style={{ maxWidth: width }}
    >
      <BLMTable
        columns={mergeColumns}
        dataSource={dataS}
        onChange={tableChange}
        showSorterTooltip={false}
        // 拖拽头部组件
        components={{
          header: {
            cell: ResizableTitle,
          },
        }}
        scroll={{
          y: isMobile() ? 240 : height - 95 - contrastLength * 40,
          x: 'max-content',
        }}
        tableLayout="auto"
        pagination={pagination}
      />
    </div>
  );
};
