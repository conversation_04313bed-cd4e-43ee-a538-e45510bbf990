// 遍历表头，复制拖拽能力

import { ColumnType, ColumnGroupType } from "@blmcp/ui";
import { DataSourceItemType } from "antd/es/auto-complete";
import { produce } from "immer";

interface HandleResize {
  (indexStr: string): void;
}

export const handleColumns = (columns: ColumnGroupType<DataSourceItemType>[], handleResize: HandleResize, path?: string) => {

  return columns.map((item, index: number) => {
    let subPath = path;
    if (item?.children) {
      subPath = subPath + `-${index}`;
      return produce(item, draftItem => {
        draftItem.children = handleColumns(draftItem.children, handleResize, subPath);
      });
    } else {
      subPath = subPath + `-${index}`;
      return {
        ...item,
        onHeaderCell: column => ({
          width: (column as ColumnType<DataSourceItemType>).width,
          onResize: handleResize(subPath),
        }),
      }
    }
  });
}
export const getResizeColumns = (columns: ColumnGroupType<DataSourceItemType>[], handleResize: HandleResize) => {

  return columns.map((item, index: number) => {

    if (item?.children) {
      let path: string = '';
      // 带分组的列
      path += `${index}`;
      return produce(item, draftItem => {
        draftItem.children = handleColumns(draftItem.children, handleResize, path);
      });
    } else {
      // 不带分组的列.
      return {
        ...item,
        onHeaderCell: column => ({
          width: (column as ColumnType<DataSourceItemType>).width,
          onResize: handleResize(`${index}`)
        }),
      }
    }

  });
}





