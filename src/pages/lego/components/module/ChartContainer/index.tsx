/**
 * 将图表，表格，的暂无数据，Loading 效果进行统一封装
 */

import React, { useEffect, useState, forwardRef, ForwardedRef } from 'react';
import ResizeObserver from 'resize-observer-polyfill';
import useComponent, { store } from '@/pages/lego/hooks/useComponent';
import { Loading } from './Loading';
import './index.less';
import isMobile from '@/pages/lego/utils/isMobile';
import { useUpdateLayoutEffect } from 'ahooks';

interface ChartWrapProps {
  loading?: boolean;
  children: React.ReactNode;
  useResize: (fn: () => void) => void;
  defaultHeight: number;
  containerStyle: object;
  chartId: string;
  compliance: boolean;
  isEdit: boolean;
  setParentVisible: (visible: boolean) => void;
}

function findParent(node: HTMLElement | null) {
  if (!node) return;
  let parent = node.parentNode as any;
  while (parent) {
    if (parent.parentNode.classList.contains('lce-page')) {
      return parent;
    }
    parent = parent.parentNode;
  }
  return null;
}
const ChartContainer = (
  {
    children,
    defaultHeight,
    containerStyle = {},
    chartId,
    compliance,
    isEdit,
    useResize,
    setParentVisible,
  }: ChartWrapProps,
  dom: ForwardedRef<HTMLDivElement>,
) => {
  // const dom = useRef<HTMLElement>(null);
  const [height, _setHeight] = useState<number>(defaultHeight || 260);
  const [width, setWidth] = useState<number>();
  const [meta] = useComponent(chartId);
  const [isDefaultHeight, setIsDefaultHeight] = useState<boolean>(false);
  const heightJudge = (func: any) => {
    if (defaultHeight === 'auto' && !dom?.current?.parentNode?.style?.height) {
      // 如果高度设置为默认自适应，且未拖拽过，不设置height
      setIsDefaultHeight(true);
    } else {
      setIsDefaultHeight(false);
      func();
    }
  };
  const setHeight = function (h: number) {
    // 四舍五入下，解决分辨率与缩放引起的布局重排问题
    _setHeight(Math.round(h));
  };
  useEffect(() => {
    const cellNode = dom.current?.parentNode;
    if (cellNode) {
      // let animation: boolean;
      let isHidden = cellNode.style.overflow === 'hidden';
      cellNode.style.overflow = 'hidden';
      let resizeObserver = new ResizeObserver(function (entries) {
        // 独立出来，设置宽高及时
        entries.forEach((item) => {
          // 没有宽高时不进行resize
          if (!item.contentRect.height || !item.contentRect.width) return;
          setWidth(item.contentRect.width as any);
          heightJudge(() => setHeight(item.contentRect.height as any));
        });
        const list = store.get(chartId)?.resize || [];
        list.forEach((fn) => fn());
        if (!isHidden) {
          cellNode.style.overflow = '';
        }
        // if (!animation) {
        //   setTimeout(() => {
        //     const list = store.get(chartId)?.resize || [];
        //     list.forEach((fn) => fn());
        //     animation = false;
        //   }, 50);
        //   animation = true;
        // }
      });
      resizeObserver.observe(cellNode);
      return function () {
        resizeObserver?.disconnect?.();
      };
    }
    // else {
    //   setWidth(cellNode?.offsetWidth);
    //   heightJudge(() => setHeight(cellNode?.offsetHeight || defaultHeight));
    //   return useResize(() => {
    //     const height = cellNode?.offsetHeight;
    //     const width = cellNode?.offsetWidth;
    //     // 预览态下也是， 如果都是0 则不执行
    //     if (!height || !width) return;
    //     heightJudge(() => setHeight(height));
    //     setWidth(width);
    //   });
    // }
  }, []);

  useUpdateLayoutEffect(() => {
    if (!meta.noRender) {
      // 解决回显宽度超出问题
      const root = findParent(dom.current);
      root?.classList.add('legoALLOverflowHidden');
      setTimeout(() => {
        root?.classList.remove('legoALLOverflowHidden');
      }, 500);
    }
    //  else {
    //   setTimeout(() => {
    //     store.get(chartId).resize?.forEach((fn) => fn());
    //   }, 500);
    // }
    setParentVisible(meta.noRender);
    if (!meta.noRender && dom.current) {
      const root = findParent(dom.current);
      if (root.querySelector('.fd-layout-row-flex')) {
        const rowFunKey = `noRenderSwitch_${root?.getAttribute('componentid')}`;
        window[rowFunKey]?.(true);
        setTimeout(() => {
          window[rowFunKey]?.(false);
        });
      }
    }

    // const root = findParent(dom.current);
    // childForEach(root, (item) => {
    //   if (item.classList.contains('lego-card-wrap')) {
    //     const originHeight = item.parentNode.offsetHeight;
    //     const originWidth = item.parentNode.offsetHeight;
    //     item.parentNode?.classList.add('lego-fdCell-height-auto');
    //     setTimeout(() => {
    //       item.parentNode?.classList.remove('lego-fdCell-height-auto');
    //     }, 100);
    //   }
    // });
  }, [meta.noRender]);

  // useUpdateLayoutEffect(() => {
  //   if (!meta.noRender && dom.current) {
  //     const root = findParent(dom.current);
  //     const showList = [];
  //     childForEach(root, (item) => {
  //       if (item.classList.contains('lego-card-wrap')) {
  //         const id = item.getAttribute('componentid');
  //         if (id && store.get(id)?.noRender !== true) {
  //           store.merge(id, {
  //             noRender: true,
  //           });
  //           showList.push(id);
  //         }
  //       }
  //     });
  //     setTimeout(() => {
  //       showList.forEach((id) => {
  //         store.merge(id, {
  //           noRender: false,
  //         });
  //       });
  //     }, 100);
  //   }
  // }, [meta.noRender]);

  if (meta.noRender) return null;
  return (
    <div
      className={`lego-card-wrap ${
        isDefaultHeight ? 'lego-card-wrap--default-height' : ''
      }`}
      id="__LEGO__New_Guide"
      componentid={chartId}
      ref={dom}
      style={{ ...containerStyle, height: height + 'px', width: width + 'px' }}
    >
      <Loading loading={compliance && meta.loading} />
      {React.Children.map(children, (child) => {
        if (!React.isValidElement(child)) {
          return null;
        }
        // height和width不要随意更改计算方式，内部组件有依赖
        const h =
          height -
          24 -
          (isMobile() ? 28 : child.props?.titleHiddenType ? 12 : 36);
        const childH =
          height -
          24 -
          (isMobile()
            ? 28
            : child.props?.children?.props?.titleHiddenType
            ? 12
            : 36);
        return React.cloneElement(
          child,
          {
            height: h,
            width: width - 24,
          },
          child.props.children &&
            React.cloneElement(child.props.children, {
              height: childH,
              width: width - 24,
            }),
        );
      })}
    </div>
  );
};

export default forwardRef(ChartContainer);
