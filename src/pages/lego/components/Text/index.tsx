import React, { useState } from 'react';
import { message, Input, Tooltip } from '@blmcp/ui';
import useComponent from '@/pages/lego/hooks/useComponent';
import { setSelectComponentPropsDataById } from '@/pages/lego/utils';
import { checkTextSafe } from '@/pages/lego/api';
import TooltipIcon from './image/tooltip.svg';
import './index.less';
import TextComponentMobile from './mobile';
import isMobile from '../../utils/isMobile';
import RichEditor from './RichEditor';
const { TextArea } = Input;
const { event } = window.AliLowCodeEngine || {};
const TextComponentPC = (props: any) => {
  // 敏感字段分类
  const checkResLabelCategories: any = {
    normal: '正常文本',
    spam: '含垃圾信息',
    ad: '广告',
    politics: '政治',
    terrorism: '暴恐',
    abuse: '辱骂',
    porn: '色情',
    flood: '灌水',
    contraband: '违禁',
    meaningless: '无意义',
    customized: '自定义（比如命中自定义关键词）',
  };

  const [tooltipTitle, setTooltipTitle] = useState('');
  const [meta] = useComponent(props.componentId);
  const value = props.text || meta.text;

  // 敏感字段展示（红色）
  const formatContentHtml = (result: any = {}) => {
    const { suggestion } = result;
    // 插入链接的字符转成#[]
    if (suggestion === 'block') {
      const txt = `文本内容涉及敏感，请修改或者删除`;
      setTooltipTitle(txt);
      // message.error(txt)
    }
  };
  return (
    <div className={`lego-text-wrap lego-bi-scroll-hide`}>
      <TextArea
        data-edit
        defaultValue={value}
        placeholder="请输入文本"
        disabled={props.__designMode !== 'design'}
        autoSize={true}
        maxLength={5000}
        count={
          props.__designMode === 'design'
            ? {
                show: true,
                max: 5000,
              }
            : {}
        }
        onBlur={async (e: React.FocusEvent<HTMLDivElement>) => {
          (window.top.proxy || window.top).__lego_checkTextSafe = true;
          const val = e.target.textContent;

          let checkRes = {};
          if (val !== '') {
            checkRes = await checkTextSafe({
              contents: [val],
            });
          }

          if (val !== '' && checkRes.data?.results[0]?.suggestion === 'block') {
            formatContentHtml(checkRes.data.results[0]);
          } else {
            setTooltipTitle('');
            setSelectComponentPropsDataById(props.componentId, 'text', val);
          }
          (window.top.proxy || window.top).__lego_checkTextSafe = false;
        }}
      />
      {props.__designMode === 'design' && tooltipTitle && (
        <div className="errorInfo">文本涉及敏感字段</div>
      )}
    </div>
  );
};

export const TextComponent = isMobile() ? TextComponentMobile : RichEditor;
