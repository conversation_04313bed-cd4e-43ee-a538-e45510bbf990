.lego-rich-edit-wrap {
  border: 1px solid rgba(37, 52, 79, 7.84%);
  width: 100%;
  height: calc(100% - 10px);
  position: relative;
  font-size: 14px;

  .data-w-e-toolbar {
    border-bottom: 1px solid rgba(37, 52, 79, 7.84%) !important;
  }

  .errorInfo {
    position: absolute;
    left: 0;
    bottom: -10px;
    display: flex;
    align-items: center;
    color: red;

    svg {
      color: red;
      margin-left: 2px;
    }
  }

  .w-e-text-container *,
  .w-e-toolbar * {
    // 解决底线被遮盖问题
    line-height: 1.3;
    text-decoration-skip-ink: none;
  }
}

.lego-rich-edit-readonly {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  font-size: 14px;
  color: rgba(0, 0, 0, 90%);
  p:last-child {
    margin-bottom: 0;
  }

  span[data-w-e-type='updateTime'] {
    color: #366cfe;
  }

  word-break: break-all;
  white-space: pre-wrap;

  * {
    // 解决底线被遮盖问题
    line-height: 1.3;
    text-decoration-skip-ink: none;
  }
}

.lego-card-wrap--default-height{
  .lego-rich-edit-readonly{
    overflow: hidden;
  }
}
