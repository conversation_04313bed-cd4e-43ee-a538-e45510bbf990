import { DomEditor, Boot, IModuleConf, IDomEditor } from '@wangeditor/editor';
import renderUpdateTimeConf from './renderUpdateTime';
import updateTimeToHtmlConf from './updateTimeToHtml';
import parseUpdateTimeHtmlConf from './parseUpdateTimeHtml';

export type updateTimeElement = {
  type: 'updateTime';
  id: string;
  children: { text: string }[];
  dataSourceId: string | number;
  dataSourceName: string;
};

function withUpdateDate<T extends IDomEditor>(editor: T) {
  // TS 语法
  // function withAttachment(editor) {                        // JS 语法
  const { isInline, isVoid } = editor;
  const newEditor = editor;

  newEditor.isInline = (elem) => {
    const type = DomEditor.getNodeType(elem);
    if (type === 'updateTime') return true; // 针对 type: attachment ，设置为 inline
    return isInline(elem);
  };

  newEditor.isVoid = (elem) => {
    const type = DomEditor.getNodeType(elem);
    if (type === 'updateTime') return true; // 针对 type: attachment ，设置为 void
    return isVoid(elem);
  };

  return newEditor; // 返回 newEditor ，重要！！！
}

export const register = () => {
  const moduleEditor: Partial<IModuleConf> = {
    editorPlugin: withUpdateDate, // 插件
  };
  Boot.registerModule(moduleEditor);
  const moduleRender: Partial<IModuleConf> = {
    renderElems: [renderUpdateTimeConf /* 其他元素... */], // renderElem
  };
  Boot.registerModule(moduleRender);

  const moduleToHtml: Partial<IModuleConf> = {
    elemsToHtml: [updateTimeToHtmlConf /* 其他元素... */], // elemToHtml
  };
  Boot.registerModule(moduleToHtml);

  const moduleParse: Partial<IModuleConf> = {
    parseElemsHtml: [parseUpdateTimeHtmlConf /* 其他元素... */], // parseElemHtml
  };
  Boot.registerModule(moduleParse);
};
