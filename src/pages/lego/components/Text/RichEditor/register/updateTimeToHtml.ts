/**
 * @description elem to html
 */

import { SlateElement } from '@wangeditor/editor';
import { updateTimeElement } from './updateTimeNode';

// 生成 html 的函数
function updateTimeToHtml(elem: SlateElement, childrenHtml: string): string {
  const { id, dataSourceId, dataSourceName } = elem as updateTimeElement;
  console.log(childrenHtml, 'childrenHtml');
  return `<span data-w-e-type="updateTime" data-w-e-is-void data-w-e-is-inline data-field-dataSourceId="${dataSourceId}" data-field-dataSourceName="${dataSourceName}" style="display: inline-block; margin-left: 3px; margin-right: 3px; cursor: inherit; color: rgb(54, 108, 254);">[${dataSourceName}.数据更新时间]</span>`;
}

// 配置
const updateTimeToHtmlConf = {
  type: 'updateTime', // 节点 type ，重要！！！
  elemToHtml: updateTimeToHtml,
};

export default updateTimeToHtmlConf;
