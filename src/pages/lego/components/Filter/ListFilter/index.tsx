/**
 * 列表筛选器
 *
 */
import { MultipleSelect } from '@blmcp/web-ui';
import {
  useCallback,
  useEffect,
  useImperativeHandle,
  useState,
  forwardRef,
  useRef,
} from 'react';
import queryCenterExp from '@/pages/lego/libraryMaterials/module/Query';
import { DatasetItem } from '@/pages/lego/type';
import './index.less';
import linkageCenterExp from '@/pages/lego/libraryMaterials/module/Linkage';
import isMobile from '../../../utils/isMobile';

interface FilterProps {
  dimensionInfo: DatasetItem;
  filterId: string;
  elementId: string;
  editable: boolean;
  isLinkDataSet: boolean;
  options: { label: string; value: string }[];
  getData: () => void;
  disabled: boolean;
  defaultValue: string[];
  handleFilterData: () => Promise<any[]>;
  uuid: string;
  title: string;
}
const Filter = (
  {
    dimensionInfo: dimension,
    options,
    filterId,
    disabled,
    defaultValue = [],
    handleFilterData,
    uuid,
    title,
  }: FilterProps,
  ref: any,
) => {
  const linkageCenter = linkageCenterExp(uuid);
  const queryCenter = queryCenterExp(uuid);
  const [key, setKey] = useState(() => Date.now());
  const defaultValueRef = useRef<any[]>();

  const setValue = useCallback(
    (val: string[]) => {
      const fieldLabel: string[] = [];

      val?.forEach((v) => {
        const item = options.find((option) => option.value === v);
        if (item) {
          fieldLabel.push(item.label);
        } else {
          // 目前label、 value 都是相同字段， 这里兼容远程搜索选中的和options不对等导致 fieldLabel 缺少问题
          fieldLabel.push(v);
        }
      });
      queryCenter.setQuery(filterId, {
        columnId: dimension?.columnId,
        key: dimension?.key,
        dataType: dimension?.dataType,
        fieldValue: val,
        fieldLabel: (fieldLabel.length && fieldLabel) || defaultValue, // 初始情况下的回填，避免下拉接口返回才能拿到
      });
    },
    [
      dimension?.columnId,
      dimension?.dataType,
      dimension?.key,
      filterId,
      options,
    ],
  );

  useImperativeHandle(ref, () => {
    return {
      setValue: (val: any) => {
        setValue(val);
        setKey(Date.now());
      },
    };
  });

  useEffect(() => {
    if (
      JSON.stringify(defaultValueRef.current) !== JSON.stringify(defaultValue)
    ) {
      setValue(defaultValue);
      defaultValueRef.current = defaultValue;
    }
  }, [filterId, setValue, defaultValue]);

  useEffect(() => {
    return () => {
      queryCenter.deleteQuery(filterId);
    };
  }, []);

  // 订阅重置
  useEffect(() => {
    // 重置操作
    const resetFilter = () => {
      setKey(Date.now());
      queryCenter.setQuery(filterId, {
        columnId: dimension?.columnId,
        key: dimension?.key,
        dataType: 0,
        fieldValue: defaultValue || [],
        fieldLabel: defaultValue || [],
      });
    };
    linkageCenter.subscribe('reset', resetFilter);

    return () => {
      linkageCenter.unsubscribe('reset', resetFilter);
    };
  }, [filterId, dimension?.key, dimension?.columnId, defaultValue]);

  const handleChange = (value: string[]) => {
    setValue(value);
  };

  return (
    <MultipleSelect
      key={key}
      placeholder={`${title || dimension.title} ${
        dimension?.dataType === 1 && options?.length >= 1000 && !isMobile()
          ? '（精确搜索）'
          : ''
      }`}
      onChange={handleChange}
      options={options}
      selectAllText={['全选']}
      disabled={disabled}
      showSearch={{
        filter: (val: string, option: any) => {
          const labelName = option.label;
          return String(labelName)?.includes(val);
        },
        handleRemote: options?.length >= 1000 && handleFilterData,
      }}
      defaultValue={defaultValue}
    />
  );
};

export const ListFilter = forwardRef(Filter);
