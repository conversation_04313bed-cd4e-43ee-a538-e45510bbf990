/**
 * 时间筛选器
 *
 */
import React, { useRef, useState } from 'react';
import dayjs from 'dayjs';
import { DateRangePicker } from '@blmcp/web-ui';
import type { Dayjs } from 'dayjs';
import isMobile from '../../../utils/isMobile';
import {
  getPresets,
  getPanelDateFilterRelativeUnit,
  getOtherPresets,
} from './tools';
import './style.less';

interface DatePickerFilterProps {
  handleChange: (value: number[], dateFilterRelativeUnit?: number) => void;
  defaultValue: {
    type: string;
    value?: any[];
  };
  pickerType?: string;
  disabledType?: boolean;
  maxStep?: number;
  value?: any;
  placeholder?: string;
  renderExtraFooter?: (value: any) => React.ReactNode;
  onOpenChange?: () => void;
}

export const DatePickerFilter = ({
  handleChange,
  defaultValue,
  pickerType,
  disabledType,
  maxStep = 93,
  value,
  placeholder,
  renderExtraFooter,
  onOpenChange,
}: DatePickerFilterProps) => {
  const [key, setKey] = React.useState(Date.now());
  // 没有的话就按昨日设置
  const presetList =
    pickerType === 'picker-filter' ? getOtherPresets() : getPresets();
  const [curDefaultValue, setCurDefaultValue] = React.useState(() => {
    // 如果 defaultValue 直接包含 value，优先使用
    if (defaultValue?.type === 'customDate') {
      return defaultValue?.value;
    }
    // 否则从预设列表中查找
    return (
      presetList.find((item: any) => item.type === defaultValue?.type)?.value ||
      presetList[0].value
    );
  });
  const curTimeType = useRef<any>(
    presetList.find((item: any) => item.type === defaultValue?.type)
      ?.timeType || presetList[0].timeType,
  );
  // 需要区分哪次onchange是通过点击预设触发的
  let selectNum = 0,
    changeNum = 0;
  const onSelect = (dateFilterRelativeUnit?: number, type?: string) => {
    selectNum += 1;
    // 预设范围选中回调，会在onChange之前调用
    curTimeType.current = dateFilterRelativeUnit;
    if (type === 'clear') {
      setCurDefaultValue([]);
      handleChange([], curTimeType.current);
      setKey(Date.now());
    }
  };
  const presets =
    pickerType === 'picker-filter'
      ? getOtherPresets(onSelect)
      : getPresets(onSelect);

  // 初始化不触发
  const onChange = (value: any) => {
    changeNum += 1;
    if (!value?.length) {
      changeNum = 0;
      selectNum = 0;
      return;
    }
    const date: [Dayjs, Dayjs] = [dayjs(value[0]), dayjs(value[1])];
    let dateFilterRelativeUnit;
    if (selectNum === changeNum) {
      // 是通过点击预设触发的，直接取预设的timeType
      dateFilterRelativeUnit = curTimeType.current;
    } else {
      // 是通过直接在面板中选择的
      dateFilterRelativeUnit = getPanelDateFilterRelativeUnit(date);
    }
    changeNum = 0;
    selectNum = 0;
    handleChange(
      [
        dayjs(value[0]).startOf('day').valueOf(),
        dayjs(value[1]).endOf('day').valueOf(),
      ],
      dateFilterRelativeUnit,
    );
  };
  console.log(value, 'datevalue-----5555', curDefaultValue, 'key', key);
  return (
    <div className="lego-date-picker-filter">
      <DateRangePicker
        // @ts-ignore
        key={key}
        isMobile={isMobile()}
        onChange={(dateNum) => {
          console.log(
            dateNum,
            'datevalue-----3333',
            dateNum && dayjs(dateNum[0]).startOf('day').format('YYYY-MM-DD'),
            dateNum && dayjs(dateNum[1]).startOf('day').format('YYYY-MM-DD'),
          );
          onChange(dateNum);
        }}
        presets={presets}
        min={new Date('2021/01/01')}
        max={dayjs().toDate()}
        // 最多可选天数，根据配置动态设置
        maxStep={maxStep}
        defaultValue={curDefaultValue}
        allowClear={false}
        // getPopupContainer={(trigger: any) => trigger.parentNode}
        placement="bottomLeft"
        config={{
          disabled: disabledType,
          renderExtraFooter: (params) => {
            return (
              <div className="render-footer">
                {renderExtraFooter && renderExtraFooter(params)}
              </div>
            );
          },
          onOpenChange: onOpenChange,
        }}
        placeholder={placeholder}
        {...(!isMobile() ? { value: value || curDefaultValue } : {})}
      />
    </div>
  );
};
