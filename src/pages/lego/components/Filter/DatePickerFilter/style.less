.ant-picker-dropdown {
  .ant-picker-panel-container {
    .ant-picker-panel-layout {
      .ant-picker-presets {
        min-width: 70px;

        ul {
          &::-webkit-scrollbar-thumb {
            background-color: transparent;
          }

          &:hover::-webkit-scrollbar-thumb {
            background-color: rgba(0, 0, 0, 0.3);
          }

          li {
            padding: 0;

            div {
              text-align: center;
              width: 100%;
            }
          }
        }
      }

      .ant-picker-panels {
        .ant-picker-panel {
          .ant-picker-date-panel {
            width: 260px;
          }
        }
      }
    }
    .render-footer {
      font-size: 14px;
      /* 样式描述：强调、主要信息 */
      color: #1d2129;
    }
  }
}
.blm-web-ui-date-range-picker-pc {
  width: 100%;
}
