.ai-wrapper {
  background-color: #fff;
  border-radius: 8px;
  // 头部设置
  .ai-header {
    height: 50px;
    background: transparent;
    padding: 15px 20px 0 10px !important;
  }

  .ant-collapse {
    border-radius: 8px;
    background-color: #fff;
    border: none;
  }

  .ant-collapse-item {
    border-bottom: 0;
  }

  .title {
    span:nth-child(1) {
      margin-left: 2px;
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;
      letter-spacing: 0em;
      color: rgba(0, 0, 0, 0.9);
    }

    span:nth-child(2) {
      margin-left: 8px;
      font-size: 12px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: 0em;
      /* 信息/font-black2 */
      /* 样式描述：次强调信息 */
      color: rgba(0, 0, 0, 0.6);
    }
  }
  .pointPig {
    display: flex;
    flex-direction: row;
    align-items: center;
    span {
      margin-right: 8px;
    }
  }

  .button_detail {
    display: flex;
    gap: 16px;
    font-size: 14px;
    font-weight: normal;
    line-height: 22px;
    letter-spacing: 0em;
    color: #366cfe;
  }

  // 内容
  .ant-collapse-content-box {
    padding: 0 !important;
  }

  .detail {
    padding-bottom: 12px;
    height: 350px;

    .chart-box {
      display: flex;
      flex-direction: row;
      height: 350px;
    }

    .chart {
      display: flex;
      flex-direction: column;
      min-width: 740px;
      flex: 3;
      padding: 10px;
      height: 350px;
      border-width: 0px 1px 0px 0px;
      border-style: solid;
      border-color: #e7e8eb;

      .chart-title {
        flex-flow: wrap;
        display: flex;
        flex-direction: row;
        align-content: center;
        height: 40px;
        border-radius: 6px;
        background: #f3f3f4;
        padding: 0 12px;
        margin-bottom: 8px;
      }

      .chart-render {
        flex: 1;
      }
    }

    .result {
      box-sizing: border-box;
      flex: 2;
      display: flex;
      flex-direction: column;
      box-sizing: border-box;
      padding: 4px 12px;
      height: 350px;
      overflow-y: auto;

      .relate {
        border-width: 0px 0px 1px 0px;
        border-style: solid;
        border-color: #e7e8eb;
        padding-bottom: 6px;
      }

      .interpretation {
        padding-top: 6px;
      }

      .result-title {
        font-size: 16px;
        font-weight: 500;
        line-height: 24px;
        letter-spacing: 0em;
        color: rgba(0, 0, 0, 0.9);
      }

      .right {
        display: flex;
        justify-content: end;
        width: 50px;

        .button-expand {
          font-size: 14px;
          font-weight: normal;
          line-height: 22px;
          letter-spacing: 0em;
          color: #366cfe;
          cursor: pointer;
        }
      }

      .left {
        flex: 1;

        .title {
          font-weight: 500;
          font-size: 14px;
          color: #ed7b2f;
          margin-bottom: 6px;
        }
      }
      .noData {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        span {
          margin-top: 6px;
        }
      }
    }

    .loading {
      height: 190px;
    }

    .loading {
      text-align: center;
      padding-top: 88px;

      p {
        margin-top: 8px;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: 0em;

        /* 信息/font-black1 */
        /* 样式描述：强调信息 */
        color: rgba(0, 0, 0, 0.9);
      }
    }
  }

  .data-explain {
    color: rgba(0, 0, 0, 0.9);
    > ol {
      ol {
        padding-inline-start: 24px;
      }
    }
    header {
      font-weight: 500;
      font-size: 14px;
    }

    span {
      font-size: 14px;
      color: #ed7b2f;
    }

    > ol {
      padding-inline-start: 0;
      text-decoration: none;
      list-style: none;
    }

    h1 {
      font-weight: 500;
      font-size: 14px;
      margin-bottom: 4px;
    }

    ul {
      text-decoration: none;
      list-style: none;
    }

    ul {
      padding-inline-start: 20px;
      margin-bottom: 6px;
    }
  }

  .data-explain-morePower {
    color: rgba(0, 0, 0, 0.9);
    > ol {
      ol {
        padding-inline-start: 24px;
      }
    }
    header {
      font-weight: 500;
      font-size: 14px;
    }

    span {
      font-size: 14px;
      color: #ed7b2f;
    }

    > ol {
      padding-inline-start: 24px;
      text-decoration: none;
      //list-style: none;
    }

    h1 {
      font-weight: 400;
      font-size: 14px;
      margin-bottom: 4px;
    }

    ul {
      text-decoration: none;
      list-style: none;
      padding-inline-start: 20px;
      margin-bottom: 6px;
    }
  }

  :where(.css-n2cne7).ant-spin-nested-loading .ant-spin-blur {
    opacity: 0 !important;
  }

  .tipTitleCss {
    cursor: not-allowed;
    color: rgba(0, 0, 0, 0.24);
  }
}
