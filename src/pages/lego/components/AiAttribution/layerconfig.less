.layerConfigDrawerCss {
  //.ant-drawer-content-wrapper {
  //  width: 1080px !important;
  //}
  .ant-drawer-header {
    background: #fff !important;
  }
  .ant-drawer-body {
    background: #fff !important;
    padding: 8px 0px !important;
  }
  .header {
    display: flex;
    flex-direction: row;
    gap: 10px;
    margin-right: 16px;
    align-items: center;
    flex-wrap: wrap;
    .city-config {
      cursor: pointer;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      letter-spacing: 0em;
      color: #366cfe;
    }
  }

  .tips-content {
    width: 1048px;
    height: 84px;
    margin-top: 12px;
    display: flex;
    flex-direction: row;
    border-radius: 6px;
    opacity: 0.98;
    background: #e8f2ff;
    box-sizing: border-box;
    /* 语义/border-blue2 */
    border: 1px solid #c4dcff;
    padding: 8px 12px;
    overflow: hidden;
    .left {
      padding-top: 4px;
      flex-basis: 24px;
      flex-grow: 0;
      flex-shrink: 0;
    }
    .right {
      //   display: flex;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      letter-spacing: 0em;
      color: rgba(0, 0, 0, 0.9);
      p {
        margin-bottom: 6px;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: 0em;
        color: rgba(0, 0, 0, 0.9);
      }
      .button-expand {
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: 0em;
        color: #366cfe;
        cursor: pointer;
      }
    }
  }
  .card-content {
    .ant-pagination .ant-pagination-total-text {
      position: relative;
    }
    .ant-pagination-total-text {
      margin-right: 30px;
      top: 2px;
    }
  }
  .cards-box {
    min-width: 1048px;
    box-sizing: border-box;
    display: flex;
    height: 552px;
    flex-direction: row;
    justify-content: flex-start;
    align-content: start;
    flex-wrap: wrap;
    column-gap: 26px;
    margin-bottom: 20px;
    min-height: 200px;
    .ant-empty {
      margin: auto;
      margin-top: 50px;
    }

    .no-data {
      width: 120px;
      margin: 200px auto;
      .no-data-icon {
        width: 120px;
        height: 120px;
        background-image: url(.././../../../assets/img/lego-noData.png);
        background-size: 100% 100%;
      }
      p {
        margin-top: 12px;
        text-align: center;
        font-size: 14px;
        font-weight: normal;
        line-height: 22px;
        letter-spacing: 0em;
        color: rgba(0, 0, 0, 0.9);
      }
    }
  }
  .tag-box {
    padding-top: 2px;
  }
  .delete-title {
    display: flex;
    flex-direction: row;
    align-content: center;
  }

  .delete-modal {
    border: none !important;
  }
}
