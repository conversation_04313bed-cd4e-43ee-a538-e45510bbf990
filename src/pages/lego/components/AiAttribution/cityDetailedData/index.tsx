import React, { useEffect, useRef, useState } from 'react';
import { Drawer, Pagination, Table as BLMTable } from '@blmcp/ui';
import './cityDetailedCSS.less';
import dayjs from 'dayjs';
import {
  queryCityDetailList,
  queryCityDetailReport,
} from '@/pages/lego/api/aiAttribution';
import { getCityInfo } from '@/pages/lego/utils';
import {
  blmAnalysisModuleClick,
  blmAnalysisModuleExposure,
} from '@/utils/eventTracking';
import { reportStore } from '@blm/bi-lego-sdk/dist/es/utils';

const CityDetailedData = (props) => {
  const containerRef = useRef(null);
  const [loading, setLoading] = useState(false);
  // 初始值，基于实际情况调整
  const [tableHeight, setTableHeight] = useState('200px');
  const [open, setOpen] = useState(false);
  const [cityCodeList, setCityCodeList] = useState([]);
  // 报告数据展示
  const [cityReport, setCityReport] = useState('');
  // table数据
  const [dataS, setDataS] = useState([]);
  const [tableTotal, setTableTotal] = useState(0);
  const [pageSize, setPageSize] = useState(20);
  const [pageNum, setPageNum] = useState(1);
  const [orderKey, setOrderKey] = useState('');
  const [sort, setSort] = useState('');
  const [reportData, setReportData] = useState({});

  const openNewWindow = (item) => {
    const reportId = props.reportId;
    const status = reportStore.get(props.uuid)?.publishStatus;
    const cityId = item?.adcode ?? '';
    const textContent = item?.adname ?? '';
    const { pCityId } = getCityInfo(props.cityOptions, cityId);
    blmAnalysisModuleClick({
      pageId: 'p_leopard_cp_00000480',
      eventId: 'e_leopard_cp_click_00003220',
      ext: {
        str0_e: textContent,
        str1_e: '城市明细数据',
      },
    });
    // share-url-hooks-disable-next-line
    window.open(
      `/qbi/legoBI/view?reportId=${reportId}&publish=${
        status || 0
      }&cityId=${cityId}&pCityId=${pCityId}`,
    );
  };

  // 格式化日期为“X月X日”
  const formatDateToMonthDay = (date) => {
    const month = String(date.month() + 1).padStart(2, '0'); // 月份是从0开始的
    const day = String(date.date()).padStart(2, '0');
    return `${month}月${day}日`;
  };
  const getTime = (val) => {
    // 获取当前日期
    let currentDate = dayjs();
    let textName = '';
    if (val) {
      let previousDay = currentDate.subtract(val, 'day');
      textName = formatDateToMonthDay(previousDay);
    }
    return textName;
  };

  const getSortOrder = (name = '') => {
    let sortOrder = null;
    if (orderKey === name) {
      if (sort === 'asc') {
        sortOrder = 'ascend';
      } else if (sort === 'desc') {
        sortOrder = 'descend';
      }
    }

    return sortOrder;
  };
  // table表头
  const columns = [
    {
      title: '城市',
      dataIndex: 'adname',
      // width: '80px',
      fixed: 'left',
      render: (text: string, record, index) => {
        return (
          <div
            style={{ width: '100%', color: '#366cfe', cursor: 'pointer' }}
            onClick={() => openNewWindow(record)}
          >
            {text}
          </div>
        );
      },
    },
    // {
    //   title: '贡献度排名',
    //   dataIndex: 'rank',
    //   width: '120px',
    //   sorter: true,
    // },
    {
      // 基期完单量 转换成 T-8 的时间显示
      title: `${getTime(8)}完单量`,
      dataIndex: 'cntOrdDoneBase',
      width: '150px',
      sorter: true,
      sortOrder: getSortOrder('cntOrdDoneBase'),
    },
    {
      // 本期完单量 转换成 T-1的时间显示
      title: `${getTime(1)}完单量`,
      dataIndex: 'cntOrdDoneExp',
      width: '150px',
      sorter: true,
      sortOrder: getSortOrder('cntOrdDoneExp'),
    },
    {
      title: '完单量变化值',
      dataIndex: 'cntOrdDoneChange',
      width: '130px',
      sorter: true,
      sortOrder: getSortOrder('cntOrdDoneChange'),
    },
    {
      title: '完单量变化率',
      dataIndex: 'cntOrdDoneRate',
      width: '130px',
      sorter: true,
      sortOrder: getSortOrder('cntOrdDoneRate'),
      render: (text: string) => <span>{text}%</span>,
    },
    {
      title: '行业完单量变化区间',
      dataIndex: 'cntOrdDoneCityUp',
      width: '150px',
      render: (text: string, record: any) => {
        return (
          <span>
            {record.cntOrdDoneCityDown}% ~ {record.cntOrdDoneCityUp}%
          </span>
        );
      },
    },
    {
      title: `${getTime(8)}出车司机数`,
      dataIndex: 'cntDrvOnlineBase',
      width: '180px',
      sorter: true,
      sortOrder: getSortOrder('cntDrvOnlineBase'),
    },
    {
      title: `${getTime(1)}出车司机数`,
      dataIndex: 'cntDrvOnlineExp',
      width: '180px',
      sorter: true,
      sortOrder: getSortOrder('cntDrvOnlineExp'),
    },
    {
      title: '出车司机变化值',
      dataIndex: 'cntDrvOnlineChange',
      width: '150px',
      sorter: true,
      sortOrder: getSortOrder('cntDrvOnlineChange'),
    },
    {
      title: '出车司机数变化率',
      dataIndex: 'cntDrvOnlineRate',
      width: '160px',
      sorter: true,
      sortOrder: getSortOrder('cntDrvOnlineRate'),
      render: (text: string) => <span>{text}%</span>,
    },
    {
      title: '行业出车司机数变化区间',
      dataIndex: 'cntDrvOnlineCityUp',
      width: '180px',
      render: (text: string, record: any) => {
        return (
          <span>
            {record.cntDrvOnlineCityDown}% ~ {record.cntDrvOnlineCityUp}%
          </span>
        );
      },
    },
  ];

  // 参数重置
  const resetting = () => {
    setPageNum(1);
    setPageSize(20);
    setOrderKey('');
    setSort('');
  };
  // 关闭抽屉
  const onClose = () => {
    setOpen(false);
    if (props.onClose) {
      props.onClose();
    }
  };

  const showReportHtml = (reportDom) => {
    const decodeHtml = (
      <div
        className="data-explain"
        dangerouslySetInnerHTML={{ __html: reportDom }}
      />
    );
    return decodeHtml;
  };
  // 获取城市明细报告
  const getCityReport = async () => {
    try {
      // 调取报告接口
      const { code, data } = await queryCityDetailReport({
        adcode: cityCodeList,
      });
      setReportData(data);
      // 处理数据
      if (code === 1 && data?.report) {
        setCityReport(data.report);
      } else {
        setCityReport('');
      }
    } catch (err) {
      // message.warning(err);
    }
  };

  // 获取表格数据
  const getTableData = async () => {
    const params = {
      adcode: cityCodeList ?? [],
      pageNum,
      pageSize,
      orderKey: orderKey,
      sort: sort,
    };
    try {
      setLoading(true);
      const res = await queryCityDetailList(params);
      if (res.code === 1) {
        const { data } = res;
        setDataS(data.items);
        setTableTotal(data.totalNum);
      } else {
        setDataS([]);
        setTableTotal(0);
      }
      setLoading(false);
    } catch (err) {
      // message.warning(err);
    }
  };

  // 分页改变回调
  const pageInfoChange = (pageNum, pageSize) => {
    setPageNum(pageNum);
    setPageSize(pageSize);
  };

  //
  const tableChange = (pagination, filters, sorter, extra) => {
    const { column, order } = sorter;
    let sort = '';
    let sortKey = column?.dataIndex ?? '';
    if (order === 'ascend') {
      sort = 'asc';
    } else if (order === 'descend') {
      sort = 'desc';
    } else {
      sort = '';
    }

    setOrderKey(sortKey);
    setSort(sort);
    // 分页重置为1
    setPageNum(1);
  };
  useEffect(() => {
    setOpen(props.open);
    if (props.open) {
      resetting();
      // 获取选中的城市数据
      setCityCodeList(props.cityInfo);

      // 曝光
      blmAnalysisModuleExposure({
        pageId: 'p_leopard_cp_00000480',
        eventId: 'e_leopard_cp_exposure_00003122',
      });
    }
  }, [props.open, props.cityInfo]);

  useEffect(() => {
    if (open) {
      // 获取报告
      getCityReport();
      // 获取表格数据
      getTableData();

      // table的高度
      function adjustTableHeight() {
        if (containerRef.current) {
          const containerHeight = containerRef.current.offsetHeight;
          let adjustedHeight = `${containerHeight - 50}px`;
          setTableHeight(adjustedHeight);
        }
      }

      window.addEventListener('resize', adjustTableHeight);
      adjustTableHeight();

      return () => {
        window.removeEventListener('resize', adjustTableHeight);
      };
    }
  }, [open, cityCodeList]);

  useEffect(() => {
    if (open) {
      if (pageNum === 1) {
        getTableData();
      } else {
        setPageNum(1);
      }
    }
  }, [open, pageSize]);
  useEffect(() => {
    if (open) {
      getTableData();
    }
  }, [open, pageNum, orderKey, sort]);

  return (
    <Drawer
      title="城市明细数据"
      width={1090}
      className="cityDetailDrawer"
      open={open}
      destroyOnClose={true}
      onClose={onClose}
    >
      <div className="cityDetailDrawerBody">
        <div className="summary">
          <div>{showReportHtml(cityReport)}</div>
          {reportData?.noPermissionCityNameList ? (
            <div>
              未展示
              <span className="summary_span">
                {reportData.noPermissionCityNameList?.length}
              </span>
              条无权限城市数据，分别是
              <span>{reportData.noPermissionCityNameList?.join('、')}</span>
              。请联系管理员开通城市的全部权限
            </div>
          ) : null}
        </div>
        <div ref={containerRef} style={{ height: 'calc(100% - 70px)' }}>
          <BLMTable
            loading={loading}
            columns={columns}
            dataSource={dataS}
            bordered={true}
            showSorterTooltip={false}
            scroll={{ x: 1680, y: tableHeight }}
            pagination={false}
            onChange={tableChange}
          />
        </div>
        <div className="paginCss">
          <Pagination
            current={pageNum}
            pageSize={pageSize}
            total={tableTotal}
            showSizeChanger
            showQuickJumper
            defaultPageSize={20}
            pageSizeOptions={[20, 50, 100]}
            showTotal={(total) => `共 ${total} 条`}
            onChange={(page, pageSize) => pageInfoChange(page, pageSize)}
          />
        </div>
      </div>
    </Drawer>
  );
};

export default CityDetailedData;
