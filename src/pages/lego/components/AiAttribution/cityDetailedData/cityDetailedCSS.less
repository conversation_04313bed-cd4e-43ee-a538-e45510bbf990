// 城市明细数据
.cityDetailDrawer {
  box-sizing: border-box;

  .ant-drawer-header {
    background: #f1f1f2 !important;
  }

  .ant-table {
    scrollbar-color: unset !important;
  }

  .ant-table-body::-webkit-scrollbar {
    height: 8px;
    width: 8px;
  }

  .ant-table-body::-webkit-scrollbar-thumb {
    background-color: #999;
    border-radius: 10px;
    width: 12px;
  }

  .ant-table-body::-webkit-scrollbar-thumb:horizontal {
    height: 12px;
  }

  .ant-table-body::-webkit-scrollbar-track {
    border-radius: 10px;
  }

  .ant-drawer-body {
    background: #f1f1f2 !important;
    margin: 0 !important;
    padding: 0 8px !important;
    border-radius: 0 !important;
  }

  .cityDetailDrawerBody {
    padding: 16px;
    height: calc(100% - 8px);
    border-radius: 8px;
    background: #fff;

    .summary {
      margin-bottom: 8px;
      min-height: 22px;
      line-height: 22px;
      font-size: 14px;
      overflow: hidden;
      color: rgba(0, 0, 0, 0.9);
      .summary_span {
        color: #ed7b2f;
      }
    }

    .paginCss {
      margin-top: 15px;

      .ant-pagination {
        padding: 4px 16px;
      }

      .ant-pagination .ant-pagination-total-text {
        left: 25px;
      }
    }
  }

  .data-explain {
    color: rgba(0, 0, 0, 090%);

    > ol {
      padding-inline-start: 0;
      text-decoration: none;
      list-style: none;

      ol {
        padding-inline-start: 24px;
      }
    }

    header {
      font-weight: 500;
      font-size: 14px;
    }

    span {
      font-size: 14px;
      color: #ed7b2f;
    }

    h1 {
      font-weight: 500;
      font-size: 14px;
      margin-bottom: 4px;
    }

    ul {
      text-decoration: none;
      list-style: none;
      padding-inline-start: 20px;
      margin-bottom: 6px;
    }
  }
}
