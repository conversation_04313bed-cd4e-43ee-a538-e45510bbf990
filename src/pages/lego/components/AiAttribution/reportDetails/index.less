.drawerBox {
  box-sizing: border-box;
  color: rgba(0, 0, 0, 0.9);

  .ant-drawer-header {
    background: #f1f1f2 !important;
  }

  .ant-drawer-body {
    background: #f1f1f2 !important;
    padding: 0px 8px !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }

  :where(.css-iksrmj).ant-segmented .ant-segmented-item-selected {
    color: rgba(0, 0, 0, 0.6);
  }

  .color06 {
    color: rgba(0, 0, 0, 0.6);
  }

  .color09 {
    color: rgba(0, 0, 0, 0.9);
  }

  .color303133 {
    color: #303133;
  }

  .ellipsis {
    /* 确保文本不会换行 */
    white-space: nowrap;
    /* 防止内容溢出容器 */
    overflow: hidden;
    /* 当文本溢出容器时，使用省略号表示 */
    text-overflow: ellipsis;
    /* 可选：为文本设置一个固定的宽度，使其有可能溢出 */
    //width: 100%; /* 或者其他你需要的宽度 */
  }
}

.reportContent {
  height: calc(100% - 10px);
  overflow-y: auto;

  .size12 {
    font-size: 12px;
    line-height: 20px;
  }

  .size14 {
    font-size: 14px;
    line-height: 22px;
  }

  .size22 {
    color: #000;
    font-size: 22px;
    line-height: 26px;
    font-weight: 500;
  }

  .weight500 {
    font-weight: 500;
  }

  .topBaseInfo {
    margin: 0 0 8px 0;
    padding: 12px;
    height: 162px;
    width: 1072px;
    border-radius: 8px;
    background-color: #ffffff;

    &_title {
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;
      color: #3d3d3d;
    }

    &_content {
      display: flex;
      margin: 8px 0 12px 0;
      height: 66px;
      color: rgba(0, 0, 0, 0.6);
      font-size: 12px;
      line-height: 20px;

      &_info {
        display: flex;
        flex: 1;
        padding-right: 16px;
        // 对照
        &_contrast {
        }

        &_arrow {
          display: flex;
          flex: 1;
          text-align: center;
          align-items: center; /* 垂直居中 */
          flex-direction: column;
          justify-content: center;
        }

        // 归因
        &_attribution {
        }
      }

      // 比例
      &_proportion {
        display: flex;
        flex-direction: column;
        justify-content: center;
        flex: 1;
        //display: inline-block;
        padding-left: 16px;
        height: 100%;
        border-left: 1px solid rgba(0, 0, 0, 0.2);

        .arrow {
          width: 18px;
          height: 16px;
        }

        .arrowGreen {
          transform: rotate(180deg);
        }
      }
    }

    .reportText {
      height: 30px;
      padding: 4px 0;
      overflow: hidden;

      &:hover {
        border-radius: 4px;
        background-color: #f3f3f4;
      }
    }
  }

  .analysisPath {
    padding: 12px;
    height: calc(100% - 178px);
    min-height: 500px;
    width: 1072px;
    //height: 100%;
    border-radius: 8px;
    background-color: #ffffff;

    &_title {
      font-size: 16px;
      line-height: 24px;
      margin-bottom: 8px;
      font-weight: 500;
    }

    &_box {
      display: flex;
      height: calc(100% - 28px);
    }

    &_left {
      display: flex;
      flex-wrap: wrap;
      align-content: flex-start;
      //height: calc(100% - 28px);
      width: 154px;
      border-right: 1px solid #e7e8eb;
      overflow: auto;

      &_box {
        display: flex;
        justify-content: space-between;
        flex-direction: row;
        align-items: center;
        padding: 0 8px;
        height: 50px;
        width: 100%;
        line-height: 50px;
        font-size: 14px;
        border-bottom: 1px solid #e7e8eb;

        &:hover {
          cursor: pointer;
          background-color: #eeeef0;
        }

        &_label {
          display: inline-block;
          width: 120px;
        }

        &_icon {
          float: right;
          margin: 18px 8px;
          width: 14px;
          height: 14px;
        }
      }
    }

    &_rightBox {
      display: flex;
      flex-direction: column;
      padding-left: 12px;
      width: calc(100% - 154px);
      //height: calc(100% - 28px);
    }

    &_right {
      display: flex;
      flex: 1;
      flex-direction: column;
      margin-bottom: 8px;
      padding-bottom: 8px;
      border-bottom: 1px solid #e7e8eb;

      &_top {
        display: flex;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        &_text {
          display: flex;
          align-items: center;
        }
      }
    }

    // 数据解析
    &_decode {
      //padding-top: 8px;
      flex: 1;
    }
    .result {
      padding-top: 10px;
    }
  }

  .data-explain_title {
    font-size: 14px;
    font-weight: 500;
    line-height: 22px;
    color: #ed7b2f;
  }

  .data-explain_contents {
    display: inline-block;
    height: 100%;
    width: 965px;
    vertical-align: top;
  }

  .chartContent {
    height: 320px;
  }

  .tooltipCard {
    :where(.css-iksrmj).ant-tooltip .ant-tooltip-inner {
      border-right: 8px !important;
      box-shadow: 5px 0px 10px 2px rgba(42, 48, 72, 0.2) !important;
    }
  }

  .data-explain {
    color: rgba(0, 0, 0, 0.9);

    header {
      font-weight: 500;
      font-size: 14px;
      line-height: 24px;
    }

    span {
      font-size: 14px;
      color: #ed7b2f;
    }

    h1 {
      font-weight: 500;
      font-size: 14px;
      margin-bottom: 4px;
    }

    ol {
      padding-inline-start: 24px;
    }

    ul,
    li {
      text-decoration: none;
      //list-style: none;
    }

    ul {
      padding-inline-start: 20px;
      margin-bottom: 6px;
    }
  }
}

// 全分层
.fully {
  .fully_title {
    margin: 8px 0;
    color: rgba(0, 0, 0, 0.6);
  }

  .fully_text {
    margin-top: 8px;
    color: rgba(0, 0, 0, 0.6);
  }

  .popoverContent {
    color: rgba(0, 0, 0, 0.6);

    &_title {
      font-size: 14px;
      font-weight: 500;
      line-height: 22px;
      color: rgba(0, 0, 0, 0.9);
    }

    &_num {
      float: right;
      color: rgba(0, 0, 0, 0.9);
    }
  }

  .ant-table-wrapper .ant-table {
    color: #303133 !important;
    font-weight: normal !important;
  }

  .ant-table-wrapper.BLMTable_Antd .ant-table-thead > tr > th,
  .BLM_Antd_content .ant-table-wrapper .ant-table-thead > tr > th {
    color: #303133 !important;
    font-weight: normal !important;
  }

  .ant-table-wrapper.BLMTable_Antd .ant-table-tbody > tr > td,
  .BLM_Antd_content .ant-table-wrapper .ant-table-tbody > tr > td {
    border-bottom: 1px solid #f0f0f0 !important;
  }

  .ant-table-wrapper .ant-table {
    border-bottom: 0px !important;
    border-radius: 6px;
  }

  :where(.css-iksrmj).ant-table-wrapper .ant-table-container {
    border-bottom-left-radius: 6px;
    border-bottom-right-radius: 6px;
  }

  .ant-table.BLMTable_Antd
    .ant-table-tbody
    > tr.ant-table-row:hover
    > td:first-child,
  .BLM_Antd_content
    .ant-table
    .ant-table-tbody
    > tr.ant-table-row:hover
    > td:first-child {
    border-radius: 0 !important;
  }

  .ant-table.BLMTable_Antd
    .ant-table-tbody
    > tr.ant-table-row:hover
    > td:last-child,
  .BLM_Antd_content
    .ant-table
    .ant-table-tbody
    > tr.ant-table-row:hover
    > td:last-child {
    border-radius: 0 !important;
  }

  .BLM_Antd_content
    .ant-table
    .ant-table-tbody
    > tr.ant-table-row:last-child
    > td:first-child,
  .ant-table.BLMTable_Antd
    .ant-table-tbody
    > tr.ant-table-row:last-child:hover
    > td:first-child,
  .BLM_Antd_content
    .ant-table
    .ant-table-tbody
    > tr.ant-table-row:last-child:hover
    > td:first-child {
    border-bottom-left-radius: 6px !important;
  }

  .BLM_Antd_content
    .ant-table
    .ant-table-tbody
    > tr.ant-table-row:last-child
    > td:last-child,
  .ant-table.BLMTable_Antd
    .ant-table-tbody
    > tr.ant-table-row:last-child:hover
    > td:last-child,
  .BLM_Antd_content
    .ant-table
    .ant-table-tbody
    > tr.ant-table-row:last-child:hover
    > td:last-child {
    border-bottom-right-radius: 6px !important;
  }
}

.reportDetails_tooltip_data_explain {
  color: rgba(0, 0, 0, 0.9);

  > ol {
    ol {
      padding-inline-start: 24px;
    }
  }

  header {
    font-weight: 500;
    font-size: 14px;
  }

  span {
    font-size: 14px;
    color: #ed7b2f;
  }

  > ol {
    padding-inline-start: 0;
    text-decoration: none;
    list-style: none;
  }

  h1 {
    font-weight: 500;
    font-size: 14px;
    margin-bottom: 4px;
  }

  ul {
    text-decoration: none;
    list-style: none;
  }

  ul {
    padding-inline-start: 20px;
    margin-bottom: 6px;
  }
}
