import React, { useEffect, useState } from 'react';
import { Button, Drawer, message, Segmented, Spin, Tooltip } from '@blmcp/ui';
import { ExclamationCircleFilled, LoadingOutlined } from '@ant-design/icons';
import './index.less';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash';
import { Coordinate } from '@blmcp/charts';
import {
  queryAllLayerChartsByPath,
  queryLayerChartsByPath,
} from '@/pages/lego/api/aiAttribution';
import {
  blmAnalysisModuleClick,
  blmAnalysisModuleExposure,
} from '@/utils/eventTracking';
import { useTooltipShow } from '@/pages/lego/libraryMaterials/hooks/useTooltipShow';
import RouteImg from '../img/route.svg';
import ArrowGreen from '../img/arrowGreen.svg';
import ArrowRed from '../img/arrowRed.svg';
import Describe from '../img/describe.svg';
import FullyLayered from './fullyLayered';

const IsShowToolTip = ({ item = '', otherVal = '', widthNum = '100%' }) => {
  const { tooltipEnable, textRef } = useTooltipShow(item, otherVal);
  const BoxDOM = (
    <span
      ref={textRef}
      className={'ellipsis'}
      style={{ width: widthNum, display: 'block' }}
    >
      {item}
    </span>
  );
  return tooltipEnable ? <Tooltip title={item}>{BoxDOM}</Tooltip> : BoxDOM;
};

const ReportDetails = (props) => {
  const [open, setOpen] = useState(false);
  const [drawerKey, setDrawerKey] = useState(null);
  // 城市
  const [adcode, setAdcode] = useState('');
  // 基础数据
  const [oneInfo, setOneInfo] = useState({});
  const [daysDifference, setDaysDifference] = useState(0);
  // 路径分析数据集合
  const [dataList, setDataList] = useState([]);
  // 选中的路径分析数据
  const [showChartInfo, setShowChartInfo] = useState({
    key: null,
    path: '',
    pathName: '',
    describe: '',
  });
  const [loading, setLoading] = useState(false);
  // 图表类型
  const [chartType, setChartType] = useState(2);
  // 图表数据
  const [dataset, setDataset] = useState([]);
  // 全分层数据
  const [layeredList, setLayeredList] = useState([]);
  // 展开/收起  展开：isRetract = false； 收起：isRetract = true
  const [isRetract, setIsRetract] = useState(false);
  // 数据解读
  const [reportDom, setReportDom] = useState('');
  const [selectKey, setSelectKey] = useState(1);

  const optionsLary = [
    {
      label: '司机分层',
      value: 1,
    },
    {
      label: '时段分层',
      value: 2,
    },
    {
      label: '全分层',
      value: 3,
    },
  ];
  const pathCollection = {
    cnt_ord_call: {
      pathName: '需求-发单量',
      describe: '乘客下发的订单量',
    },
    cnt_drv_online: {
      pathName: '供给-出车人数',
      describe: '有过出车行为的司机数',
    },
    cnt_drv_done: {
      pathName: '供给-完单人数',
      describe: '有过确认费用行为的司机数',
    },
    dur_fixed_price: {
      pathName: '效率-人均听一口价时长',
      describe: '一口价听单总时长/出车人数',
    },
    total_score: {
      pathName: '效率-出车司机人均服务分',
      describe: '出车司机的服务分总和/出车人数',
    },
    avg_dur_online: {
      pathName: '效率-人均出车时长',
      describe: '出车总时长/出车人数',
    },
    speed: {
      pathName: '效率-接送驾速度',
      describe: '（接驾里程+送驾里程）/（接驾时间+送驾时间）',
    },
    cancel_rate: {
      pathName: '效率-司乘取消率',
      describe: '（应答后司机取消量+应答后乘客取消量）/应答量',
    },
  };

  const dimensions = [
    { name: 'cate' },
    { name: 'baseValue', displayName: '', seriesType: 'bar' },
    { name: 'expValue', displayName: '', seriesType: 'bar' },
    { name: 'rate', displayName: '变化率', seriesType: 'line', yAxisIndex: 1 },
  ];
  const [dimensionsInfo, setDimensionsInfo] = useState(dimensions);
  const overlayInnerCss = {
    width: '500px',
    maxHeight: '300px',
    overflow: 'auto',
  };

  // 关闭抽屉
  const onClose = () => {
    setOpen(false);
    // setDrawerKey(null);
    if (props.onClose) {
      props.onClose();
    }
  };

  // 图表渲染
  const chartInit = () => {
    return (
      <Coordinate
        dataSource={{ dimensions: dimensionsInfo, source: dataset }}
        yAxis0Formatter={(value) => {
          if (showChartInfo?.path === 'cancel_rate') {
            return `${Math.floor(value * 100) / 100}%`;
          }
          return Number(value.toFixed(2));
        }}
        yAxis1Formatter={(value) => {
          return `${Math.floor(value * 100) / 100}%`;
        }}
        tooltipFormatter={(value, item: any) => {
          if (item.name === 'rate') {
            return `${value}%`;
          } else if (showChartInfo?.path === 'cancel_rate') {
            return `${value}%`;
          }
        }}
      ></Coordinate>
    );
  };

  // 格式化日期为“X月X日”
  const formatDateToMonthDay = (date) => {
    const month = String(date.month() + 1).padStart(2, '0'); // 月份是从0开始的
    const day = String(date.date()).padStart(2, '0');
    return `${month}月${day}日`;
  };
  const getTime = (val) => {
    // 获取当前日期
    let currentDate = dayjs();
    let textName = '';
    if (val) {
      let previousDay = currentDate.subtract(val, 'day');
      textName = formatDateToMonthDay(previousDay);
    }
    return textName;
  };
  const showDecode = (reportDom, titleText = '') => {
    const decodeHtml = (
      <div
        className="data-explain"
        dangerouslySetInnerHTML={{ __html: reportDom }}
      />
    );
    return decodeHtml;
  };
  const showDecodeTooltip = (reportDom, titleText = '') => {
    const decodeHtml = (
      <div
        className="reportDetails_tooltip_data_explain"
        dangerouslySetInnerHTML={{ __html: reportDom }}
      />
    );
    return decodeHtml;
  };

  // 获取分层数据
  const getLayeredData = (path = '', type = '') => {
    if (!(path && type) && open) {
      message.error('请选择分析路径和图表分层');
      return;
    }
    setLoading(true);
    // 获取图表数据
    const chartParams = {
      layerType: props.layerType,
      adcode,
      type, // 时段分层
      path,
    };
    // 发单量 只显示司机分层数据
    if (chartParams?.path === 'cnt_ord_call') {
      chartParams.type = 2;
    }
    // 全分层
    if (chartParams.type === 3) {
      setLayeredList([]);
      setReportDom('');
      queryAllLayerChartsByPath(chartParams)
        .then((res) => {
          const chartData = res?.data?.chart ?? [];
          setLayeredList(chartData);
          // 数据解读
          const report = res?.data?.report ?? null;
          setReportDom(report);
          setTimeout(() => {
            blmAnalysisModuleExposure({
              pageId: 'p_leopard_cp_00000480',
              eventId: 'e_leopard_cp_exposure_00003118',
              ext: {
                //	Lessdata:数据量少,normal:正常
                str0_e: report === '-1' ? 'Lessdata' : 'normal',
              },
            });
            setLoading(false);
          }, 500);
        })
        .catch((e) => {
          setLoading(false);
        });
    } else {
      setReportDom('');
      setDimensionsInfo(dimensions);
      setDataset([]);
      queryLayerChartsByPath(chartParams)
        .then((res) => {
          const chartData = res?.data?.chart ?? [];
          // 处理接口返回的数据
          let dimensionsData = cloneDeep(dimensionsInfo);
          // 图例显示
          if (chartData.length) {
            dimensionsData[1].displayName = chartData[0].baseDate;
            dimensionsData[2].displayName = chartData[0].expDate;
          }
          setDimensionsInfo(dimensionsData);
          let source = [];
          chartData.forEach((item) => {
            let xaxisList = (item?.xaxis || item?.xAxis || '').split('｜');
            let xaxisNew = '';
            if (xaxisList[0] && xaxisList[1]) {
              xaxisNew = `${xaxisList[0]}\n${xaxisList[1]}`;
            } else if (xaxisList[0]) {
              xaxisNew = xaxisList[0];
            }
            source.push({
              cate: xaxisNew,
              baseValue: item.baseValue ?? 0,
              expValue: item.expValue ?? 0,
              rate: item.rate ?? 0,
            });
          });
          setDataset(source);
          // 数据解读
          const report = res?.data?.report ?? null;
          setReportDom(report);
          setTimeout(() => {
            blmAnalysisModuleExposure({
              pageId: 'p_leopard_cp_00000480',
              eventId: 'e_leopard_cp_exposure_00003118',
              ext: {
                //	Lessdata:数据量少,normal:正常
                str0_e: report === '-1' ? 'Lessdata' : 'normal',
              },
            });
            setLoading(false);
          }, 500);
        })
        .catch((e) => {
          setLoading(false);
        });
    }
  };

  // 路径点击事件
  const pathClick = (item) => {
    // 路径切换 - 点击
    blmAnalysisModuleClick({
      pageId: 'p_leopard_cp_00000480',
      eventId: 'e_leopard_cp_click_00003044',
      ext: {
        str0_e: item.pathName,
      },
    });
    setShowChartInfo(item);
    setSelectKey(item.key);
  };

  // 图表分层状态变化
  const chartTypeChange = (val) => {
    // 分层按钮 - 点击
    const optionsLaryName = optionsLary.find((i) => {
      if (i.value === val) {
        return i;
      }
    })?.label;
    blmAnalysisModuleClick({
      pageId: 'p_leopard_cp_00000480',
      eventId: 'e_leopard_cp_click_00003046',
      ext: {
        str0_e: optionsLaryName ?? '',
        str1_e: showChartInfo?.pathName ?? '',
      },
    });
    setChartType(val);
  };

  // 展开/收起
  const retractTypeChange = () => {
    setIsRetract(!isRetract);
  };

  // 抽屉open状态变化
  useEffect(() => {
    if (props.open && props.baseInfo) {
      const { bastTime, currentTime } = props.baseInfo;
      let baseInfo = cloneDeep(props.baseInfo);
      if (bastTime && currentTime) {
        // 将时间戳转换为 dayjs 对象
        const bastDate = dayjs(bastTime);
        const currentDate = dayjs(currentTime);
        // 计算天数差
        const daysDifferenceCopy = currentDate.diff(bastDate, 'day');
        setDaysDifference(daysDifferenceCopy);
        // 格式化日期为 'YYYY/MM/DD' 格式
        baseInfo.bastTime = bastDate.format('YYYY/MM/DD');
        baseInfo.currentTime = currentDate.format('YYYY/MM/DD');
      }
      setOneInfo(baseInfo);
      setAdcode(props.cityInfo[0]);
    }
    // setDrawerKey(Date.now());
    // 抽屉打开
    setOpen(props.open);

    if (props.open) {
      // 归因报告详情页面 - 曝光
      blmAnalysisModuleExposure({
        pageId: 'p_leopard_cp_00000480',
        eventId: 'e_leopard_cp_exposure_00003040',
      });
    }
  }, [props.open]);

  useEffect(() => {
    if (open) {
      if (props?.baseInfo?.pathList) {
        // 获取对应路径分析对应名称与描述
        let arr = [];
        const pathList = props.baseInfo?.pathList ?? [];
        pathList.forEach((item, index) => {
          if (pathCollection[item.path]) {
            const { pathName, describe } = pathCollection[item.path];
            arr.push({
              key: index,
              path: item.path,
              atten: item.atten,
              pathName,
              describe,
            });
          }
        });
        setDataList(arr);
        // 默认展示第一个路径数据
        pathClick(arr[0]);
        setChartType(2);
      }
    }
  }, [open, adcode]);

  // 分层状态变化/选择路径变化
  useEffect(() => {
    if (open && showChartInfo.path && chartType) {
      getLayeredData(showChartInfo.path, chartType);
    }
  }, [showChartInfo.path, chartType, open, props.layerType]);

  return (
    <Drawer
      title="归因报告详情"
      width={1090}
      className={'drawerBox'}
      open={open}
      onClose={onClose}
    >
      <div className={'reportContent'}>
        {/* 头部内容 */}
        <div className={'topBaseInfo'}>
          <div className={'topBaseInfo_title'}>完单量</div>
          <div className={'topBaseInfo_content'}>
            <div className={'topBaseInfo_content_info'}>
              <div>
                <div className={'size22'}>{oneInfo?.baseNum ?? 0}</div>
                <div style={{ marginTop: '4px' }}>对照日期</div>
                <div className="size14">{oneInfo?.bastTime}</div>
              </div>
              <div className={'topBaseInfo_content_info_arrow'}>
                <RouteImg></RouteImg>
                <div>
                  经过天数<span>{daysDifference}</span>天
                </div>
              </div>
              <div>
                <div className={'size22'}>{oneInfo?.currentNum ?? 0}</div>
                <div style={{ marginTop: '4px' }}>归因日期</div>
                <div className="size14">{oneInfo?.currentTime}</div>
              </div>
            </div>

            <div className={'topBaseInfo_content_proportion'}>
              <div>
                {/* 绿色下降 红色上升*/}
                {oneInfo?.diffValue > 0 ? (
                  <ArrowRed className="arrow"></ArrowRed>
                ) : (
                  <ArrowGreen className="arrow arrowGreen"></ArrowGreen>
                )}
                <div className={'size22'} style={{ display: 'inline-block' }}>
                  {Math.abs(oneInfo?.diffValue ?? 0)} ({oneInfo?.rate ?? '0'}%)
                </div>
              </div>
              <div> 变化数值（变化比例）</div>
            </div>
          </div>

          <div className={'size14 reportText'}>
            <span className={'data-explain_title'}>核心目标：</span>
            {oneInfo.report ? (
              <Tooltip
                placement={'bottomLeft'}
                title={() => {
                  let item = '';
                  if (oneInfo.report === '-1') {
                    item = `${getTime(8)}或${getTime(
                      1,
                    )}出车司机数过少,暂无结论`;
                  } else {
                    item = oneInfo.report;
                  }
                  return showDecodeTooltip(item);
                }}
                color={'#FFF'}
                overlayClassName={'tooltipCard'}
                overlayInnerStyle={overlayInnerCss}
              >
                <div className={'ellipsis data-explain_contents'}>
                  {oneInfo.report === '-1'
                    ? showDecode(
                        `${getTime(8)}或${getTime(1)}出车司机数过少,暂无结论`,
                      )
                    : showDecode(oneInfo.report)}
                </div>
              </Tooltip>
            ) : null}
          </div>
        </div>

        {/*  */}
        <div className={'analysisPath'}>
          <div className={'analysisPath_title'}>分析路径</div>

          <div className={'analysisPath_box'}>
            <div className={'analysisPath_left'}>
              {dataList.map((i) => {
                return (
                  <div
                    key={i.key}
                    className={'analysisPath_left_box'}
                    onClick={() => pathClick(i)}
                    style={{
                      backgroundColor: i.key === selectKey ? '#EEEEF0' : '#fff',
                    }}
                  >
                    <div className={'analysisPath_left_box_label'}>
                      <IsShowToolTip
                        item={i?.pathName}
                        otherVal={i?.atten}
                        widthNum={i?.atten ? '100px' : '120px'}
                      ></IsShowToolTip>
                    </div>
                    {i?.atten ? (
                      <ExclamationCircleFilled
                        className={'analysisPath_left_box_icon'}
                        style={{
                          color: oneInfo?.diffValue > 0 ? '#E34D59' : '#00a870',
                        }}
                      />
                    ) : null}
                  </div>
                );
              })}
            </div>

            {/*右侧盒子*/}
            <div className={'analysisPath_rightBox'}>
              {!isRetract ? (
                <div className={'analysisPath_right'}>
                  <div className={'analysisPath_right_top'}>
                    <div className={'analysisPath_right_top_text'}>
                      <span
                        className={'size14'}
                        style={{ marginRight: '12px' }}
                      >
                        {showChartInfo.pathName}
                      </span>
                      <Tooltip placement="top" title={showChartInfo.describe}>
                        <Describe />
                      </Tooltip>
                    </div>
                    {showChartInfo.path === 'cnt_ord_call' ? null : (
                      <Segmented
                        value={chartType}
                        options={optionsLary}
                        onChange={(val) => chartTypeChange(val)}
                        style={{ float: 'right' }}
                      />
                    )}
                  </div>

                  <Spin
                    indicator={
                      <LoadingOutlined style={{ fontSize: 54 }} spin />
                    }
                    spinning={loading}
                  >
                    <div>
                      {chartType === 3 &&
                      showChartInfo.path !== 'cnt_ord_call' ? (
                        <FullyLayered
                          layeredList={layeredList}
                          showChartInfo={showChartInfo}
                          loading={loading}
                        ></FullyLayered>
                      ) : (
                        <div className="chartContent"> {chartInit()} </div>
                      )}
                    </div>
                  </Spin>
                </div>
              ) : null}

              {/*数据解读*/}
              <div
                className={'analysisPath_decode'}
                style={{ overflow: 'auto' }}
              >
                <Button
                  type="link"
                  onClick={() => retractTypeChange()}
                  style={{ float: 'right', top: '0', position: 'sticky' }}
                >
                  {isRetract ? '收起' : '展开'}
                </Button>
                <div className="result">
                  {/*<span className={'data-explain_title'}>数据解读：</span>*/}
                  {reportDom === '-1'
                    ? showDecode(`<span style="{{font-size: 14px; font-weight: 500}}">数据解读:</span>
                                          ${getTime(8)}或${getTime(
                                            1,
                                          )}出车司机数过少,暂无结论`)
                    : showDecode(reportDom)}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Drawer>
  );
};

export default ReportDetails;
