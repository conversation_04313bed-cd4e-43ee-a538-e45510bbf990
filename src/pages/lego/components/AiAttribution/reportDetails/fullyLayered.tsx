// 全分层
import React, { useEffect, useState } from 'react';
import { Popover, Table as BLMTable } from '@blmcp/ui';
import type { TableProps } from '@blmcp/ui';
import './index.less';

interface DataType {
  layerName: string;
  morning: string;
  midday: string;
  evening: string;
  flat: string;
  all: string;
  tags: string[];
}

const FullyLayered = (props) => {
  const [dataList, setDataList] = useState([]);
  const [showSymbol, setShowSymbol] = useState(false);

  useEffect(() => {
    setDataList(props.layeredList);
  }, [props.layeredList]);

  useEffect(() => {
    if (props?.showChartInfo?.path === 'cancel_rate') {
      setShowSymbol(true);
    } else {
      setShowSymbol(false);
    }
  }, [props.showChartInfo?.path]);

  const getContentText = (item) => {
    // 格式化数据
    const parts = (item || '').split('|');
    const info = {
      rate: parts[2] ?? '',
      base: parts[1] ?? '',
      thisPeriod: parts[0] ?? '',
    };
    return info;
  };

  // 气泡要显示的内容
  const popoverContent = (itemInfo) => {
    return (
      <div>
        {itemInfo.rate}%
        <br />
        {showSymbol ? (
          <span>
            ({itemInfo.base}% - {itemInfo.thisPeriod}%)
          </span>
        ) : (
          <span>
            ({itemInfo.base} - {itemInfo.thisPeriod})
          </span>
        )}
      </div>
    );
  };
  const columnTitle = (text, times) => {
    return (
      <div>
        {text} <br /> {times}
      </div>
    );
  };

  // 鼠标移入后显示的内容
  const handleCellHover = (itemInfo) => {
    const content = (
      <div style={{ minWidth: '140px', color: 'rgba(0, 0, 0, 0.6)' }}>
        <div
          style={{
            fontSize: '14px',
            fontWeight: '500',
            lineHeight: '22px',
            color: 'rgba(0, 0, 0, 0.9)',
          }}
        >
          {itemInfo?.recode?.layerName}/{itemInfo.titleText}
        </div>
        变化率
        <span style={{ float: 'right', color: 'rgba(0, 0, 0, 0.9)' }}>
          {itemInfo?.rate} <span style={{ color: 'rgba(0,0,0,0.6)' }}>%</span>
        </span>
        <br />
        基期数据
        <span style={{ float: 'right', color: 'rgba(0, 0, 0, 0.9)' }}>
          {itemInfo?.base}
          {showSymbol ? (
            <span style={{ color: 'rgba(0,0,0,0.6)' }}>%</span>
          ) : null}
        </span>
        <br />
        本期数据
        <span style={{ float: 'right', color: 'rgba(0, 0, 0, 0.9)' }}>
          {itemInfo?.thisPeriod}
          {showSymbol ? (
            <span style={{ color: 'rgba(0,0,0,0.6)' }}>%</span>
          ) : null}
        </span>
      </div>
    );
    return content;
  };

  const columns: TableProps<DataType>['columns'] = [
    {
      title: '司机分层/时段分层',
      dataIndex: 'layerName',
    },
    {
      title: columnTitle('早高峰', '07:00-09:00'),
      dataIndex: 'morning',
      render: (text, recode) => {
        const { rate, base, thisPeriod } = getContentText(text);
        const itemInfo = {
          recode,
          rate,
          base,
          thisPeriod,
          titleText: '早高峰 07:00-09:00',
        };
        const content = handleCellHover(itemInfo);
        return <Popover content={content}>{popoverContent(itemInfo)}</Popover>;
      },
    },
    {
      title: columnTitle('午高峰', '12:00-14:00'),
      dataIndex: 'midday',
      render: (text, recode) => {
        const { rate, base, thisPeriod } = getContentText(text);
        const itemInfo = {
          recode,
          rate,
          base,
          thisPeriod,
          titleText: '午高峰12:00-14:00',
        };
        const content = handleCellHover(itemInfo);
        return <Popover content={content}>{popoverContent(itemInfo)}</Popover>;
      },
    },
    {
      title: columnTitle('晚高峰', '17:00-19:00'),
      dataIndex: 'evening',
      render: (text, recode) => {
        const { rate, base, thisPeriod } = getContentText(text);
        const itemInfo = {
          recode,
          rate,
          base,
          thisPeriod,
          titleText: '晚高峰17:00-19:00',
        };
        const content = handleCellHover(itemInfo);
        return <Popover content={content}>{popoverContent(itemInfo)}</Popover>;
      },
    },
    {
      title: columnTitle('夜高峰', '21:00-23:00'),
      dataIndex: 'night',
      render: (text, recode) => {
        const { rate, base, thisPeriod } = getContentText(text);
        const itemInfo = {
          recode,
          rate,
          base,
          thisPeriod,
          titleText: '夜高峰 21:00-23:00',
        };
        const content = handleCellHover(itemInfo);
        return <Popover content={content}>{popoverContent(itemInfo)}</Popover>;
      },
    },
    {
      title: columnTitle('平峰', '其他时段'),
      dataIndex: 'flat',
      render: (text, recode) => {
        const { rate, base, thisPeriod } = getContentText(text);
        const itemInfo = {
          recode,
          rate,
          base,
          thisPeriod,
          titleText: '平峰 其他时段',
        };
        const content = handleCellHover(itemInfo);
        return <Popover content={content}>{popoverContent(itemInfo)}</Popover>;
      },
    },
    {
      title: '汇总',
      dataIndex: 'all',
      render: (text, recode) => {
        const { rate, base, thisPeriod } = getContentText(text);
        const itemInfo = {
          recode,
          rate,
          base,
          thisPeriod,
          titleText: '汇总',
        };
        const content = handleCellHover(itemInfo);
        return <Popover content={content}>{popoverContent(itemInfo)}</Popover>;
      },
    },
  ];

  return (
    <div className={'fully'}>
      <div className={'fully_title size12'}>
        值：<span>变化率（基期数据-本期数据）</span>
      </div>
      <div style={{ maxHeight: '260px', overflow: 'auto' }}>
        <BLMTable
          bordered
          columns={columns}
          dataSource={dataList}
          pagination={false}
        />
      </div>
      <div className={'fully_text size12'}>
        注：汇总的数值不为所属行或列数值相加的总和，而是表明对应分层整体的数据表现。
      </div>
    </div>
  );
};
export default FullyLayered;
