.qbi-lego-cityconfig-modal-body .ant-modal-content {
  // width: 600px !important;
  // .ant-btn-default,
  // .ant-btn-primary {
  //   width: 80px !important;
  // }
  .ant-modal-body {
    padding-top: 8px !important;
    border-width: 0.5px 0px 0.5px 0px;
    border-style: solid;
    border-color: #e7e8eb;
  }
  .ant-form-item-label {
    label {
      font-size: 13px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: 0em;
      color: rgba(0, 0, 0, 0.6);
    }
  }
  .ant-form-item-control-input {
    .ant-form-item-control-input-content {
      .ant-select {
        .ant-select-selector {
          background: #fff !important;
          // border: 1px solid #e7e8eb !important;
        }
      }
    }
  }

  .ant-form-item-control-input {
    .ant-form-item-control-input-content {
      .ant-select {
        .ant-select-selector:hover {
          //   background: #fff !important;
          // border: 1px solid #e7e8eb !important;
        }
      }
    }
  }
  .layer {
    width: 502px;
    margin-left: 73px;
    margin-bottom: 16px;
    input::-webkit-outer-spin-button,
    input::-webkit-inner-spin-button {
      -webkit-appearance: none;
    }
    .ant-form-item .ant-form-item-label >label::after{
      margin-inline-end: 0px;
    }
    input {
      width: 140px;
      background: none;
      // border: 1px solid #e7e8eb !important;
    }
    input:hover {
      background: none;
    }
    .ant-form {
      margin-bottom: 8px;
    }
    .ant-form-item-control-input {
      //   width: 140px;
    }
    :where(.css-iksrmj).ant-form-item .ant-form-item-label > label::after {
      margin-inline-end: 0px;
    }
    .min-input {
      width: 52px;
    }
    .auto-input {
      // width: -webkit-fill-available;
      width: 100%;
    }
    .formLayer1 {
      flex-wrap: nowrap;
      > div:nth-child(2) {
        flex: 1;
      }
    }
    .formLayer4 {
      flex-wrap: nowrap;
      > div:nth-child(2) {
        flex: 1;
      }
    }
    .formLayer2 {
      flex-wrap: nowrap;
      > div:nth-child(2) {
        flex: 1;
      }
      > div:nth-child(3) {
        flex: 1;
      }

      .ant-form-item-control-input-content {
        display: flex;
        input {
          flex: 1;
        }
      }
      > div:nth-child(2),
      > div:nth-child(3) {
        .ant-form-item-explain-error {
          max-width: 60px;
        }
      }
    }
    .formLayer3 {
      flex-wrap: nowrap;
      > div:nth-child(2) {
        flex: 1;
      }
      > div:nth-child(3) {
        flex: 1;
      }
      .ant-form-item-control-input-content {
        display: flex;
        input {
          flex: 1;
        }
      }
      > div:nth-child(2),
      > div:nth-child(3) {
        .ant-form-item-explain-error {
          max-width: 60px;
        }
      }
      // .ant-form-item-explain-error {
      //   max-width: 60px;
      // }
    }
  }
}
.ok-modal {
  border: none !important;
}
