import React, { useRef, useEffect, useState } from 'react';
import { Tag, Button, Divider, Typography } from '@blmcp/ui';
import './cityCards.less';
import dayjs from 'dayjs';
import DelImg from './img/del.svg';
import EditImg from './img/edit.svg';

const { Text } = Typography;
const levelOptions = [
  {
    value: 0,
    label: '青铜',
  },
  {
    value: 1,
    label: '白银',
  },
  {
    value: 2,
    label: '黄金',
  },
  {
    value: 3,
    label: '铂金',
  },
  {
    value: 4,
    label: '钻石',
  },
];
const levelMap = {
  '0': '青铜',
  '1': '白银',
  '2': '黄金',
  '3': '铂金',
  '4': '钻石',
};
const typeMap = {
  '1': '近28天周均完单',
  '2': '服务分分数',
  '3': '服务分等级',
};

const CityCards = (props) => {
  const [info, setInfo] = useState({
    tomorrowEnable: 1, // 是否明日生效
    tenantId: 1,
    adcode: '110100',
    cityName: '北京',
    id: 1,
    gmtCreate: '6月27日', // 创建时间
    modifiedUser: 'test', // 修改人
    detail: [
      {
        id: 1,
        value: 150,
        layer_name: '核心司机周完单大于',
      },
      {
        id: 2,
        value: 150,
        layer_name: '核心司机周完单大于',
      },
      {
        id: 3,
        value: 150,
        layer_name: '核心司机周完单大于',
      },
      {
        id: 4,
        value: 150,
        layer_name: '核心司机周完单大于',
      },
    ],
  });
  const [cityName, setCityName] = useState('');
  const [updateTime, setUpdateTime] = useState('');
  const [layers, setLayers] = useState([]);
  useState(() => {
    if (props.cityMap) {
      const data = props.data;

      // 设置城市
      const cityMap = props.cityMap;
      const cityIn = cityMap?.filter((o) => {
        return data.adcode === o.code;
      });
      // 设置修改时间

      const timeObj = dayjs(data.gmtModified);

      setUpdateTime(
        `${timeObj.year()}年${timeObj.month() + 1}月${timeObj.date()}日`,
      );
      // 设置分层
      const type = data.type;
      const detail = data.detail;
      const typeNumber = data.detail.length;
      let arr = [];
      let typeName = '';
      switch (type) {
        case 1:
          typeName = '周完单';
          break;
        case 2:
          typeName = '服务分';
          break;
        case 3:
          typeName = '服务分等级为';
          break;
      }
      if (type === 1 || type === 2) {
        if (typeNumber === 2) {
          arr.push(
            `${detail[0].layer_name}${typeName}小于等于<span>${detail[0].value}</span>`,
          );
          arr.push(
            `${detail[1].layer_name}${typeName}大于<span>${detail[1].value}</span>`,
          );
        }
        if (typeNumber === 3) {
          arr.push(
            `${detail[0].layer_name}${typeName}小于等于<span>${detail[0].value}</span>`,
          );
          arr.push(
            `${detail[1].layer_name}${typeName}大于<span>${detail[0].value}</span>小于等于<span>${detail[1].value}</span>`,
          );
          arr.push(
            `${detail[2].layer_name}${typeName}大于<span>${detail[2].value}</span>`,
          );
        }
        if (typeNumber === 4) {
          arr.push(
            `${detail[0].layer_name}${typeName}小于等于<span>${detail[0].value}</span>`,
          );
          arr.push(
            `${detail[1].layer_name}${typeName}大于<span>${detail[0].value}</span>小于等于<span>${detail[1].value}</span>`,
          );
          arr.push(
            `${detail[2].layer_name}${typeName}大于<span>${detail[1].value}</span>小于等于<span>${detail[2].value}</span>`,
          );
          arr.push(
            `${detail[3].layer_name}${typeName}大于<span>${detail[3].value}</span>`,
          );
        }
      }
      if (type === 3) {
        detail.forEach((item) => {
          arr.push(
            `${item.layer_name}服务分等级为<span>${item.value
              .split(',')
              .map((o) => {
                return levelMap[o];
              })
              .join('、')}</span>`,
          );
        });
      }
      setLayers(arr);
      setCityName(cityIn[0]?.name);

      setInfo(data);
    }
  }, [props.data, props.cityMap]);

  const del = () => {
    props.onDelete(info.id);
  };
  const edit = () => {
    props.onEdit(info);
  };

  return (
    <div className="cityCard">
      <div style={{ marginTop: '8px' }} className="title-cityCard">
        <div className="size16 cityName">{cityName}</div>
        <div className="tag-box">
          {info.tomorrowEnable === 1 ? (
            <Tag bordered={false} color="processing" className="tag">
              明日生效
            </Tag>
          ) : (
            ''
          )}
        </div>
        <div className="times size12">
          {/* {`${info.modifiedUser} 修改于${updateTime}`} */}
          <Text
            ellipsis={{
              tooltip: `${info.modifiedUser} 修改于${updateTime}`,
            }}
          >
            {`${info.modifiedUser} 修改于${updateTime}`}
          </Text>
        </div>
      </div>
      <div className={'fenceng'}>
        <div className="title">{typeMap[info.type + '']}</div>
        {/* {info?.detail.map((i) => {
          return (
            <div className="content size12" key={i.id}>
              {i.layer_name} {i.value}
            </div>
          );
        })} */}
        <div className="layers-box">
          {layers.map((item, idx) => {
            return (
              <div className="content size12" key={idx}>
                <Text
                  style={{
                    width: 153,
                  }}
                  ellipsis={{
                    tooltip: item
                      .replaceAll('<span>', '')
                      .replaceAll('</span>', ''),
                  }}
                >
                  <span
                    className="font-w"
                    dangerouslySetInnerHTML={{ __html: item }}
                  ></span>
                </Text>
              </div>
            );
          })}
        </div>
      </div>

      <div className="btn-box">
        <Button type="link" onClick={del} className={'btn'}>
          <DelImg className={'img'}></DelImg> 删除
        </Button>
        <Divider type="vertical" />
        <Button type="link" onClick={edit} className={'btn'}>
          <EditImg className={'img'}></EditImg> 编辑
        </Button>
      </div>
    </div>
  );
};

export default CityCards;
