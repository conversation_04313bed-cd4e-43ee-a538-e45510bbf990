import React, { useEffect, useRef, useState } from 'react';
import { <PERSON><PERSON>, Drawer, message, Modal, Pagination, Spin } from '@blmcp/ui';
import {
  BLMCityCascader,
  BLMOrgBtCity,
} from '@blmcp/peento-businessComponents';
import { LoadingOutlined } from '@ant-design/icons';
// import { Empty } from '@blmcp/ui';

import request from '@/utils/request';
import { deleteRuleConfig, queryRuleConfigList } from '../../api/aiAttribution';
import CityConfig from './CityConfig';
import CityCards from './cityCards';
import './layerconfig.less';
import TipsIcon from './img/tips.svg';
import DelIcon from './img/BLM-ic-caution.svg';

const DeleteTitle = () => {
  return (
    <div className="delete-title">
      <DelIcon></DelIcon>
      <span>确认删除吗？</span>
    </div>
  );
};

const LayerConfig = (props) => {
  const [open, setOpen] = useState(false);
  const [cityOpen, setCityOpen] = useState(false);
  const [citysConfigData, setCitysConfigData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [deleteOpen, setDeleteOpen] = useState(false);
  const [dLoading, setDLoading] = useState(false);
  // 城市
  const [cityValue, setCityValue] = useState([]);
  const [isExpand, setIsExpand] = useState(false);

  const [cityMap, setCityMap] = useState([]);

  const [cityValueParam, setCityValueParam] = useState([]);
  // page
  const [total, setTotal] = useState(0);
  const [pageNum, setPageNum] = useState(1);
  const CityConfigKey = useRef();
  CityConfigKey.current = 1;

  //  hooks

  //  callback

  const onClose = () => {
    setOpen(false);
    setCityValue([]);
    setCityValueParam([]);
    if (props.onClose) {
      props.onClose();
    }
  };

  const onChangeCity = (value: string[]) => {
    setCityValue(value);
  };

  // 搜索按钮
  const getData = async (pageNum, cityValue) => {
    setLoading(true);
    const res = await queryRuleConfigList({
      adcode: cityValue,
      pageSize: 9,
      pageNum: pageNum,
    });
    if (res.code === 1) {
      setTotal(res.data.totalNum);
      setCitysConfigData(res.data.items);
    } else {
      message.error(res.msg);
    }
    setLoading(false);
  };

  const pageChange = (page) => {
    setPageNum(page);
    getData(page, cityValueParam);
  };
  const [deleteId, setDeleteId] = useState();
  const onDelete = (id) => {
    setDeleteId(id);
    setDeleteOpen(true);
  };
  const [editInfo, setEditInfo] = useState();
  const [isEdit, setIsEdit] = useState(false);
  const onEdit = (info) => {
    setEditInfo(info);
    setIsEdit(true);
    setCityOpen(true);
    CityConfigKey.current += 1;
  };
  const cityConfigClick = () => {
    setCityOpen(true);
    CityConfigKey.current += 1;
    setIsEdit(false);
  };

  const cityConfigCancel = () => {
    setCityOpen(false);
    setIsEdit(false);
  };

  useEffect(() => {
    setOpen(props.open);
    if (props.open) {
      getData(1, []);
    }
    setPageNum(1);
  }, [props.open]);
  // useEffect(() => {
  //   getData(1, []);
  //   setPageNum(1);
  // }, []);

  return (
    <>
      {' '}
      <Drawer
        title="分层条件配置"
        open={open}
        width={'1080px'}
        className={'layerConfigDrawerCss'}
        onClose={onClose}
        // maskClosable={false}
      >
        <div>
          <div className="header">
            <div className="city">
              <BLMOrgBtCity
                value={cityValue}
                style={{ width: 228 }}
                addonbefore={''}
                placeholder="请选择城市"
                structureType={2} // 省市级联
                kind={'authOpen'}
                multiple={true}
                allowClear={true}
                onChange={(value) => {
                  value = value ?? [];
                  onChangeCity(value);
                }}
                getOptions={(options) => {
                  let arr = [];
                  options.forEach((item) => {
                    arr = arr.concat(item.childList);
                  });
                  setCityMap(arr);
                }}
                old={
                  <BLMCityCascader
                    value={cityValue}
                    style={{ width: 228 }}
                    request={request}
                    requestParams={{ filterType: 1 }}
                    placeholder="请选择城市"
                    multiple={true}
                    allowClear={true}
                    onChange={(value) => onChangeCity(value)}
                    getOptions={(options) => {
                      let arr = [];
                      options.forEach((item) => {
                        arr = arr.concat(item.childDictList);
                      });
                      setCityMap(arr);
                    }}
                  />
                }
              ></BLMOrgBtCity>
            </div>
            <Button
              onClick={() => {
                setCityValue([]);
                setCityValueParam([]);
                getData(1, []);
                setPageNum(1);
              }}
            >
              重置
            </Button>
            <Button
              type="primary"
              onClick={() => {
                getData(1, cityValue);
                setCityValueParam(cityValue);
                setPageNum(1);
              }}
            >
              搜索
            </Button>
            <span className="city-config" onClick={cityConfigClick}>
              配置城市
            </span>
          </div>
          <div
            className="tips-content"
            style={{
              height: `${isExpand ? 174 : 84}px`,
            }}
          >
            <div className="left">
              <TipsIcon></TipsIcon>
            </div>
            <div className="right">
              <p style={{ textWrap: 'nowrap' }}>
                本司机分层功能作用于智能归因的分析逻辑中，会根据配置的司机分层来对各分层的司机群体进行分析，输出各司机群体的数据表现，便于您分析数据定位问题。
              </p>
              <p>
                1.默认分层逻辑：当未自定义城市分层逻辑时使用默认分层逻辑，根据每个城市的服务分进行司机分层；核心司机：铂金及以上司机；非核心司机：铂金以下司机（不含铂金司机）。
                {!isExpand ? (
                  <span
                    className="button-expand"
                    onClick={() => {
                      setIsExpand(true);
                    }}
                  >
                    展开
                  </span>
                ) : null}
              </p>
              <p>2.分层生效时间：每次修改后会在次日生效。</p>
              <p>
                3.分层修改时间：在每日20:00-次日06:00不能修改分层，在这段时间内会进行过去一天的数据计算，请您合理安排配置时间。
              </p>
              <p>
                4.仅可查询和编辑有完整权限城市的配置，如有需要请联系管理员开通。
                {isExpand ? (
                  <span
                    className="button-expand"
                    onClick={() => {
                      setIsExpand(false);
                    }}
                  >
                    收起
                  </span>
                ) : null}
              </p>
            </div>
          </div>
          <CityConfig
            key={CityConfigKey.current}
            open={cityOpen}
            onCancel={cityConfigCancel}
            editInfo={editInfo}
            isEdit={isEdit}
            onUpdate={() => {
              getData(1, cityValueParam);
              setPageNum(1);
            }}
            onAdd={() => {
              getData(1, []);
              setCityValue([]);
              setCityValueParam([]);
              setPageNum(1);
            }}
          ></CityConfig>
          <div className="card-content">
            <Spin
              spinning={loading}
              indicator={<LoadingOutlined style={{ fontSize: 54 }} spin />}
            >
              <div className="cards-box">
                {citysConfigData.length === 0 ? (
                  <div className="no-data">
                    <div className="no-data-icon"></div>
                    <p>暂无数据</p>
                  </div>
                ) : (
                  citysConfigData.map((o) => {
                    return (
                      <CityCards
                        cityMap={cityMap}
                        data={o}
                        key={`${JSON.stringify(o)}${
                          cityMap.length === 0 ? 'a' : 'b'
                        }`}
                        onDelete={onDelete}
                        onEdit={onEdit}
                      ></CityCards>
                    );
                  })
                )}
              </div>
            </Spin>
            {citysConfigData.length !== 0 ? (
              <Pagination
                total={total}
                pageSize={9}
                current={pageNum}
                pageSizeOptions={[9]}
                showSizeChanger={false}
                onChange={pageChange}
                showTotal={(total) => `共 ${total} 条`}
              ></Pagination>
            ) : null}
          </div>
        </div>
      </Drawer>
      <Modal
        classNames={{
          body: 'delete-modal',
        }}
        width={400}
        title={<DeleteTitle></DeleteTitle>}
        open={deleteOpen}
        onCancel={() => {
          setDeleteOpen(false);
        }}
        onOk={async () => {
          if (dLoading) return;
          setDLoading(true);
          const res = await deleteRuleConfig({
            id: deleteId,
          }).catch(() => {});
          if (res.code === 1) {
            setDLoading(false);
            setDeleteOpen(false);
            message.success('删除成功');
            getData(1, cityValueParam);
            setPageNum(1);
          }
          setDLoading(false);
          setDeleteOpen(false);
        }}
      >
        <br></br>
        <p> 删除后该城市会使用默认的分层配置</p>
        <br></br>
      </Modal>
    </>
  );
};

export default LayerConfig;
