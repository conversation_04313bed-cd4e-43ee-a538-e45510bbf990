import React, { useRef, useEffect, useState } from 'react';
import { Modal, Form, Select, Input, message, Spin } from '@blmcp/ui';
import {
  BLMCityCascader,
  BLMOrgBtCity,
  BLMOriAndCategoryContainer,
} from '@blmcp/peento-businessComponents';
import './CityConfig.less';
import _ from 'lodash';
import { LoadingOutlined } from '@ant-design/icons';
import request from '@/utils/request';

import {
  addRuleConfig,
  addRuleConfigExistCheck,
  updateRuleConfig,
  bosAddRuleConfigExistCheck,
} from '../../api/aiAttribution';

const typeOption = [
  // 司机分层枚举
  {
    value: 1,
    label: '近28天周均完单',
    explain: '近28天内司机确认费用的订单量/28*7，是每周平均完单量',
  },
  {
    value: 2,
    label: '服务分分数',
    explain: '司机当日的服务分分数',
  },
  {
    value: 3,
    label: '服务分等级',
    explain: '司机当日的服务分等级',
  },
];
const typeNumberOption = [
  // 分层个数枚举
  {
    value: 2,
    label: 2,
  },
  {
    value: 3,
    label: 3,
  },
  {
    value: 4,
    label: 4,
  },
];
// 服务分等级枚举
const levelOptions = [
  {
    value: 0,
    label: '青铜',
  },
  {
    value: 1,
    label: '白银',
  },
  {
    value: 2,
    label: '黄金',
  },
  {
    value: 3,
    label: '铂金',
  },
  {
    value: 4,
    label: '钻石',
  },
];

const levelArray = [0, 1, 2, 3, 4];

const CityConfig = (props) => {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [okOpen, setOkOpen] = useState(false);
  const [cityName, setCityName] = useState([]);
  const [cityMap, setCityMap] = useState({});
  const [addParams, setAddParams] = useState({});
  const [loadingFirst, setLoadingFirst] = useState(false);
  const [loadingSecond, setLoadingSecond] = useState(false);

  const [formLayer1] = Form.useForm();
  const [formLayer2] = Form.useForm();
  const [formLayer3] = Form.useForm();
  const [formLayer4] = Form.useForm();

  const [isShowLayer2, setIsShowLayer2] = useState(false);
  const [isShowLayer3, setIsShowLayer3] = useState(false);
  const [isShowLevel, setIsShowLevel] = useState(false);
  const [labelLayer4, setLabelLayer4] = useState('分层二');
  const [typeName, setTypeName] = useState('服务分');

  // 编辑态
  const [isEdit, setIsEdit] = useState(false);
  const [cityDisabled, isCityDisabled] = useState(false);

  // 城市
  // const [cityValue, setCityValue] = useState([]);

  const Item = Form.Item;

  //   hooks
  useEffect(() => {
    setOpen(props.open);
    isCityDisabled(false);
  }, [props.open]);
  // 城市选项变更事件

  //   callback

  // 确认提交按钮
  const clearLayer = () => {
    if (formLayer1) {
      formLayer1.resetFields();
    }
    if (formLayer2) {
      formLayer2.resetFields();
    }
    if (formLayer3) {
      formLayer3.resetFields();
    }
    if (formLayer4) {
      formLayer4.resetFields();
    }
  };
  const onCancel = () => {
    if (props.onCancel) {
      props.onCancel();
      form.resetFields();
      clearLayer();
      isCityDisabled(false);
      setIsEdit(false);
      setIsShowLayer2(false);
      setIsShowLayer3(false);
    }
  };

  // 二次确认按钮
  const onceOk = async () => {
    if (loadingSecond) return;
    setLoadingSecond(true);
    const addInfo = await addRuleConfig(addParams);
    if (addInfo.code === 1) {
      message.success('添加成功');
    } else {
      message.error(addInfo.msg);
    }

    setOpen(false);
    setOkOpen(false);
    onCancel();
    if (props.onAdd) {
      props.onAdd();
    }

    setLoadingSecond(false);
  };

  const onOk = async () => {
    if (loadingFirst) return;
    // let check = await form.validateFields();
    // let check1 = { errorFields: [] };
    // let check2 = { errorFields: [] };
    // let check3 = { errorFields: [] };
    // let check4 = { errorFields: [] };

    // if (formLayer1) {
    //   check1 = await formLayer1.validateFields();
    // }
    // if (formLayer2) {
    //   check2 = await formLayer2.validateFields();
    // }
    // if (formLayer3) {
    //   check3 = await formLayer3.validateFields();
    // }
    // if (formLayer4) {
    //   check4 = await formLayer4.validateFields();
    // }
    let p1 = form.validateFields();
    let p2 = Promise.resolve();
    let p3 = Promise.resolve();
    let p4 = Promise.resolve();
    let p5 = Promise.resolve();

    if (formLayer1) {
      p2 = formLayer1.validateFields();
    }
    if (formLayer2) {
      p3 = formLayer2.validateFields();
    }
    if (formLayer3) {
      p4 = formLayer3.validateFields();
    }
    if (formLayer4) {
      p5 = formLayer4.validateFields();
    }

    const res = await Promise.allSettled([p1, p2, p3, p4, p5]);

    const isValidates = res.every((item) => {
      return item.status === 'fulfilled';
    });

    setLoadingFirst(true);

    if (isValidates) {
      const formValue = form.getFieldsValue();
      const formLayer1Value = formLayer1.getFieldsValue();
      const formLayer4Value = formLayer4.getFieldsValue();
      const params = {};
      let id = 1;
      let formLayer2Value = null;
      let formLayer3Value = null;

      if (formValue.typeNumber === 3 || formValue.typeNumber === 4) {
        formLayer2Value = formLayer2.getFieldsValue();
      }
      if (formValue.typeNumber === 4) {
        formLayer3Value = formLayer3.getFieldsValue();
      }
      params.adcode = formValue.adcode;
      params.type = formValue.type;
      params.detail = [];

      let o1 = {};
      o1.id = id;
      o1.layer_name = formLayer1Value.nameLayer;
      if (formValue.type === 1 || formValue.type === 2) {
        o1.value = formLayer1Value.layerNum;
      } else {
        o1.value = formLayer1Value.layerNumLevel.join(',');
      }

      id++;
      params.detail.push(o1);

      if (formValue.typeNumber === 3 || formValue.typeNumber === 4) {
        let o2 = {};
        o2.id = id;
        o2.layer_name = formLayer2Value.nameLayer;
        if (formValue.type === 1 || formValue.type === 2) {
          o2.value = formLayer2Value.layerNum2;
        } else {
          o2.value = formLayer2Value.layerNumLevel.join(',');
        }

        id++;
        params.detail.push(o2);
      }

      if (formValue.typeNumber === 4) {
        let o3 = {};
        o3.id = id;
        o3.layer_name = formLayer3Value.nameLayer;
        if (formValue.type === 1 || formValue.type === 2) {
          o3.value = formLayer3Value.layerNum2;
        } else {
          o3.value = formLayer3Value.layerNumLevel.join(',');
        }

        id++;
        params.detail.push(o3);
      }
      let o4 = {};
      o4.id = id;
      o4.layer_name = formLayer4Value.nameLayer;
      if (formValue.type === 1 || formValue.type === 2) {
        o4.value = formLayer4Value.layerNum;
      } else {
        o4.value = formLayer4Value.layerNumLevel.join(',');
      }

      id++;
      params.detail.push(o4);
      if (isEdit) {
        // 编辑 提交
        delete params.adcode;
        params.id = props.editInfo.id;
        try {
          const res = await updateRuleConfig(params);
          setLoadingFirst(false);
          if (res.code === 1) {
            message.success('更新成功');
            setOpen(false);
            onCancel();
            if (props.onUpdate) {
              props.onUpdate();
            }
          } else {
            message.error(res.msg);
          }
        } catch (error) {
          setLoadingFirst(false);
        }
      } else {
        try {
          params.adcodeList = params.adcode;
          delete params.adcode;
          const resBos = await bosAddRuleConfigExistCheck(params);
          if (resBos.code === 1) {
            if (resBos?.data?.configExistAdcodeList?.length === 0) {
              const addInfo = await addRuleConfig(params);
              if (addInfo.code === 1) {
                message.success('添加成功');
                setOpen(false);
                setOkOpen(false);
                onCancel();
                if (props.onAdd) {
                  props.onAdd();
                }
              }
              setLoadingFirst(false);
            } else {
              let carr = cityMap.filter((o) => {
                return resBos?.data?.configExistAdcodeList?.includes(o.code);
              });
              let cityNames = [];
              carr.forEach((o) => {
                cityNames.push(o.name);
              });
              setCityName(cityNames);
              setOkOpen(true);
              setAddParams(params);
              setLoadingFirst(false);
            }
          } else {
            setLoadingFirst(false);
            message.error(resBos.msg);
          }
        } catch (err) {
          setLoadingFirst(false);
          // message.error('新增失败');
        }
      }
    } else {
      setLoadingFirst(false);
    }
  };

  const typeChange = (val) => {
    clearLayer();
    switch (val) {
      case 1:
        setTypeName('周完单');
        setIsShowLevel(false);
        break;
      case 2:
        setTypeName('服务分');
        setIsShowLevel(false);
        break;
      case 3:
        setIsShowLevel(true);
      // setTypeName('服务分等级为');
    }
  };
  const typeNumberChange = (val) => {
    clearLayer();
    switch (val) {
      case 2:
        setLabelLayer4('分层二');
        setIsShowLayer2(false);
        setIsShowLayer3(false);
        break;
      case 3:
        setLabelLayer4('分层三');
        setIsShowLayer2(true);
        setIsShowLayer3(false);
        break;
      case 4:
        setIsShowLayer2(true);
        setIsShowLayer3(true);
        setLabelLayer4('分层四');
        break;
    }
  };
  //编辑 回显
  useEffect(() => {
    if (props.isEdit) {
      let info = props.editInfo;
      let typeNumber = info.detail.length;
      let type = info.type;
      let detailArr = info.detail;
      form.setFieldValue('adcode', info.adcode);
      form.setFieldValue('type', type);
      form.setFieldValue('typeNumber', typeNumber);
      typeChange(type);
      if (type === 1 || type === 2) {
        typeNumberChange(typeNumber);
        formLayer1.setFieldValue('layerNum', detailArr[0].value);
        formLayer1.setFieldValue('nameLayer', detailArr[0].layer_name);
        if (typeNumber === 2) {
          formLayer4.setFieldValue('layerNum', detailArr[1].value);
          formLayer4.setFieldValue('nameLayer', detailArr[1].layer_name);
        }
        if (typeNumber === 3) {
          formLayer2.setFieldValue('layerNum1', detailArr[0].value);
          formLayer2.setFieldValue('layerNum2', detailArr[1].value);
          formLayer2.setFieldValue('nameLayer', detailArr[1].layer_name);

          formLayer4.setFieldValue('layerNum', detailArr[2].value);
          formLayer4.setFieldValue('nameLayer', detailArr[2].layer_name);
        }
        if (typeNumber === 4) {
          formLayer2.setFieldValue('layerNum1', detailArr[0].value);
          formLayer2.setFieldValue('layerNum2', detailArr[1].value);
          formLayer2.setFieldValue('nameLayer', detailArr[1].layer_name);

          formLayer3.setFieldValue('layerNum1', detailArr[1].value);
          formLayer3.setFieldValue('layerNum2', detailArr[2].value);
          formLayer3.setFieldValue('nameLayer', detailArr[2].layer_name);

          formLayer4.setFieldValue('layerNum', detailArr[3].value);
          formLayer4.setFieldValue('nameLayer', detailArr[3].layer_name);
        }
      }
      if (type === 3) {
        typeNumberChange(typeNumber);
        formLayer1.setFieldValue(
          'layerNumLevel',
          detailArr[0].value.split(',').map((o) => {
            return parseInt(o);
          }),
        );
        formLayer1.setFieldValue('nameLayer', detailArr[0].layer_name);
        if (typeNumber === 2) {
          formLayer4.setFieldValue(
            'layerNumLevel',
            detailArr[1].value.split(',').map((o) => {
              return parseInt(o);
            }),
          );
          formLayer4.setFieldValue('nameLayer', detailArr[1].layer_name);
        }
        if (typeNumber === 3) {
          formLayer2.setFieldValue(
            'layerNumLevel',
            detailArr[1].value.split(',').map((o) => {
              return parseInt(o);
            }),
          );
          formLayer2.setFieldValue('nameLayer', detailArr[1].layer_name);
          formLayer4.setFieldValue(
            'layerNumLevel',
            detailArr[2].value.split(',').map((o) => {
              return parseInt(o);
            }),
          );
          formLayer4.setFieldValue('nameLayer', detailArr[2].layer_name);
        }
        if (typeNumber === 4) {
          formLayer2.setFieldValue(
            'layerNumLevel',
            detailArr[1].value.split(',').map((o) => {
              return parseInt(o);
            }),
          );
          formLayer2.setFieldValue('nameLayer', detailArr[1].layer_name);
          formLayer3.setFieldValue(
            'layerNumLevel',
            detailArr[2].value.split(',').map((o) => {
              return parseInt(o);
            }),
          );
          formLayer3.setFieldValue('nameLayer', detailArr[2].layer_name);

          formLayer4.setFieldValue(
            'layerNumLevel',
            detailArr[3].value.split(',').map((o) => {
              return parseInt(o);
            }),
          );
          formLayer4.setFieldValue('nameLayer', detailArr[3].layer_name);
        }
      }
      isCityDisabled(true);
      setIsEdit(props.isEdit);
    }
  }, [
    props,
    props.isEdit,
    props.editInfo,
    form,
    formLayer1,
    formLayer2,
    formLayer3,
    formLayer4,
  ]);

  // -------------------------------------------------------------------  联动效果 ---------------------------------------------------------------------------------------------------
  const layerNumLayer1 = Form.useWatch('layerNum', formLayer1);
  const layerNum1Layer2 = Form.useWatch('layerNum1', formLayer2);
  const layerNum2Layer2 = Form.useWatch('layerNum2', formLayer2);
  const layerNum1Layer3 = Form.useWatch('layerNum1', formLayer3);
  const layerNum2Layer3 = Form.useWatch('layerNum2', formLayer3);
  const layerNumLayer4 = Form.useWatch('layerNum', formLayer4);
  const typeNumber = Form.useWatch('typeNumber', form);
  const typeValue = Form.useWatch('type', form);
  const lock = useRef();
  const TIMES = 50;

  //     ------------  服务分等级联动 ------------
  const layerNumLevelLayer1 = Form.useWatch('layerNumLevel', formLayer1);
  const layerNumLevelLayer2 = Form.useWatch('layerNumLevel', formLayer2);
  const layerNumLevelLayer3 = Form.useWatch('layerNumLevel', formLayer3);
  // const [disabledLayer2, setDisabledLayer2] = useState(true);
  // const [disabledLayer3, setDisabledLayer3] = useState(true);
  const [disabledLayer4, setDisabledLayer4] = useState(true);

  //   ------2层联动------
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }

    if (typeNumber === 2 && typeValue === 3) {
      lock.current = true;
      formLayer4.setFieldValue(
        'layerNumLevel',
        _.difference(levelArray, layerNumLevelLayer1),
      );
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNumLevelLayer1, typeNumber, formLayer4, typeValue]);

  //   ------3层联动------
  //   1层变动
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }

    if (typeNumber === 3 && typeValue === 3) {
      lock.current = true;

      let arr = formLayer2.getFieldValue('layerNumLevel');
      let arrDiff = _.difference(arr, layerNumLevelLayer1);
      formLayer2.setFieldValue('layerNumLevel', arrDiff);
      formLayer4.setFieldValue(
        'layerNumLevel',
        _.difference(levelArray, arrDiff.concat(layerNumLevelLayer1)),
      );

      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNumLevelLayer1, typeNumber, formLayer2, formLayer4, typeValue]);
  //   2层变动

  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }

    if (typeNumber === 3 && typeValue === 3) {
      lock.current = true;

      let arr = formLayer1.getFieldValue('layerNumLevel');
      let arrDiff = _.difference(arr, layerNumLevelLayer2);
      formLayer1.setFieldValue('layerNumLevel', arrDiff);
      formLayer4.setFieldValue(
        'layerNumLevel',
        _.difference(levelArray, arrDiff.concat(layerNumLevelLayer2)),
      );

      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNumLevelLayer2, typeNumber, formLayer1, formLayer4, typeValue]);

  //   ------4层联动------
  //   1层变动
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }

    if (typeNumber === 4 && typeValue === 3) {
      lock.current = true;

      let arr = formLayer2.getFieldValue('layerNumLevel');
      let arr1 = formLayer3.getFieldValue('layerNumLevel');
      let arrDiff = _.difference(arr, layerNumLevelLayer1);
      let arrDiff1 = _.difference(arr1, layerNumLevelLayer1);

      formLayer2.setFieldValue('layerNumLevel', arrDiff);
      formLayer3.setFieldValue('layerNumLevel', arrDiff1);

      formLayer4.setFieldValue(
        'layerNumLevel',
        _.difference(levelArray, arrDiff.concat(layerNumLevelLayer1, arrDiff1)),
      );

      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [
    layerNumLevelLayer1,
    typeNumber,
    formLayer2,
    formLayer3,
    formLayer4,
    typeValue,
  ]);

  //   2层变动
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }

    if (typeNumber === 4 && typeValue === 3) {
      lock.current = true;

      let arr = formLayer1.getFieldValue('layerNumLevel');
      let arr1 = formLayer3.getFieldValue('layerNumLevel');
      let arrDiff = _.difference(arr, layerNumLevelLayer2);
      let arrDiff1 = _.difference(arr1, layerNumLevelLayer2);

      formLayer1.setFieldValue('layerNumLevel', arrDiff);
      formLayer3.setFieldValue('layerNumLevel', arrDiff1);

      formLayer4.setFieldValue(
        'layerNumLevel',
        _.difference(levelArray, arrDiff.concat(layerNumLevelLayer2, arrDiff1)),
      );

      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [
    layerNumLevelLayer2,
    typeNumber,
    formLayer1,
    formLayer3,
    formLayer4,
    typeValue,
  ]);

  //   3层变动
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }

    if (typeNumber === 4 && typeValue === 3) {
      lock.current = true;

      let arr = formLayer1.getFieldValue('layerNumLevel');
      let arr1 = formLayer2.getFieldValue('layerNumLevel');
      let arrDiff = _.difference(arr, layerNumLevelLayer3);
      let arrDiff1 = _.difference(arr1, layerNumLevelLayer3);

      formLayer1.setFieldValue('layerNumLevel', arrDiff);
      formLayer2.setFieldValue('layerNumLevel', arrDiff1);

      formLayer4.setFieldValue(
        'layerNumLevel',
        _.difference(levelArray, arrDiff.concat(layerNumLevelLayer3, arrDiff1)),
      );

      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [
    layerNumLevelLayer3,
    typeNumber,
    formLayer1,
    formLayer2,
    formLayer4,
    typeValue,
  ]);

  //   ------------  周完单、服务分联动 ------------
  //   ------  2层联动 ------

  //   1联动4
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }

    if (typeNumber === 2 && (typeValue === 1 || typeValue === 2)) {
      lock.current = true;
      formLayer4.setFieldValue('layerNum', layerNumLayer1);
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNumLayer1, typeNumber, formLayer4, typeValue]);
  //   4联动1
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }

    if (typeNumber === 2 && (typeValue === 1 || typeValue === 2)) {
      lock.current = true;
      formLayer1.setFieldValue('layerNum', layerNumLayer4);
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNumLayer4, typeNumber, formLayer1, typeValue]);

  //   ------  3层联动 ------
  //   1联动2-1

  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }
    if (typeNumber === 3 && (typeValue === 1 || typeValue === 2)) {
      lock.current = true;
      formLayer2.setFieldValue('layerNum1', layerNumLayer1);
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNumLayer1, typeNumber, formLayer2, typeValue]);
  //   2-1 联动 1
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }
    if (typeNumber === 3 && (typeValue === 1 || typeValue === 2)) {
      lock.current = true;
      formLayer4.setFieldValue('layerNum', layerNum2Layer2);
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNum2Layer2, typeNumber, formLayer4, typeValue]);

  //   2-2 联动 4
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }
    if (typeNumber === 3 && (typeValue === 1 || typeValue === 2)) {
      lock.current = true;
      formLayer1.setFieldValue('layerNum', layerNum1Layer2);
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNum1Layer2, typeNumber, formLayer1, typeValue]);

  //   4 联动 2-2
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }
    if (typeNumber === 3 && (typeValue === 1 || typeValue === 2)) {
      lock.current = true;
      formLayer2.setFieldValue('layerNum2', layerNumLayer4);
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNumLayer4, typeNumber, formLayer2, typeValue]);
  //   4 联动 2-2

  // ------  4层联动 ------

  //   1联动2-1

  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }
    if (typeNumber === 4 && (typeValue === 1 || typeValue === 2)) {
      lock.current = true;
      formLayer2.setFieldValue('layerNum1', layerNumLayer1);
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNumLayer1, typeNumber, formLayer2, typeValue]);

  //   2-1 联动 1
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }
    if (typeNumber === 4 && (typeValue === 1 || typeValue === 2)) {
      lock.current = true;
      formLayer1.setFieldValue('layerNum', layerNum1Layer2);
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNum1Layer2, typeNumber, formLayer1, typeValue]);

  //   2-2 联动 3-1
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }
    if (typeNumber === 4 && (typeValue === 1 || typeValue === 2)) {
      lock.current = true;
      formLayer3.setFieldValue('layerNum1', layerNum2Layer2);
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNum2Layer2, typeNumber, formLayer3, typeValue]);

  //    3-1 联动 2-2
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }
    if (typeNumber === 4 && (typeValue === 1 || typeValue === 2)) {
      lock.current = true;
      formLayer2.setFieldValue('layerNum2', layerNum1Layer3);
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNum1Layer3, typeNumber, formLayer2, typeValue]);

  //    3-2 联动 4
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }
    if (typeNumber === 4 && (typeValue === 1 || typeValue === 2)) {
      lock.current = true;
      formLayer4.setFieldValue('layerNum', layerNum2Layer3);
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNum2Layer3, typeNumber, formLayer4, typeValue]);

  //    4 联动 3-2
  useEffect(() => {
    if (lock.current) {
      lock.current = false;
      return;
    }
    if (typeNumber === 4 && (typeValue === 1 || typeValue === 2)) {
      lock.current = true;
      formLayer3.setFieldValue('layerNum2', layerNumLayer4);
      setTimeout(() => {
        lock.current = false;
      }, TIMES);
    }
  }, [layerNumLayer4, typeNumber, formLayer3, typeValue]);

  return (
    <>
      <Modal
        classNames={{ wrapper: 'qbi-lego-cityconfig-modal-body' }}
        zIndex={2000}
        title="配置城市"
        open={open}
        onCancel={onCancel}
        onOk={onOk}
        maskClosable={false}
        destroyOnClose={true}
        width={600}
        centered={true}
      >
        <Spin
          spinning={loadingFirst}
          indicator={<LoadingOutlined style={{ fontSize: 54 }} spin />}
        >
          <div>
            <Form form={form} name="cityForm">
              <BLMOriAndCategoryContainer
                new={
                  <Item
                    name="adcode"
                    label="适用城市"
                    colon={false}
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  >
                    <BLMOrgBtCity
                      addonbefore={''}
                      placeholder="请选择城市"
                      structureType={2} // 省市级联
                      kind={'authOpen'}
                      style={{ width: '490px' }}
                      disabled={cityDisabled}
                      multiple={true}
                      allowClear={true}
                      getOptions={(options) => {
                        let arr = [];
                        options.forEach((item) => {
                          arr = arr.concat(item.childList);
                        });
                        setCityMap(arr);
                      }}
                    ></BLMOrgBtCity>
                  </Item>
                }
                old={
                  <Item
                    name="adcode"
                    label="适用城市"
                    colon={false}
                    rules={[
                      {
                        required: true,
                      },
                    ]}
                  >
                    <BLMCityCascader
                      // labelInValue={true}  组件问题，不生效
                      disabled={cityDisabled}
                      request={request}
                      requestParams={{ filterType: 1 }}
                      placeholder="请选择城市"
                      multiple={true}
                      allowClear={true}
                      // onChange={(value) => onChangeCity(value)}
                      style={{ width: '490px' }}
                      getOptions={(options) => {
                        let arr = [];
                        options.forEach((item) => {
                          arr = arr.concat(item.childDictList);
                        });
                        setCityMap(arr);
                      }}
                    />
                  </Item>
                }
              ></BLMOriAndCategoryContainer>
              <Item
                colon={false}
                name="type"
                label="分层维度"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  placeholder="请选择"
                  options={typeOption}
                  optionRender={(option) => {
                    return (
                      <div>
                        <div style={{ fontWeight: 'normal' }}>
                          {option.data.label}
                        </div>
                        <span style={{ color: 'rgba(0,0,0,0.6)' }}>
                          {option.data.explain}
                        </span>
                      </div>
                    );
                  }}
                  onChange={typeChange}
                ></Select>
              </Item>
              <Item
                colon={false}
                label="分层个数"
                name="typeNumber"
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Select
                  placeholder="请选择"
                  options={typeNumberOption}
                  onChange={typeNumberChange}
                ></Select>
              </Item>
            </Form>
            <div
              className="layer"
              style={{
                // visibility: typeNumber && typeValue ? 'visible' : 'hidden',
                display: typeNumber && typeValue ? 'block' : 'none',
              }}
            >
              <Form
                form={formLayer1}
                name="formLayer1Config"
                layout="inline"
                className="formLayer1"
              >
                <Item
                  name="nameLayer"
                  label="分层一"
                  colon={false}
                  validateTrigger="onBlur"
                  rules={[
                    {
                      required: true,
                      message: '必填',
                    },
                    {
                      max: 10,
                      message: '最多可输入10个字符',
                    },
                    () => ({
                      validator(_, value) {
                        if (!value) return Promise.resolve();
                        const formValue = form.getFieldsValue();
                        if (formValue.typeNumber === 2) {
                          const nameLayer4 =
                            formLayer4.getFieldValue('nameLayer');
                          if (value === nameLayer4) {
                            return Promise.reject(new Error(`名称不能重复`));
                          } else {
                            return Promise.resolve();
                          }
                        }
                        if (formValue.typeNumber === 3) {
                          const nameLayer2 =
                            formLayer2.getFieldValue('nameLayer');
                          const nameLayer4 =
                            formLayer4.getFieldValue('nameLayer');
                          if (value === nameLayer4 || value === nameLayer2) {
                            return Promise.reject(new Error(`名称不能重复`));
                          } else {
                            return Promise.resolve();
                          }
                        }
                        if (formValue.typeNumber === 4) {
                          const nameLayer2 =
                            formLayer2.getFieldValue('nameLayer');
                          const nameLayer3 =
                            formLayer3.getFieldValue('nameLayer');
                          const nameLayer4 =
                            formLayer4.getFieldValue('nameLayer');
                          if (
                            value === nameLayer4 ||
                            value === nameLayer2 ||
                            value === nameLayer3
                          ) {
                            return Promise.reject(new Error(`名称不能重复`));
                          } else {
                            return Promise.resolve();
                          }
                        }
                      },
                    }),
                  ]}
                >
                  <Input
                    width="140px"
                    placeholder="请输入"
                    maxLength="10"
                  ></Input>
                </Item>
                {isShowLevel ? (
                  <Item
                    name="layerNumLevel"
                    label={`服务分等级为`}
                    colon={false}
                  >
                    <Select
                      placeholder="请选择"
                      options={levelOptions}
                      mode="multiple"
                      allowClear={true}
                      optionFilterProp="label"
                    ></Select>
                  </Item>
                ) : (
                  <Item
                    name="layerNum"
                    label={`${typeName}小于等于`}
                    colon={false}
                    rules={[
                      {
                        required: true,
                        message: '必填、数字类型',
                      },
                    ]}
                  >
                    <Input
                      width="182px"
                      placeholder="请输入"
                      className="auto-input"
                      type="number"
                    ></Input>
                  </Item>
                )}
              </Form>
              {isShowLayer2 ? (
                <Form
                  form={formLayer2}
                  name="formLayer2Config"
                  layout="inline"
                  className="formLayer2"
                >
                  <Item
                    name="nameLayer"
                    label="分层二"
                    colon={false}
                    validateTrigger="onBlur"
                    rules={[
                      {
                        required: true,
                        message: '必填',
                      },
                      {
                        max: 10,
                        message: '最多可输入10个字符',
                      },
                      () => ({
                        validator(_, value) {
                          if (!value) return Promise.resolve();
                          const formValue = form.getFieldsValue();

                          if (formValue.typeNumber === 3) {
                            const nameLayer1 =
                              formLayer1.getFieldValue('nameLayer');
                            const nameLayer4 =
                              formLayer4.getFieldValue('nameLayer');
                            if (value === nameLayer4 || value === nameLayer1) {
                              return Promise.reject(new Error(`名称不能重复`));
                            } else {
                              return Promise.resolve();
                            }
                          }
                          if (formValue.typeNumber === 4) {
                            const nameLayer1 =
                              formLayer1.getFieldValue('nameLayer');
                            const nameLayer3 =
                              formLayer3.getFieldValue('nameLayer');
                            const nameLayer4 =
                              formLayer4.getFieldValue('nameLayer');
                            if (
                              value === nameLayer4 ||
                              value === nameLayer1 ||
                              value === nameLayer3
                            ) {
                              return Promise.reject(new Error(`名称不能重复`));
                            } else {
                              return Promise.resolve();
                            }
                          }
                        },
                      }),
                    ]}
                  >
                    <Input
                      width="140px"
                      placeholder="请输入"
                      maxLength="10"
                    ></Input>
                  </Item>
                  {isShowLevel ? (
                    <Item
                      name="layerNumLevel"
                      label={`服务分等级为`}
                      colon={false}
                    >
                      <Select
                        placeholder="请选择"
                        options={levelOptions}
                        mode="multiple"
                        allowClear={true}
                        optionFilterProp="label"
                      ></Select>
                    </Item>
                  ) : (
                    <>
                      <Item
                        name="layerNum1"
                        label={`${typeName}大于`}
                        colon={false}
                        dependencies={['layerNum2']}
                        rules={[
                          {
                            required: true,
                            message: '必填',
                          },
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              let layerNum2Value = parseFloat(
                                getFieldValue('layerNum2'),
                              );
                              let valueNumber = parseFloat(value);

                              if (
                                valueNumber < layerNum2Value ||
                                isNaN(layerNum2Value)
                              ) {
                                return Promise.resolve();
                              }
                              return Promise.reject(
                                new Error(`需小于${layerNum2Value}`),
                              );
                            },
                          }),
                        ]}
                      >
                        <Input
                          placeholder=""
                          className="min-input"
                          type="number"
                          width="59px"
                        ></Input>
                      </Item>
                      <Item
                        name="layerNum2"
                        label="小于等于"
                        colon={false}
                        dependencies={['layerNum1']}
                        rules={[
                          {
                            required: true,
                            message: '必填',
                          },
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              let layerNum1Value = parseFloat(
                                getFieldValue('layerNum1'),
                              );
                              let valueNumber = parseFloat(value);

                              if (
                                valueNumber > layerNum1Value ||
                                isNaN(layerNum1Value)
                              ) {
                                return Promise.resolve();
                              }
                              return Promise.reject(
                                new Error(`需大于${layerNum1Value}`),
                              );
                            },
                          }),
                        ]}
                      >
                        <Input
                          width="72px"
                          placeholder=""
                          className="min-input"
                          type="number"
                        ></Input>
                      </Item>
                    </>
                  )}
                </Form>
              ) : null}
              {isShowLayer3 ? (
                <Form
                  form={formLayer3}
                  name="formLayer3Config"
                  layout="inline"
                  className="formLayer3"
                >
                  <Item
                    name="nameLayer"
                    label="分层三"
                    colon={false}
                    validateTrigger="onBlur"
                    rules={[
                      {
                        required: true,
                        message: '必填',
                      },
                      {
                        max: 10,
                        message: '最多可输入10个字符',
                      },
                      () => ({
                        validator(_, value) {
                          if (!value) return Promise.resolve();
                          const formValue = form.getFieldsValue();

                          if (formValue.typeNumber === 4) {
                            const nameLayer1 =
                              formLayer1.getFieldValue('nameLayer');
                            const nameLayer2 =
                              formLayer2.getFieldValue('nameLayer');
                            const nameLayer4 =
                              formLayer4.getFieldValue('nameLayer');
                            if (
                              value === nameLayer4 ||
                              value === nameLayer1 ||
                              value === nameLayer2
                            ) {
                              return Promise.reject(new Error(`名称不能重复`));
                            } else {
                              return Promise.resolve();
                            }
                          }
                        },
                      }),
                    ]}
                  >
                    <Input
                      width="140px"
                      placeholder="请输入"
                      maxLength="10"
                    ></Input>
                  </Item>
                  {isShowLevel ? (
                    <Item
                      name="layerNumLevel"
                      label={`服务分等级为`}
                      colon={false}
                    >
                      <Select
                        placeholder="请选择"
                        options={levelOptions}
                        mode="multiple"
                        allowClear={true}
                        optionFilterProp="label"
                      ></Select>
                    </Item>
                  ) : (
                    <>
                      <Item
                        name="layerNum1"
                        label={`${typeName}大于`}
                        colon={false}
                        dependencies={['layerNum2']}
                        rules={[
                          {
                            required: true,
                            message: '必填',
                          },
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              let layerNum2Value = parseFloat(
                                getFieldValue('layerNum2'),
                              );
                              let valueNumber = parseFloat(value);

                              if (
                                valueNumber < layerNum2Value ||
                                isNaN(layerNum2Value)
                              ) {
                                return Promise.resolve();
                              }
                              return Promise.reject(
                                new Error(`需小于${layerNum2Value}`),
                              );
                            },
                          }),
                        ]}
                      >
                        <Input
                          placeholder=""
                          className="min-input"
                          type="number"
                          width="59px"
                        ></Input>
                      </Item>
                      <Item
                        name="layerNum2"
                        label="小于等于"
                        colon={false}
                        dependencies={['layerNum1']}
                        rules={[
                          {
                            required: true,
                            message: '必填',
                          },
                          ({ getFieldValue }) => ({
                            validator(_, value) {
                              let layerNum1Value = parseFloat(
                                getFieldValue('layerNum1'),
                              );
                              let valueNumber = parseFloat(value);

                              if (
                                valueNumber > layerNum1Value ||
                                isNaN(layerNum1Value)
                              ) {
                                return Promise.resolve();
                              }
                              return Promise.reject(
                                new Error(`需大于${layerNum1Value}`),
                              );
                            },
                          }),
                        ]}
                      >
                        <Input
                          placeholder=""
                          className="min-input"
                          type="number"
                          width="72px"
                        ></Input>
                      </Item>
                    </>
                  )}
                </Form>
              ) : null}
              <Form
                form={formLayer4}
                name="formLayer4Config"
                layout="inline"
                className="formLayer4"
              >
                <Item
                  name="nameLayer"
                  label={labelLayer4}
                  colon={false}
                  validateTrigger="onBlur"
                  rules={[
                    {
                      required: true,
                      message: '必填',
                    },
                    {
                      max: 10,
                      message: '最多可输入10个字符',
                    },
                    () => ({
                      validator(_, value) {
                        if (!value) return Promise.resolve();
                        const formValue = form.getFieldsValue();
                        if (formValue.typeNumber === 2) {
                          const nameLayer1 =
                            formLayer1.getFieldValue('nameLayer');
                          if (value === nameLayer1) {
                            return Promise.reject(new Error(`名称不能重复`));
                          } else {
                            return Promise.resolve();
                          }
                        }
                        if (formValue.typeNumber === 3) {
                          const nameLayer2 =
                            formLayer2.getFieldValue('nameLayer');
                          const nameLayer1 =
                            formLayer1.getFieldValue('nameLayer');
                          if (value === nameLayer1 || value === nameLayer2) {
                            return Promise.reject(new Error(`名称不能重复`));
                          } else {
                            return Promise.resolve();
                          }
                        }
                        if (formValue.typeNumber === 4) {
                          const nameLayer2 =
                            formLayer2.getFieldValue('nameLayer');
                          const nameLayer3 =
                            formLayer3.getFieldValue('nameLayer');
                          const nameLayer1 =
                            formLayer1.getFieldValue('nameLayer');
                          if (
                            value === nameLayer1 ||
                            value === nameLayer2 ||
                            value === nameLayer3
                          ) {
                            return Promise.reject(new Error(`名称不能重复`));
                          } else {
                            return Promise.resolve();
                          }
                        }
                      },
                    }),
                  ]}
                >
                  <Input
                    width="140px"
                    placeholder="请输入"
                    maxLength="10"
                  ></Input>
                </Item>
                {isShowLevel ? (
                  <Item
                    name="layerNumLevel"
                    label={`服务分等级为`}
                    colon={false}
                  >
                    <Select
                      disabled={disabledLayer4}
                      placeholder="请选择"
                      options={levelOptions}
                      mode="multiple"
                      allowClear={true}
                      optionFilterProp="label"
                    ></Select>
                  </Item>
                ) : (
                  <Item
                    name="layerNum"
                    label={`${typeName}大于`}
                    colon={false}
                    rules={[
                      {
                        required: true,
                        message: '必填、数字类型',
                      },
                    ]}
                  >
                    <Input
                      placeholder="请输入"
                      className="auto-input"
                      type="number"
                      width="208px"
                    ></Input>
                  </Item>
                )}
              </Form>
            </div>
          </div>
        </Spin>
      </Modal>
      <Modal
        centered={true}
        zIndex={4000}
        width={400}
        classNames={{
          body: 'ok-modal',
        }}
        maskClosable={false}
        title="确认提交吗"
        open={okOpen}
        onCancel={() => {
          setOkOpen(false);
        }}
        onOk={onceOk}
      >
        <p>{`监测到${cityName.join(
          '、',
        )}已有城市配置，再次点击保存会更新之前的配置项为本次配置信息`}</p>
      </Modal>
    </>
  );
};

export default CityConfig;
