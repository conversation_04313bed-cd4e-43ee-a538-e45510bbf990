.size12 {
  font-size: 12px;
  font-weight: normal;
  line-height: 20px;
}
.size16 {
  font-size: 16px;
  font-width: 500;
  line-height: 24px;
}

.cityCard {
  margin-top: 12px;
  width: 332px;
  height: 172px;
  border-radius: 8px;
  border: 1px solid #e7e8eb;

  .cityName {
    flex-shrink: 0;
    margin: 0px 4px 0px 8px;
    font-size: 16px;
    font-weight: 500;
    line-height: 24px;
    letter-spacing: 0em;
    color: rgba(0, 0, 0, 0.9);
  }
  .times {
    padding-top: 4px;
    flex: 1;
    color: rgba(0, 0, 0, 0.6);
    display: flex;
    flex-direction: row;
    align-content: center;
    flex-wrap: wrap;
    box-sizing: border-box;
    padding-right: 8px;
    justify-content: end;
    min-width: 80px;
    span {
      font-size: 12px;
      font-weight: normal;
      line-height: 20px;
      letter-spacing: 0em;
      color: rgba(0, 0, 0, 0.6);
    }
  }
  .fenceng {
    padding: 0 0 8px 8px;
    border-bottom: 1px solid #e7e8eb;
    min-width: 330px;
    .title {
      margin-top: 8px;
      font-size: 14px;
      font-weight: normal;
      line-height: 22px;
      letter-spacing: 0em;
      color: rgba(0, 0, 0, 0.9);
    }
    .content {
      box-sizing: border-box;
      display: inline-block;
      margin: 8px 8px 0 0;
      width: 153px;
      border-radius: 4px;
      background-color: #f3f3f4;
      // border: 1px solid #e8f2ff;
      padding: 0 8px;
      span {
        font-size: 12px;
        font-weight: normal;
        line-height: 20px;
        letter-spacing: 0em;
        color: rgba(0, 0, 0, 0.6);
      }
    }
  }

  .btn {
    width: 155px;
    color: rgba(0, 0, 0, 0.9);
    .img {
      vertical-align: bottom;
      margin: 5px;
    }
  }
  .btn-box {
    margin-top: 3px;
    span {
      position: relative;
      top: -1px;
    }

    .ant-btn-link:hover {
      color: rgba(0, 0, 0, 0.9) !important;
    }
  }
}
.layers-box {
  min-height: 60px;
  .font-w {
    span {
      padding: 0 4px;
      font-weight: 500 !important;
      color: rgba(0, 0, 0, 0.9) !important;
    }
  }
}
:where(.css-iksrmj).ant-tag {
  padding-inline: 3px;
}
.title-cityCard {
  display: flex;
  flex-direction: row;
}
.delete-title {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  align-items: center;
  gap: 10px;
}
