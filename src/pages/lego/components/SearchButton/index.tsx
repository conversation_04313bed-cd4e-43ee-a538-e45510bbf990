import { useRef, useState } from 'react';
import { Button } from '@blmcp/ui';
import { store } from '@/pages/lego/hooks/useComponent';
import { getCurrnetPageId } from '@/pages/lego/utils';
import relationCenterExp from '../../libraryMaterials/module/RelationCenter';
import './index.less';
import linkageCenterExp from '../../libraryMaterials/module/Linkage';
import queryCenterExp from '@/pages/lego/libraryMaterials/module/Query';
import isMobile from '../../utils/isMobile';
import RuleBranchDrawer from '../RuleBranchDrawer';
import {
  cleanBranchConditions,
  queryBranchConditions,
} from '../RuleBranchDrawer/ruleBranchService';
import Icon from './Icon.jsx';

const waitTime = 1000;

export const SearchButton = (props) => {
  const linkageCenter = linkageCenterExp(props.uuid);
  const queryCenter = queryCenterExp(props.uuid);
  const relationCenter = relationCenterExp(props.uuid);
  const [loading, setLoading] = useState(false);
  const [expandText, setExpandText] = useState('展开筛选项');
  const filterLinkComponents = props.filterLinkComponents;

  // 条件分支抽屉状态
  const [open, setOpen] = React.useState<boolean>(false);

  const dispatch = function () {
    // 触发筛选器查询
    if (filterLinkComponents && filterLinkComponents.length > 0) {
      const map: any = {};
      console.log('filterLinkComponents', filterLinkComponents);
      filterLinkComponents.forEach((item: any) => {
        const componentMeta = store.get(item.componentId) || {};
        if (
          !map[item.componentId] &&
          item.datasetId === componentMeta.dataSourceId
        ) {
          map[item.componentId] = 1;
          relationCenter.notify('all', item.componentId, {
            cache: false,
          });
        }
      });
    } else {
      relationCenter.notify('all', undefined, {
        cache: false,
      });
    }
  };
  const search = () => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, waitTime);
    queryCenter.resetTableQuery();

    dispatch();

    blmAnalysisModuleClick({
      eventId: 'e_leopard_cp_click_00003812',
      pageId: 'p_leopard_cp_00000884',
      ext: {
        str0_e: props.uuid,
      },
    });
  };
  const [initData, setInitData] = useState([]);

  const handleBranchConditions = () => {
    queryBranchConditions({ reportId: getCurrnetPageId() }).then((res) => {
      if (res.code === 1) {
        setInitData(res?.data);
      }
    });
  };
  // 模拟条件分支抽屉
  // const search = () => {
  //   setOpen(true);
  //   handleBranchConditions();
  // };
  const reset = () => {
    // 重置，走联动规则
    const resetKey = `reset${props.partialContainerFilterId || ''}`;
    linkageCenter.notify(resetKey);

    queryCenter.resetTableQuery();
    dispatch();
  };
  const wrapRef = useRef();
  const onExpand = () => {
    wrapRef.current?.parentNode?.parentNode?.classList?.toggle('expand');
    setExpandText(expandText === '展开筛选项' ? '收起筛选项' : '展开筛选项');
  };

  // 条件分支确定
  const handelOk = () => {
    setOpen(false);
  };
  // 条件分支取消
  const handelCancel = () => {
    setOpen(false);
  };
  const mockSaveBranchConditions = {
    reportId: '1780498439921537523',
    branchInfo: [
      {
        branchType: 2,
        selectedId: '633357049',
        content: [
          {
            operator: 'or',
            condition: [
              {
                operator: 'and',
                children: [
                  {
                    compareType: 'eq',
                    columnId: 41019,
                    columnTitle: '平台名称',
                    compareValue: 'aa',
                  },
                ],
              },
            ],
            operatorInfos: [
              { nodeId: '743745', nodeName: '(计数) 司机姓名', operator: 1 },
            ],
          },
        ],
      },
    ],
  };
  const mockSaveBranchConditions2 = [
    {
      selectedId: 633357049,
      branchType: 2,
      content: [
        {
          operator: 'or',
          condition: [
            {
              operator: 'and',
              children: [
                {
                  compareType: 'eq',
                  columnId: 41019,
                  columnTitle: '平台名称',
                  compareValue: '11',
                },
                {
                  compareType: 'eq',
                  columnId: 41017,
                  columnTitle: '平台编码',
                  compareValue: '22',
                },
              ],
            },
            {
              operator: 'and',
              children: [
                {
                  compareType: 'eq',
                  columnId: 41021,
                  columnTitle: '省份编码',
                  compareValue: '33',
                },
                {
                  compareType: 'eq',
                  columnId: 41023,
                  columnTitle: '省份名称',
                  compareValue: '44',
                },
              ],
            },
          ],
          operatorInfos: [
            {
              nodeId: '743745',
              nodeName: '(计数) 司机姓名',
              operator: 1,
            },
          ],
        },
        {
          operator: 'or',
          condition: [
            {
              operator: 'and',
              children: [
                {
                  compareType: 'eq',
                  columnId: 41029,
                  columnTitle: '运力公司编码',
                  compareValue: '55',
                },
              ],
            },
          ],
          operatorInfos: [
            {
              nodeId: '743745',
              nodeName: '(计数) 司机姓名',
              operator: 1,
            },
          ],
        },
      ],
    },
    {
      selectedId: 633357045,
      branchType: 2,
      content: [
        {
          operator: 'or',
          condition: [
            {
              operator: 'and',
              children: [
                {
                  compareType: 'eq',
                  columnId: 42963,
                  columnTitle: '城市编码',
                  compareValue: '66',
                },
              ],
            },
          ],
          operatorInfos: [
            {
              nodeId: '743755',
              nodeName: '圈选司机数',
              operator: 1,
            },
            {
              nodeId: '743757',
              nodeName: '达标司机数',
              operator: 1,
            },
          ],
        },
      ],
    },
  ];
  return (
    <div
      className={`lego-search-wrap ${isMobile() ? 'isMobile' : ''}`}
      ref={wrapRef}
    >
      <Button className="lego-search-btn" onClick={reset}>
        重置
      </Button>
      <Button type="primary" onClick={search} loading={loading}>
        查询
      </Button>
      {isMobile() ? (
        <div className="condition-expand-icon" onClick={onExpand}>
          <span style={{ marginRight: '5px' }}>{expandText}</span>
          <Icon></Icon>
        </div>
      ) : null}
      <RuleBranchDrawer
        open={open}
        onOk={handelOk}
        initialData={initData}
        // initialData={[]}
        onCancel={handelCancel}
      ></RuleBranchDrawer>
    </div>
  );
};
