.lego-table-wrap {
  display: block;
  /* stylelint-disable selector-class-pattern, alpha-value-notation */
  .BLM_Antd_content .ant-table .ant-table-thead > tr > th {
    border-top: 0.5px solid rgba(37, 52, 79, 0.08) !important;
    //border-left: 0.5px solid rgba(37, 52, 79, 0.08) !important;
  }
  /* stylelint-disable */
  .BLM_Antd_content .ant-pagination li:nth-child(2) {
    margin-left: 70px;
  }
  &.mobile {
    .BLM_Antd_content .ant-pagination li:nth-child(2) {
      margin-left: 0;
    }
    /* stylelint-enable selector-class-pattern, alpha-value-notation */
    .ant-pagination-simple {
      align-items: center;
      li {
        vertical-align: middle;
        * {
          vertical-align: middle;
          position: relative;
          transform: translate3d(0, 0, 0);
        }
      }
    }
  }
  .lego-operation-item {
    margin: 0px 5px;
    cursor: pointer;
    white-space: nowrap;
  }
  .lego-operation-wrapper {
    width: 100%;
    overflow: auto;
    // 滚动条默认隐藏，滚动显示
    &::-webkit-scrollbar {
      width: 2px !important;
      height: 2px !important;
    }

    &::-webkit-scrollbar-thumb {
      background-color: transparent;
    }

    &:hover::-webkit-scrollbar-thumb {
      background-color: rgba(0, 0, 0, 0.3);
    }
  }
}

.ant-table-wrapper .ant-table-column-sorters {
  max-width: 100% !important;
}

.lego-table-container {
  &::-webkit-scrollbar {
    width: 0px;
    height: 0px;
  }

  /* 隐藏滚动条轨道 */
  &::-webkit-scrollbar-track {
    background-color: transparent;
  }

  // 控制组件在正确位置
  .react-resizable {
    position: relative;
    background-clip: padding-box;
  }

  .react-resizable-handle {
    position: absolute;
    right: -5px;
    bottom: 0;
    z-index: 1;
    width: 10px;
    height: 100%;
    cursor: col-resize;
  }
}

// 滚动条默认隐藏，滚动显示
.lego-bi-scroll-hide .ant-table-body::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

.lego-bi-scroll-hide .ant-table-body::-webkit-scrollbar-thumb {
  background-color: transparent;
}

.lego-bi-scroll-hide:hover .ant-table-body::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
}
