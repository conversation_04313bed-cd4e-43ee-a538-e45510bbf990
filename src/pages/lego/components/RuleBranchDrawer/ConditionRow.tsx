import React from 'react';
import { Select, Button, BLMIconFont } from '@blmcp/ui';
import { ConditionRowState, DatasetColumn, CompareType } from './types';
import { COMPARE_TYPE_OPTIONS } from './utils';
import ValidatedInput from './ValidatedInput';
import ValidatedSelect from './ValidatedSelect';

interface ConditionRowProps {
  condition: ConditionRowState;
  datasetColumns: DatasetColumn[];
  loading?: boolean;
  canDelete: boolean;
  onUpdate: (condition: ConditionRowState) => void;
  onDelete: () => void;
  lastSubmittedAt: number;
}

const ConditionRow: React.FC<ConditionRowProps> = ({
  condition,
  datasetColumns,
  loading = false,
  canDelete,
  onUpdate,
  onDelete,
  lastSubmittedAt,
}) => {
  // 处理字段选择变更
  const handleColumnChange = (columnId: number) => {
    const selectedColumn = datasetColumns.find(
      (col) => col.columnId === columnId,
    );
    onUpdate({
      ...condition,
      column: selectedColumn || null,
      compareValue: '', // 清空比较值
    });
  };

  // 处理比较类型变更
  const handleCompareTypeChange = (compareType: CompareType) => {
    onUpdate({
      ...condition,
      compareType,
    });
  };

  // 处理比较值变更
  const handleCompareValueChange = (e: string) => {
    onUpdate({
      ...condition,
      compareValue: e,
    });
  };

  // 处理比较值失去焦点，去除空格
  const handleCompareValueBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    const trimmedValue = e.target.value?.replace(/\s/g, '');
    if (trimmedValue !== condition.compareValue) {
      onUpdate({
        ...condition,
        compareValue: trimmedValue,
      });
    }
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'space-between' }}>
      <div style={{ flex: 1 }}>
        <ValidatedSelect
          placeholder="请选择字段"
          style={{ width: 'calc(50% - 46px)' }}
          value={condition.column?.columnId}
          onChange={handleColumnChange}
          loading={loading}
          showSearch
          allowClear
          filterOption={(input, option) =>
            (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
          }
          options={datasetColumns.map((col) => ({
            value: col.columnId,
            label: col.title,
          }))}
          lastSubmittedAt={lastSubmittedAt}
        />
        <Select
          style={{ width: 52, marginLeft: 8, marginRight: 8, minWidth: 52 }}
          value={condition.compareType}
          onChange={handleCompareTypeChange}
          options={COMPARE_TYPE_OPTIONS}
          // disabled
        />
        <ValidatedInput
          placeholder="请输入"
          style={{ width: 'calc(50% - 46px)' }}
          value={condition.compareValue}
          onChange={handleCompareValueChange}
          onBlur={handleCompareValueBlur}
          allowClear
          lastSubmittedAt={lastSubmittedAt}
        />
      </div>
      <Button
        icon={<BLMIconFont type="BLM-ic-delete-o" />}
        disabled={loading || !canDelete}
        onClick={onDelete}
        style={{
          marginLeft: 8,
        }}
      />
    </div>
  );
};

export default ConditionRow;
