// 接口数据类型定义

// 分支类型枚举
export enum BranchType {
  CHART_LEVEL = 1, // 图表级配置
  FIELD_LEVEL = 2, // 字段级配置
}

// 操作符类型
export enum OperatorType {
  AND = 'and',
  OR = 'or',
}

// 条件运算符类型
export enum CompareType {
  EQ = 'eq', // 等于
}

// 字段/图表操作隐藏类型
export enum ActionType {
  HIDE = 1, // 隐藏
}

// 下拉选项类型
export interface DropdownOption {
  value: string | number;
  label: string;
  type?: number;
}

// 报告元素列表项
export interface ReportElement {
  value: number; // 数据集datasetId或者图表elementId
  label: string; // 数据集名称或者图表名称
  datasetId: number; // 若选择图表，当前图表的数据集ID，选择数据集，跟数据集ID一致
  dropdownList: DropdownOption[]; // 可配置字段list
}

// 数据集字段信息
export interface DatasetColumn {
  columnId: number;
  title: string;
  dataType: number;
  category: string;
  key: string;
  alias?: string;
  fieldDesc?: string;
}

// 维度信息
export interface DimensionInfo extends DatasetColumn {
  biType: number;
}

// 指标信息
export interface MeasureInfo extends DatasetColumn {
  biType: number;
}

// 指标分组
export interface MeasureGroup {
  category: string;
  columnList: MeasureInfo[];
}

// 数据集获取字段接口
export interface DatasetColumnsResponse {
  dimensionList: DimensionInfo[];
  measureList: MeasureGroup[];
  tenantId: number;
}

// 条件子项
export interface ConditionChild {
  compareType: CompareType;
  columnId: number;
  columnTitle: string;
  compareValue: string;
}

// 条件组（且条件）
export interface ConditionGroup {
  operator: OperatorType.AND;
  children: ConditionChild[];
}

// 操作信息
export interface OperatorInfo {
  nodeId: string;
  nodeName: string;
  operator: ActionType;
  type?: number;
}

// 条件配置内容
export interface ConditionContent {
  operator: OperatorType.OR;
  condition: ConditionGroup[];
  operatorInfos: OperatorInfo[];
}

// 分支信息
export interface BranchInfo {
  branchType: BranchType;
  selectedId: number | string; // 支持字符串和数字类型
  content: ConditionContent[];
}

// 保存接口请求参数
export interface SaveBranchRequest {
  reportId: string | number | null;
  branchInfo: BranchInfo[];
}

// 通用API响应
export interface ApiResponse<T = any> {
  code: number;
  ts: number;
  msg: string;
  data: T;
}

// 组件内部状态类型定义

// 分支配置项状态
export interface BranchConfigState {
  id: string; // 唯一标识
  name: string; // 分支名称
  branchType: BranchType | null; // 分支类型
  selectedElement: ReportElement | null; // 选中的元素
  conditionConfigs: ConditionConfigState[]; // 条件配置列表
}

// 条件配置状态
export interface ConditionConfigState {
  id: string; // 唯一标识
  name: string; // 条件配置名称
  andGroups: AndGroupState[]; // 且条件组列表
  resultConfig: ResultConfigState; // 结果配置（每个条件配置组都有自己的结果配置）
}

// 且条件组状态
export interface AndGroupState {
  id: string; // 唯一标识
  conditions: ConditionRowState[]; // 条件行列表
}

// 条件行状态
export interface ConditionRowState {
  id: string; // 唯一标识
  column: DatasetColumn | null; // 选中的字段
  compareType: CompareType; // 比较类型
  compareValue: string; // 比较值
}

// 结果配置状态
export interface ResultConfigState {
  selectedFields: DropdownOption[]; // 选中的隐藏字段
  searchText: string; // 搜索文本
}

// 主组件状态
export interface RuleBranchDrawerState {
  branchConfigs: BranchConfigState[]; // 分支配置列表
  reportElementsCache: Map<BranchType, ReportElement[]>; // 报告元素缓存
  datasetColumnsCache: Map<number, DatasetColumn[]>; // 数据集字段缓存
  loading: boolean; // 加载状态
}

// 组件Props类型
export interface RuleBranchDrawerProps {
  open: boolean;
  reportId?: number;
  initialData?: BranchInfo[]; // 初始数据（用于回填）
  onOk: (data: SaveBranchRequest) => void;
  onCancel: () => void;
}
