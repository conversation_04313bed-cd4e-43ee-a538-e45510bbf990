import request from '@/utils/request';
import {
  BranchType,
  ReportElement,
  DatasetColumnsResponse,
  SaveBranchRequest,
} from './types';

// Mock数据

// 模拟报告元素列表数据
const mockReportElements: ReportElement[] = [
  {
    value: 10086,
    label: '活动名称',
    datasetId: 57,
    dropdownList: [
      { value: '1', label: '减佣活动', type: 2 },
      { value: '2', label: '免佣活动', type: 1 },
      { value: '3', label: '抽奖活动', type: 2 },
    ],
  },
  {
    value: 10087,
    label: '活动名称1',
    datasetId: 58,
    dropdownList: [
      { value: '1', label: '减佣活动', type: 2 },
      { value: '2', label: '免佣活动', type: 2 },
      { value: '3', label: '抽奖活动', type: 1 },
    ],
  },
  {
    value: 10088,
    label: '订单图表',
    datasetId: 59,
    dropdownList: [
      { value: '4', label: '订单数', type: 1 },
      { value: '5', label: '订单金额', type: 2 },
      { value: '6', label: '平均订单价值', type: 1 },
    ],
  },
  {
    value: 10089,
    label: '用户数据集',
    datasetId: 60,
    dropdownList: [
      { value: '7', label: '用户ID', type: 1 },
      { value: '8', label: '用户名称', type: 1 },
      { value: '9', label: '注册时间', type: 2 },
    ],
  },
  {
    value: 10090,
    label: '销售图表',
    datasetId: 61,
    dropdownList: [
      { value: '10', label: '销售额', type: 1 },
      { value: '11', label: '销售量', type: 1 },
      { value: '12', label: '客单价', type: 1 },
    ],
  },
];

// 模拟数据集字段数据
const mockDatasetColumns: DatasetColumnsResponse = {
  dimensionList: [
    {
      columnId: 40151,
      dataType: 2,
      title: '筛选日期',
      category: '基础维度',
      key: '筛选日期',
      biType: 1,
      alias: '筛选日期',
      fieldDesc: '筛选日期',
    },
    {
      columnId: 40153,
      dataType: 1,
      title: '平台编码',
      category: '基础维度',
      key: 'tenant_id',
      biType: 1,
      fieldDesc: '平台编码',
    },
    {
      columnId: 40155,
      dataType: 0,
      title: '平台名称',
      category: '基础维度',
      key: 'tenant_name',
      biType: 1,
      fieldDesc: '平台名称',
    },
    {
      columnId: 40157,
      dataType: 0,
      title: '省份编码',
      category: '基础维度',
      key: 'procode',
      biType: 1,
      fieldDesc: '司机所属省份编码',
    },
    {
      columnId: 40159,
      dataType: 0,
      title: '省份名称',
      category: '基础维度',
      key: 'proname',
      biType: 1,
      fieldDesc: '司机所属省份名称',
    },
  ],
  measureList: [
    {
      category: '行为转化分析',
      columnList: [
        {
          columnId: 40195,
          dataType: 1,
          title: '是否参与',
          category: '行为转化分析',
          key: 'is_join',
          biType: 2,
          fieldDesc: '是否参与',
        },
        {
          columnId: 40215,
          dataType: 1,
          title: '是否达标',
          category: '行为转化分析',
          key: 'is_grade',
          biType: 2,
          fieldDesc: '统计周期内，司机是否符合活动规则且达成任一活动阶梯',
        },
        {
          columnId: 40233,
          dataType: 1,
          title: '圈选司机数',
          category: '行为转化分析',
          key: '圈选司机数',
          biType: 2,
          fieldDesc: '统计周期内，活动参与人群圈选的司机数量',
        },
      ],
    },
    {
      category: '自建指标',
      columnList: [
        {
          columnId: 39323,
          dataType: 0,
          title: '群聊总数测试',
          category: '自建指标',
          key: '群聊总数测试',
          biType: 2,
          alias: '群聊总数测试',
          fieldDesc: 'sum([群聊总数基础])/COUNTD([城市编码])',
        },
        {
          columnId: 39325,
          dataType: 0,
          title: '群成员总数测试',
          category: '自建指标',
          key: '群成员总数测试',
          biType: 2,
          alias: '群成员总数测试',
          fieldDesc: 'sum([群成员总数基础])/COUNTD([城市编码])',
        },
      ],
    },
  ],
  tenantId: 1,
};

// Mock服务函数

/**
 * 模拟查询报告元素列表接口
 */
// export const mockQueryReportElementList = async (branchType: BranchType) => {
//   // 模拟网络延迟
//   await new Promise((resolve) => setTimeout(resolve, 500));

//   // 根据分支类型返回不同的数据
//   const filteredElements =
//     branchType === BranchType.CHART_LEVEL
//       ? mockReportElements.filter((el) => el.label.includes('图表'))
//       : mockReportElements.filter((el) => !el.label.includes('图表'));

//   return {
//     code: 1,
//     ts: Date.now(),
//     msg: '服务接口调用成功',
//     data: filteredElements,
//   };
// };
export const mockQueryReportElementList = (params: unknown) => {
  return request({
    url: '/lego/admin/v1/ai/chart-manager/branch/queryReportElementList',
    method: 'POST',
    data: params,
  });
};

/**
 * 模拟查询数据集字段接口
 */
// export const mockQueryDatasetColumns = async (datasetId: number) => {
//   // 模拟网络延迟
//   await new Promise((resolve) => setTimeout(resolve, 300));

//   return {
//     code: 1,
//     ts: Date.now(),
//     msg: '服务接口调用成功',
//     data: mockDatasetColumns,
//   };
// };
export const mockQueryDatasetColumns = (params = {}) => {
  return request({
    url: '/lego/admin/v1/ai/chart-manager/queryDatasetsColumnsV2',
    method: 'POST',
    data: params,
  });
};

/**
 * 模拟保存分支条件接口
 */
// export const mockSaveBranchConditions = async (data: SaveBranchRequest) => {
//   // 模拟网络延迟
//   await new Promise((resolve) => setTimeout(resolve, 800));

//   console.log('保存的数据:', JSON.stringify(data, null, 2));

//   return {
//     code: 1,
//     ts: Date.now(),
//     msg: '服务接口调用成功',
//     data: 'success',
//   };
// };
export const mockSaveBranchConditions = (params = {}) => {
  return request({
    url: '/lego/admin/v1/ai/chart-manager/branch/saveBranchConditions',
    method: 'POST',
    data: params,
  });
};

export const cleanBranchConditions = (params = {}) => {
  return request({
    url: '/lego/admin/v1/ai/chart-manager/branch/cleanBranchConditions',
    method: 'POST',
    data: params,
  });
};

export const queryBranchConditions = (params = {}) => {
  return request({
    url: '/lego/admin/v1/ai/chart-manager/branch/queryBranchConditions',
    method: 'POST',
    data: params,
  });
};
