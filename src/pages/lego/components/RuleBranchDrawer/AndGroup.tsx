import React from 'react';
import { Button, Typography, BLMIconFont } from '@blmcp/ui';
import { AndGroupState, ConditionRowState, DatasetColumn } from './types';
import ConditionRow from './ConditionRow';

const { Text } = Typography;

interface AndGroupProps {
  group: AndGroupState;
  datasetColumns: DatasetColumn[];
  loading?: boolean;
  canDelete: boolean;
  onUpdate: (group: AndGroupState) => void;
  onDelete: () => void;
  onAddCondition: () => void;
  onDeleteCondition: (conditionId: string) => void;
  onUpdateCondition: (
    conditionId: string,
    condition: ConditionRowState,
  ) => void;
  lastSubmittedAt: number;
}

const AndGroup: React.FC<AndGroupProps> = ({
  group,
  datasetColumns,
  loading = false,
  canDelete,
  onUpdate,
  onDelete,
  onAddCondition,
  onDeleteCondition,
  onUpdateCondition,
  lastSubmittedAt,
}) => {
  return (
    <div className="and-group">
      {/* 且条件标签和删除按钮 */}
      <div className="and-group-leftLine">
        <div className="and-group-leftText">且</div>
      </div>

      {/* 条件列表 */}
      <div className="and-group-content">
        {group.conditions.map((condition, index) => (
          <div key={condition.id} className="and-group-content-item">
            {/* 分支连接线 */}
            <div className="and-group-content-item-line" />
            <div
              style={{
                marginBottom: 16,
              }}
            >
              <ConditionRow
                condition={condition}
                datasetColumns={datasetColumns}
                loading={loading}
                canDelete={group.conditions.length > 1}
                onUpdate={(updatedCondition) =>
                  onUpdateCondition(condition.id, updatedCondition)
                }
                onDelete={() => onDeleteCondition(condition.id)}
                lastSubmittedAt={lastSubmittedAt}
              />
            </div>
          </div>
        ))}

        <div className="and-group-content-item-btn">
          <Button
            type="link"
            notspacelinkbtn
            icon={<BLMIconFont type="BLM-ic-plus-o" />}
            onClick={onAddCondition}
            disabled={loading}
            style={{
              marginRight: 16,
            }}
          >
            新增条件
          </Button>
          <Button
            type="link"
            notspacelinkbtn
            icon={<BLMIconFont type="BLM-ic-delete-o" />}
            onClick={onDelete}
            disabled={loading || !canDelete}
          >
            删除条件组
          </Button>
        </div>
      </div>
    </div>
  );
};

export default AndGroup;
