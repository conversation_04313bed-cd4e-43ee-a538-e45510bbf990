import { Select, SelectProps } from '@blmcp/ui';
import { useFormLikeValidation } from './utils';

interface ValidatedSelectProps extends SelectProps {
  /** 当前值 */
  value?: any;
  /** 修改事件 */
  onChange: (value: any, option: any) => void;
  /** 全局上次保存的时间戳 */
  lastSubmittedAt: number | null;
  /** 错误提示文案 */
  message?: string;
  style?: React.CSSProperties;
}

const ValidatedSelect = ({
  value,
  onChange,
  lastSubmittedAt,
  message = '请选择',
  style,
  ...restProps // 透传给 antd Select
}: ValidatedSelectProps) => {
  const { status, markTouched } = useFormLikeValidation({
    value,
    lastSubmittedAt,
    message,
  });

  return (
    <Select
      {...restProps}
      value={value}
      status={status}
      style={style}
      onChange={(v, option) => {
        markTouched();
        onChange(v, option);
      }}
    />
  );
};

export default ValidatedSelect;
