.branch-config {
  border: 1px solid #f2f3f5;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  &-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;
    &-title {
      font-weight: 500;
      line-height: 24px;
      font-size: 16px;
      color: #1d2129;
    }
    &-btn {
      height: 24px;
    }
  }
  .ant-collapse-header {
    align-items: center !important;
  }
}

.condition-config {
  border-radius: 4px;
  background: #f7f8fa;
  padding: 16px;
  &-item {
    border-bottom: 1px solid #e5e6eb;
    margin-bottom: 16px;
    &-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 16px;
      &-title {
        line-height: 24px;
        font-size: 14px;
        color: #1d2129;
        font-weight: 500;
      }
      &-btn {
        height: 24px;
      }
    }
    &-content {
      &-add {
        margin-left: 8px;
        margin-top: 16px;
      }
      &-orLine {
        display: 'flex';
        align-items: 'center';
        border-left: 1px solid #c0d9ff;
        position: relative;
        left: 16px;
        margin-right: 16px;
        &-text {
          font-weight: 500;
          width: 32px;
          height: 32px;
          line-height: 32px;
          background-color: #e8f0ff;
          color: #2a6bff;
          border-radius: 4px;
          position: relative;
          top: calc(50% - 16px);
          right: 16px;
          text-align: center;
        }
      }
      &-line {
        display: flex;
      }
    }
  }
  &-add {
    margin-left: 8px;
  }
}
.and-group {
  display: flex;
  &-leftLine {
    position: relative;
    left: 16px;
    margin-right: 16px;
  }
  &-leftText {
    font-weight: 500;
    width: 32px;
    height: 32px;
    line-height: 32px;
    background-color: #e8f0ff;
    color: #2a6bff;
    border-radius: 4px;
    position: relative;
    top: calc(50% - 16px);
    right: 16px;
    text-align: center;
    z-index: 10;
  }
  &-content {
    flex: 1;
    &-item {
      &-line {
        // position: 'absolute';
        // left: -20;
        // top: -12;
        // width: 20;
        // height: 24;
        // // border-left: 1px solid #C0D9FF;
        // border-bottom: 1px solid #c0d9ff;
        // border-bottom-left-radius: '6px';
      }
    }
  }
}

.result-config {
  margin-top: 16px;
  margin-bottom: 16px;
  &-title {
    font-size: 14px;
    color: #1d2129;
    margin-bottom: 16px;
    font-weight: 500;
  }
}
.and-group-content-item:not(:first-child) {
  position: relative;
  &::before {
    content: '';
    position: absolute;
    left: -32px;
    top: 16px;
    width: 32px;
    height: 50%;
    background-color: transparent;
    border-top: 1px solid #c0d9ff;
  }
}
.and-group-content-item:first-child {
  // position: relative;
  &::before {
    content: '';
    position: absolute;
    left: 16px;
    top: 16px;
    width: 32px;
    height: 50%;
    background-color: transparent;
    border-top: 1px solid #c0d9ff;
    border-left: 1px solid #c0d9ff;
    border-top-left-radius: 4px;
  }
}
.and-group-content-item-btn {
  margin-left: 8px;
  &::before {
    content: '';
    position: absolute;
    left: 16px;
    top: calc(50% - 16px);
    width: 33px;
    height: 50%;
    background-color: transparent;
    border: 1px solid #c0d9ff;
    border-bottom-left-radius: 4px;
    border-top: none;
    border-right: none;
  }
}
