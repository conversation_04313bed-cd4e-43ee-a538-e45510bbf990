import { Input } from '@blmcp/ui';
import { useFormLikeValidation } from './utils';

const ValidatedInput = ({ value, onChange, lastSubmittedAt, style }: any) => {
  const { status, markTouched } = useFormLikeValidation({
    value,
    lastSubmittedAt,
    message: '请输入内容',
  });

  return (
    <Input
      placeholder="请输入"
      value={value}
      style={style}
      onChange={(e) => {
        markTouched();
        onChange(e.target.value);
      }}
      status={status}
    />
  );
};
export default ValidatedInput;
