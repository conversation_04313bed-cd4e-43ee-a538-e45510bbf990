import React from 'react';
import { But<PERSON>, Typography, Popconfirm, BLMIconFont } from '@blmcp/ui';
import {
  ConditionConfigState,
  AndGroupState,
  ConditionRowState,
  DatasetColumn,
  ReportElement,
} from './types';
import {
  createDefaultConditionConfig,
  createDefaultAndGroup,
  createDefaultConditionRow,
  canDeleteAndGroup,
} from './utils';
import AndGroup from './AndGroup';
import ResultConfig from './ResultConfig';
import './index.less';

const { Text } = Typography;

interface ConditionConfigProps {
  configs: ConditionConfigState[];
  datasetColumns: DatasetColumn[];
  selectedElement: ReportElement | null;
  loading?: boolean;
  branchType?: 1 | 2 | null;
  onChange: (configs: ConditionConfigState[]) => void;
  lastSubmittedAt?: number;
}

const ConditionConfig: React.FC<ConditionConfigProps> = ({
  configs,
  datasetColumns,
  selectedElement,
  loading = false,
  branchType,
  onChange,
  lastSubmittedAt,
}) => {
  console.log(configs, 'configs----111', datasetColumns, selectedElement);
  // 添加条件配置
  const handleAddConditionConfig = () => {
    const newConfig = createDefaultConditionConfig(configs.length + 1);
    onChange([...configs, newConfig]);
  };

  // 删除条件配置
  const handleDeleteConditionConfig = (configId: string) => {
    const updatedConfigs = configs.filter((config) => config.id !== configId);
    onChange(updatedConfigs);
  };

  // 更新条件配置
  const handleUpdateConditionConfig = (
    configId: string,
    updatedConfig: ConditionConfigState,
  ) => {
    const updatedConfigs = configs.map((config) =>
      config.id === configId ? updatedConfig : config,
    );
    onChange(updatedConfigs);
  };

  // 添加且条件组
  const handleAddAndGroup = (configId: string) => {
    const updatedConfigs = configs.map((config) => {
      if (config.id === configId) {
        const newAndGroup = createDefaultAndGroup();
        return {
          ...config,
          andGroups: [...config.andGroups, newAndGroup],
        };
      }
      return config;
    });
    onChange(updatedConfigs);
  };

  // 删除且条件组
  const handleDeleteAndGroup = (configId: string, groupId: string) => {
    const updatedConfigs = configs.map((config) => {
      if (config.id === configId) {
        return {
          ...config,
          andGroups: config.andGroups.filter((group) => group.id !== groupId),
        };
      }
      return config;
    });
    onChange(updatedConfigs);
  };

  // 更新且条件组
  const handleUpdateAndGroup = (
    configId: string,
    groupId: string,
    updatedGroup: AndGroupState,
  ) => {
    const updatedConfigs = configs.map((config) => {
      if (config.id === configId) {
        return {
          ...config,
          andGroups: config.andGroups.map((group) =>
            group.id === groupId ? updatedGroup : group,
          ),
        };
      }
      return config;
    });
    onChange(updatedConfigs);
  };

  // 添加条件行
  const handleAddConditionRow = (configId: string, groupId: string) => {
    const updatedConfigs = configs.map((config) => {
      if (config.id === configId) {
        return {
          ...config,
          andGroups: config.andGroups.map((group) => {
            if (group.id === groupId) {
              const newCondition = createDefaultConditionRow();
              return {
                ...group,
                conditions: [...group.conditions, newCondition],
              };
            }
            return group;
          }),
        };
      }
      return config;
    });
    onChange(updatedConfigs);
  };

  // 删除条件行
  const handleDeleteConditionRow = (
    configId: string,
    groupId: string,
    conditionId: string,
  ) => {
    const updatedConfigs = configs.map((config) => {
      if (config.id === configId) {
        return {
          ...config,
          andGroups: config.andGroups.map((group) => {
            if (group.id === groupId) {
              return {
                ...group,
                conditions: group.conditions.filter(
                  (condition) => condition.id !== conditionId,
                ),
              };
            }
            return group;
          }),
        };
      }
      return config;
    });
    onChange(updatedConfigs);
  };

  // 更新条件行
  const handleUpdateConditionRow = (
    configId: string,
    groupId: string,
    conditionId: string,
    updatedCondition: ConditionRowState,
  ) => {
    const updatedConfigs = configs.map((config) => {
      if (config.id === configId) {
        return {
          ...config,
          andGroups: config.andGroups.map((group) => {
            if (group.id === groupId) {
              return {
                ...group,
                conditions: group.conditions.map((condition) =>
                  condition.id === conditionId ? updatedCondition : condition,
                ),
              };
            }
            return group;
          }),
        };
      }
      return config;
    });
    onChange(updatedConfigs);
  };

  // 更新结果配置
  const handleUpdateResultConfig = (configId: string, resultConfig: any) => {
    const updatedConfigs = configs.map((config) => {
      if (config.id === configId) {
        return {
          ...config,
          resultConfig,
        };
      }
      return config;
    });
    onChange(updatedConfigs);
  };

  return (
    <div className="condition-config">
      {configs.map((config, configIndex) => (
        <div key={config.id} className="condition-config-item">
          <div className="condition-config-item-header">
            <div className="condition-config-item-header-title">
              条件配置{configIndex + 1}
            </div>
            <Popconfirm
              title={`确定要删除吗？`}
              onConfirm={() => handleDeleteConditionConfig(config.id)}
              okText="确定"
              cancelText="取消"
            >
              <Button
                className="condition-config-item-header-btn"
                type="link"
                notspacelinkbtn
                disabled={loading || configs?.length <= 1}
              >
                删除
              </Button>
            </Popconfirm>
          </div>
          <div className="condition-config-item-content">
            <div className="condition-config-item-content-line">
              {config.andGroups?.length > 1 && (
                <div className="condition-config-item-content-orLine">
                  <div className="condition-config-item-content-orLine-text">
                    或
                  </div>
                </div>
              )}
              <div style={{ flex: 1 }}>
                {config.andGroups.map((group, groupIndex) => (
                  <div key={group.id} style={{ position: 'relative' }}>
                    <div
                      style={{
                        marginBottom:
                          groupIndex < config.andGroups.length - 1 ? 16 : 0,
                      }}
                    >
                      <AndGroup
                        group={group}
                        datasetColumns={datasetColumns}
                        loading={loading}
                        canDelete={canDeleteAndGroup(config)}
                        onUpdate={(updatedGroup) =>
                          handleUpdateAndGroup(
                            config.id,
                            group.id,
                            updatedGroup,
                          )
                        }
                        onDelete={() =>
                          handleDeleteAndGroup(config.id, group.id)
                        }
                        onAddCondition={() =>
                          handleAddConditionRow(config.id, group.id)
                        }
                        onDeleteCondition={(conditionId) =>
                          handleDeleteConditionRow(
                            config.id,
                            group.id,
                            conditionId,
                          )
                        }
                        onUpdateCondition={(conditionId, updatedCondition) =>
                          handleUpdateConditionRow(
                            config.id,
                            group.id,
                            conditionId,
                            updatedCondition,
                          )
                        }
                        lastSubmittedAt={lastSubmittedAt}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </div>

            <div className="condition-config-item-content-add">
              <Button
                type="link"
                notspacelinkbtn
                icon={<BLMIconFont type="BLM-ic-plus-o" />}
                onClick={() => handleAddAndGroup(config.id)}
                disabled={loading}
              >
                新增或条件
              </Button>
            </div>
            {/* 结果配置 */}
            {/* {selectedElement && ( */}
            <ResultConfig
              config={config.resultConfig}
              dropdownList={selectedElement?.dropdownList || []}
              branchType={branchType}
              onChange={(resultConfig) =>
                handleUpdateResultConfig(config?.id, resultConfig)
              }
              lastSubmittedAt={lastSubmittedAt}
            />
            {/* )} */}
          </div>
        </div>
      ))}

      <div className="condition-config-add">
        <Button
          type="link"
          notspacelinkbtn
          icon={<BLMIconFont type="BLM-ic-plus-o" />}
          onClick={handleAddConditionConfig}
          disabled={loading}
        >
          新增条件配置
        </Button>
      </div>
    </div>
  );
};

export default ConditionConfig;
