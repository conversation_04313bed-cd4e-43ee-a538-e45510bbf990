import React, { useState, useEffect, useCallback } from 'react';
import { <PERSON><PERSON>, Drawer, Space, message, BLMIconFont } from '@blmcp/ui';
import { getCurrnetPageId } from '@/pages/lego/utils';
import {
  BranchType,
  BranchConfigState,
  RuleBranchDrawerProps,
  RuleBranchDrawerState,
} from './types';
import {
  createDefaultBranchConfig,
  updateBranchConfigNames,
  transformToSaveData,
  transformFromSaveData,
  processDatasetColumns,
  validateBranchConfig,
} from './utils';
import {
  mockSaveBranchConditions,
  mockQueryReportElementList,
  mockQueryDatasetColumns,
} from './ruleBranchService';
import BranchConfig from './BranchConfig';

const RuleBranchDrawer: React.FC<RuleBranchDrawerProps> = ({
  open,
  initialData = [],
  onOk,
  onCancel,
}) => {
  const [state, setState] = useState<RuleBranchDrawerState>({
    branchConfigs: [createDefaultBranchConfig(1)],
    reportElementsCache: new Map(),
    datasetColumnsCache: new Map(),
    loading: false,
  });
  // 当前保存时间戳
  const [lastSubmittedAt, setLastSubmittedAt] = useState(0);
  console.log(state, 'state----1111', initialData);

  // 初始化数据回填
  useEffect(() => {
    const initializeData = async () => {
      console.log(
        open,
        initialData,
        'mockSaveBranchConditions----',
        state.reportElementsCache,
      );

      if (open && initialData && initialData.length > 0) {
        setState((prev) => ({ ...prev, loading: true }));

        try {
          // 先加载必要的缓存数据
          const newReportElementsCache = new Map(state.reportElementsCache);

          // 获取所有需要的分支类型
          const branchTypes = [
            ...new Set(initialData.map((item) => item.branchType)),
          ];

          // 为每个分支类型加载报告元素
          for (const branchType of branchTypes) {
            if (!newReportElementsCache.has(branchType)) {
              try {
                console.log(`加载分支类型 ${branchType} 的报告元素...`);
                const response = await mockQueryReportElementList({
                  reportId: getCurrnetPageId(),
                  branchType: branchType,
                });
                console.log(response, 'response----');
                if (response.code === 1) {
                  newReportElementsCache.set(branchType, response.data);
                  console.log(
                    `分支类型 ${branchType} 的报告元素加载成功:`,
                    response.data,
                  );
                }
              } catch (error) {
                console.error(
                  `加载分支类型 ${branchType} 的报告元素失败:`,
                  error,
                );
              }
            }
          }

          // 获取所有需要的数据集ID并加载字段数据
          const newDatasetColumnsCache = new Map(state.datasetColumnsCache);
          for (const branch of initialData) {
            const reportElements =
              newReportElementsCache.get(branch.branchType) || [];
            const selectedElement = reportElements.find(
              (el) => String(el.value) === String(branch.selectedId),
            );

            if (
              selectedElement &&
              selectedElement.datasetId &&
              !newDatasetColumnsCache.has(selectedElement.datasetId)
            ) {
              try {
                console.log(
                  `加载数据集 ${selectedElement.datasetId} 的字段...`,
                );
                const response = await mockQueryDatasetColumns({
                  datasetId: selectedElement.datasetId,
                });
                if (response.code === 1) {
                  const processedColumns = processDatasetColumns(response.data);
                  newDatasetColumnsCache.set(
                    selectedElement.datasetId,
                    processedColumns,
                  );
                  console.log(
                    `数据集 ${selectedElement.datasetId} 的字段加载成功:`,
                    processedColumns,
                  );
                }
              } catch (error) {
                console.error(
                  `加载数据集 ${selectedElement.datasetId} 的字段失败:`,
                  error,
                );
              }
            }
          }

          // 转换数据
          const configs = transformFromSaveData(
            initialData,
            newReportElementsCache,
          );
          console.log(
            configs,
            initialData,
            'mockSaveBranchConditions----转换完成',
            newReportElementsCache,
          );

          setState((prev) => ({
            ...prev,
            branchConfigs:
              configs.length > 0 ? configs : [createDefaultBranchConfig(1)],
            reportElementsCache: newReportElementsCache,
            datasetColumnsCache: newDatasetColumnsCache,
            loading: false,
          }));
        } catch (error) {
          console.error('数据回填失败:', error);
          setState((prev) => ({
            ...prev,
            branchConfigs: [createDefaultBranchConfig(1)],
            loading: false,
          }));
        }
      } else if (open) {
        // 重置为默认状态
        setState((prev) => ({
          ...prev,
          branchConfigs: [createDefaultBranchConfig(1)],
        }));
      }
    };

    initializeData();
  }, [open, initialData]);

  // 更新缓存
  const updateCache = useCallback(
    (
      type: 'reportElements' | 'datasetColumns',
      key: BranchType | number,
      data: any,
    ) => {
      setState((prev) => {
        const newState = { ...prev };
        if (type === 'reportElements') {
          newState.reportElementsCache = new Map(prev.reportElementsCache);
          newState.reportElementsCache.set(key as BranchType, data);
        } else {
          newState.datasetColumnsCache = new Map(prev.datasetColumnsCache);
          newState.datasetColumnsCache.set(key as number, data);
        }
        return newState;
      });
    },
    [],
  );

  // 添加分支配置
  const handleAddBranchConfig = () => {
    setState((prev) => {
      const newConfigs = [
        ...prev.branchConfigs,
        createDefaultBranchConfig(prev.branchConfigs.length + 1),
      ];
      return {
        ...prev,
        branchConfigs: newConfigs,
      };
    });
  };

  // 删除分支配置
  const handleDeleteBranchConfig = (configId: string) => {
    setState((prev) => {
      const filteredConfigs = prev.branchConfigs.filter(
        (config) => config.id !== configId,
      );
      const updatedConfigs = updateBranchConfigNames(filteredConfigs);
      return {
        ...prev,
        branchConfigs: updatedConfigs,
      };
    });
  };

  // 清空分支配置
  const handleClearBranchConfig = (configId: string) => {
    setState((prev) => {
      const updatedConfigs = prev.branchConfigs.map((config) => {
        if (config.id === configId) {
          const index = prev.branchConfigs.findIndex((c) => c.id === configId);
          return createDefaultBranchConfig(index + 1);
        }
        return config;
      });
      return {
        ...prev,
        branchConfigs: updatedConfigs,
      };
    });
  };

  // 更新分支配置
  const handleUpdateBranchConfig = (
    configId: string,
    updatedConfig: BranchConfigState,
  ) => {
    setState((prev) => {
      const updatedConfigs = prev.branchConfigs.map((config) =>
        config.id === configId ? updatedConfig : config,
      );
      return {
        ...prev,
        branchConfigs: updatedConfigs,
      };
    });
  };
  const handleSave = async () => {
    setLastSubmittedAt(Date.now());

    const validConfigs = state.branchConfigs.filter((config) =>
      validateBranchConfig(config),
    );

    if (validConfigs.length === 0) {
      // setValidationErrors(newValidationErrors);
      message.error('请继续配置分支逻辑');
      return;
    }

    if (validConfigs.length !== state.branchConfigs.length) {
      // setValidationErrors(newValidationErrors);
      message.error('请继续配置分支逻辑');
      return;
    }

    setState((prev) => ({ ...prev, loading: true }));
    console.log(state, 'state----2222');

    try {
      const saveData = transformToSaveData(
        state.branchConfigs,
        getCurrnetPageId(),
      );
      console.log(saveData, 'saveData-----');
      const response = await mockSaveBranchConditions(saveData);

      if (response.code === 1) {
        message.success('保存成功');
        onOk(saveData);
      } else {
        message.error(response.msg || '保存失败');
      }
    } catch (error) {
      console.error('保存失败:', error);
      message.error('保存失败');
    } finally {
      console.log(state, 'state----3333');
      setState((prev) => ({ ...prev, loading: false }));
    }
  };

  // 取消操作
  const handleCancel = () => {
    onCancel();
  };

  return (
    <Drawer
      closable
      destroyOnHidden
      title="分支配置"
      open={open}
      maskClosable={false}
      width={720}
      onClose={handleCancel}
      footer={
        <Space style={{ float: 'right' }}>
          <Button onClick={handleCancel}>取消</Button>
          <Button type="primary" onClick={handleSave} loading={state.loading}>
            保存
          </Button>
        </Space>
      }
    >
      <div>
        {/* 分支配置列表 */}
        {state.branchConfigs.map((config, index) => (
          <BranchConfig
            key={config.id}
            config={config}
            index={index}
            allConfigs={state.branchConfigs}
            reportElementsCache={state.reportElementsCache}
            datasetColumnsCache={state.datasetColumnsCache}
            onUpdate={(updatedConfig) =>
              handleUpdateBranchConfig(config.id, updatedConfig)
            }
            onDelete={() => handleDeleteBranchConfig(config.id)}
            onClear={() => handleClearBranchConfig(config.id)}
            onUpdateCache={updateCache}
            lastSubmittedAt={lastSubmittedAt}
          />
        ))}

        {/* 新增分支配置按钮 */}
        <div style={{ textAlign: 'center' }}>
          <Button
            type="dashed"
            icon={<BLMIconFont type="BLM-ic-plus-o" />}
            onClick={handleAddBranchConfig}
            disabled={state.loading}
            block
          >
            新增分支配置
          </Button>
        </div>
      </div>
    </Drawer>
  );
};

export default RuleBranchDrawer;
