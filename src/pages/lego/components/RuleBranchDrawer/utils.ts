import { useRef, useState, useCallback } from 'react';
// 生成简单的UUID替代方案，避免依赖外部库
const generateUUID = (): string => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0;
    const v = c === 'x' ? r : (r & 0x3) | 0x8;
    return v.toString(16);
  });
};
import {
  BranchType,
  CompareType,
  OperatorType,
  ActionType,
  BranchConfigState,
  ConditionConfigState,
  AndGroupState,
  ConditionRowState,
  ResultConfigState,
  DatasetColumn,
  DimensionInfo,
  MeasureGroup,
  DatasetColumnsResponse,
  BranchInfo,
  ConditionContent,
  ConditionGroup,
  ConditionChild,
  SaveBranchRequest,
} from './types';

// 常量定义
export const BRANCH_TYPE_OPTIONS = [
  { value: BranchType.FIELD_LEVEL, label: '字段级配置' },
  { value: BranchType.CHART_LEVEL, label: '图表级配置' },
];

export const COMPARE_TYPE_OPTIONS = [{ value: CompareType.EQ, label: '=' }];

export const RESULT_ACTION_OPTIONS_FIELD = [
  { value: ActionType.HIDE, label: '隐藏字段' },
];
export const RESULT_ACTION_OPTIONS_LABEL = [
  { value: ActionType.HIDE, label: '隐藏图表' },
];

/**
 * 生成唯一ID
 */
export const generateId = (): string => generateUUID();

/**
 * 创建默认的条件行状态
 */
export const createDefaultConditionRow = (): ConditionRowState => ({
  id: generateId(),
  column: null,
  compareType: CompareType.EQ,
  compareValue: '',
});

/**
 * 创建默认的且条件组状态
 */
export const createDefaultAndGroup = (): AndGroupState => ({
  id: generateId(),
  conditions: [createDefaultConditionRow()],
});
/**
 * 创建默认的结果配置状态
 */
export const createDefaultResultConfig = (): ResultConfigState => ({
  // 选择的字段列表，默认为空数组
  selectedFields: [],
  // 搜索文本，默认为空字符串
  searchText: '',
});

/**
 * 创建默认的条件配置状态
 */
export const createDefaultConditionConfig = (
  index: number,
): ConditionConfigState => ({
  id: generateId(),
  name: `条件配置${index}`,
  andGroups: [createDefaultAndGroup()],
  resultConfig: createDefaultResultConfig(),
});

/**
 * 创建默认的分支配置状态
 */
export const createDefaultBranchConfig = (
  index: number,
): BranchConfigState => ({
  id: generateId(),
  name: `设置分支条件${index}`,
  branchType: 2,
  selectedElement: null,
  conditionConfigs: [createDefaultConditionConfig(1)],
});

/**
 * 更新分支配置名称序号
 */
export const updateBranchConfigNames = (
  configs: BranchConfigState[],
): BranchConfigState[] => {
  return configs.map((config, index) => ({
    ...config,
    name: `设置分支条件${index + 1}`,
  }));
};

/**
 * 处理数据集字段数据
 * 取dimensionList中dataType不为2的 + measureList中category为"自建指标"的columnList中dataType为0的
 */
export const processDatasetColumns = (
  data: DatasetColumnsResponse,
): DatasetColumn[] => {
  const result: DatasetColumn[] = [];

  // 只需展示非时间维度字段
  if (data.dimensionList) {
    const validDimensions = data.dimensionList.filter(
      (item) => item.dataType !== 2,
    );
    result.push(...validDimensions);
  }
  return result;
};

/**
 * 过滤已选择的元素
 */
export const filterSelectedElements = <T extends { value: number }>(
  allElements: T[],
  selectedValues: number[],
): T[] => {
  return allElements.filter(
    (element) => !selectedValues.includes(element.value),
  );
};

/**
 * 获取已选择的元素值列表
 */
export const getSelectedElementValues = (
  configs: BranchConfigState[],
): number[] => {
  return configs
    .map((config) => config.selectedElement?.value)
    .filter((value): value is number => value !== undefined);
};

/**
 * 验证分支配置是否完整
 */
export const validateBranchConfig = (config: BranchConfigState): boolean => {
  // 检查基本配置
  if (!config.branchType || !config.selectedElement) {
    return false;
  }

  // 检查条件配置
  for (const conditionConfig of config.conditionConfigs) {
    // 检查且条件组
    for (const andGroup of conditionConfig.andGroups) {
      for (const condition of andGroup.conditions) {
        if (!condition.column || !condition.compareValue.trim()) {
          return false;
        }
      }
    }

    // 检查结果配置（可选，允许为空）
    // 结果配置不是必须的，所以这里不做强制验证
    if (conditionConfig.resultConfig?.selectedFields?.length === 0) {
      return false;
    }
  }

  return true;
};

/**
 * 检查是否可以删除且条件组
 */
export const canDeleteAndGroup = (
  conditionConfig: ConditionConfigState,
): boolean => {
  return conditionConfig.andGroups.length > 1;
};

/**
 * 检查是否可以删除分支配置
 */
export const canDeleteBranchConfig = (
  configs: BranchConfigState[],
): boolean => {
  return configs.length > 1;
};

/**
 * 转换组件状态为保存数据格式
 */
export const transformToSaveData = (
  configs: BranchConfigState[],
  reportId: string | null,
): SaveBranchRequest => {
  const branchInfo: BranchInfo[] = configs
    .filter((config) => validateBranchConfig(config))
    .map((config) => {
      const content: ConditionContent[] = config.conditionConfigs
        .map((conditionConfig) => {
          const condition: ConditionGroup[] = conditionConfig.andGroups
            .map((andGroup) => ({
              operator: OperatorType.AND,
              children: andGroup.conditions
                .filter((cond) => cond.column && cond.compareValue.trim())
                .map((cond) => ({
                  compareType: cond.compareType,
                  columnId: cond.column!.columnId,
                  columnTitle: cond.column!.title,
                  compareValue: cond.compareValue.trim(),
                })),
            }))
            .filter((group) => group.children.length > 0);

          return {
            operator: OperatorType.OR as OperatorType.OR,
            condition,
            operatorInfos: conditionConfig.resultConfig.selectedFields.map(
              (field) => ({
                nodeId: String(field.value),
                nodeName: field.label,
                operator: ActionType.HIDE,
                type: field?.type,
              }),
            ),
          };
        })
        .filter((content) => content.condition.length > 0);

      return {
        branchType: config.branchType!,
        selectedId: config.selectedElement!.value,
        content,
      };
    });

  return {
    reportId,
    branchInfo,
  };
};

/**
 * 从保存数据格式转换为组件状态
 */
export const transformFromSaveData = (
  branchInfo: BranchInfo[],
  reportElementsMap: Map<BranchType, any[]>,
): BranchConfigState[] => {
  if (!branchInfo || branchInfo.length === 0) {
    return [createDefaultBranchConfig(1)];
  }

  return branchInfo.map((branch, index) => {
    try {
      const reportElements = reportElementsMap.get(branch.branchType) || [];
      const selectedElement =
        reportElements.find(
          (el) => String(el.value) === String(branch.selectedId),
        ) || null; // 转换为字符串进行比较
      console.log(branch.content, 'branch.content----', selectedElement);
      const conditionConfigs: ConditionConfigState[] = (
        branch.content || []
      ).map((content, contentIndex) => ({
        id: generateId(),
        name: `条件配置${contentIndex + 1}`,
        andGroups: (content.condition || []).map((condGroup) => ({
          id: generateId(),
          conditions: (condGroup.children || []).map((child) => ({
            id: generateId(),
            column: {
              columnId: child.columnId,
              title: child.columnTitle,
              dataType: 0, // 默认值，实际应该从缓存中获取
              category: '',
              key: child.columnTitle,
            },
            compareType: child.compareType,
            compareValue: child.compareValue,
          })),
        })),
        resultConfig: {
          selectedFields: (content.operatorInfos || []).map((info) => ({
            value: info.nodeId,
            label: info.nodeName,
            type: info?.type,
          })),
          searchText: '',
        },
      }));
      console.log(branch.branchType, 'branchType----1111');
      return {
        id: generateId(),
        name: `设置分支条件${index + 1}`,
        branchType: branch.branchType,
        selectedElement,
        conditionConfigs:
          conditionConfigs.length > 0
            ? conditionConfigs
            : [createDefaultConditionConfig(1)],
      };
    } catch (error) {
      console.error('转换分支数据失败:', error);
      return createDefaultBranchConfig(index + 1);
    }
  });
};

interface FieldValidationOptions {
  /** 当前字段的值 */
  value: any;
  /** 全局上次点击保存的时间戳 */
  lastSubmittedAt: number | null;
  /** 校验文案 */
  message?: string;
}
/**
 * 模拟 antd Form.Item 的必填校验逻辑
 * 1. 用户刚创建的字段：初始不红
 * 2. 用户动过（touched）且值为空：红
 * 3. 点击过保存时刻之前就已存在的字段，如果仍为空：红
 */
export function useFormLikeValidation({
  value,
  lastSubmittedAt,
  message = '必填项不能为空',
}: FieldValidationOptions) {
  // 记录字段创建时间
  const createdAtRef = useRef<number>(Date.now());

  // 是否被用户操作过
  const [touched, setTouched] = useState(false);

  // const empty = isEmpty(value);
  const empty = !value;
  const existedWhenSubmitted =
    lastSubmittedAt !== null && createdAtRef.current <= lastSubmittedAt;

  const showError = empty && (touched || existedWhenSubmitted);

  const markTouched = useCallback(() => setTouched(true), []);

  return {
    status: showError ? 'error' : undefined, // 对应 antd v5 组件的 status
    help: showError ? message : undefined, // 对应 Form.Item 的 help
    markTouched, // 需要在控件的 onChange 里调用
  };
}
