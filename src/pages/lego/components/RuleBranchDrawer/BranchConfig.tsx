import React, { useState, useEffect, useMemo } from 'react';
import { <PERSON><PERSON>, Popconfirm, Typography } from '@blmcp/ui';
import { getCurrnetPageId } from '@/pages/lego/utils';
import {
  BranchType,
  BranchConfigState,
  ReportElement,
  DatasetColumn,
} from './types';
import {
  BRANCH_TYPE_OPTIONS,
  canDeleteBranchConfig,
  filterSelectedElements,
  createDefaultConditionConfig,
  createDefaultResultConfig,
} from './utils';
import {
  mockQueryReportElementList,
  mockQueryDatasetColumns,
} from './ruleBranchService';
import { processDatasetColumns } from './utils';
import ConditionConfig from './ConditionConfig';
import ValidatedSelect from './ValidatedSelect';
// import ResultConfig from './ResultConfig';

import './index.less';

const { Title } = Typography;

interface BranchConfigProps {
  config: BranchConfigState;
  index: number;
  allConfigs: BranchConfigState[];
  reportElementsCache: Map<BranchType, ReportElement[]>;
  datasetColumnsCache: Map<number, DatasetColumn[]>;
  onUpdate: (config: BranchConfigState) => void;
  onDelete: () => void;
  onClear: () => void;
  onUpdateCache: (
    type: 'reportElements' | 'datasetColumns',
    key: BranchType | number,
    data: any,
  ) => void;
  lastSubmittedAt: number;
}

// 分支配置
const BranchConfig: React.FC<BranchConfigProps> = ({
  config,
  index,
  allConfigs,
  reportElementsCache,
  datasetColumnsCache,
  onUpdate,
  onDelete,
  onClear,
  onUpdateCache,
  lastSubmittedAt,
}) => {
  const [loadingElements, setLoadingElements] = useState(false);
  const [loadingColumns, setLoadingColumns] = useState(false);

  // 计算可用的报告元素（排除已选择的）
  const availableElements = useMemo(() => {
    if (!config.branchType) return [];

    const allElements = reportElementsCache.get(config.branchType) || [];
    const selectedValues = allConfigs
      .filter((c) => c.id !== config.id && c.selectedElement)
      .map((c) => c.selectedElement!.value);

    return filterSelectedElements(allElements, selectedValues);
  }, [config.branchType, config.id, reportElementsCache, allConfigs]);

  // 获取数据集字段
  const datasetColumns = useMemo(() => {
    if (!config.selectedElement) return [];
    return datasetColumnsCache.get(config.selectedElement.datasetId) || [];
  }, [config.selectedElement, datasetColumnsCache]);

  // 处理分支类型变更
  const handleBranchTypeChange = async (branchType: BranchType) => {
    setLoadingElements(true);
    try {
      // 清空当前分支条件下所有内容，重置为初始状态
      const updatedConfig = {
        ...config,
        branchType,
        selectedElement: null,
        conditionConfigs: [createDefaultConditionConfig(1)],
      };
      onUpdate(updatedConfig);

      // 检查缓存
      if (!reportElementsCache.has(branchType)) {
        const response = await mockQueryReportElementList({
          reportId: getCurrnetPageId(),
          branchType: branchType,
        });
        if (response.code === 1) {
          onUpdateCache('reportElements', branchType, response.data);
        }
      }
    } catch (error) {
      console.error('获取报告元素失败:', error);
    } finally {
      setLoadingElements(false);
    }
  };

  // 处理元素选择变更
  const handleElementChange = async (
    elementValue: number | null | undefined,
  ) => {
    console.log(elementValue, 'elementValue----111updatedConfig----1111');
    if (elementValue) {
      const selectedElement = availableElements.find(
        (el) => el.value === elementValue,
      );
      if (!selectedElement) return;

      // 清空所有条件配置内容，只保留一组默认空的条件配置
      const updatedConfig = {
        ...config,
        selectedElement,
        conditionConfigs: [createDefaultConditionConfig(1)],
      };
      onUpdate(updatedConfig);

      // 获取数据集字段
      if (!datasetColumnsCache.has(selectedElement.datasetId)) {
        setLoadingColumns(true);
        try {
          const response = await mockQueryDatasetColumns({
            datasetId: selectedElement.datasetId,
          });
          if (response.code === 1) {
            const processedColumns = processDatasetColumns(response.data);
            onUpdateCache(
              'datasetColumns',
              selectedElement.datasetId,
              processedColumns,
            );
          }
        } catch (error) {
          console.error('获取数据集字段失败:', error);
        } finally {
          setLoadingColumns(false);
        }
      }
    } else {
      // 清空所有条件配置内容，只保留一组默认空的条件配置
      const updatedConfig = {
        ...config,
        selectedElement: null,
        conditionConfigs: [createDefaultConditionConfig(1)],
      };
      console.log(updatedConfig, 'updatedConfig----1111', elementValue);
      onUpdate(updatedConfig);
    }
  };

  // 处理条件配置更新
  const handleConditionConfigsUpdate = (conditionConfigs: any[]) => {
    onUpdate({
      ...config,
      conditionConfigs,
    });
  };
  // 移除自动加载逻辑，改为在主组件中处理
  const canDelete = canDeleteBranchConfig(allConfigs);
  const actionButtonText = canDelete ? '删除' : '清空';
  const actionHandler = canDelete ? onDelete : onClear;
  console.log(config, 'configs----2222', datasetColumns);
  useEffect(() => {
    if (!reportElementsCache.has(2)) {
      handleBranchTypeChange(2);
    }
  }, []);

  return (
    <div className="branch-config">
      <div className="branch-config-header">
        <div className="branch-config-header-title">{config.name}</div>
        <Popconfirm
          title={
            canDelete
              ? '确定要删除吗？'
              : `确定清空配置吗？清空后当前配置恢复初始态`
          }
          onConfirm={actionHandler}
          okText="确定"
          cancelText="取消"
        >
          <Button
            className="branch-config-header-btn"
            type="link"
            notspacelinkbtn
          >
            {actionButtonText}
          </Button>
        </Popconfirm>
      </div>
      {/* <Space direction="vertical" style={{ width: '100%' }} size="large"> */}
      <div>
        {/* 分支类型和元素选择 */}
        <div style={{ display: 'flex', marginBottom: 16 }}>
          <ValidatedSelect
            placeholder="请选择"
            style={{ width: 154, marginRight: 12 }}
            value={config.branchType}
            onChange={handleBranchTypeChange}
            options={BRANCH_TYPE_OPTIONS}
            lastSubmittedAt={lastSubmittedAt}
          />
          <ValidatedSelect
            placeholder="请选择"
            value={config.selectedElement?.value}
            onChange={handleElementChange}
            loading={loadingElements}
            style={{ flex: 1 }}
            // disabled={!config.branchType}
            showSearch
            allowClear
            filterOption={(input, option) =>
              (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
            }
            options={availableElements.map((el) => ({
              value: el.value,
              label: el.label,
            }))}
            lastSubmittedAt={lastSubmittedAt}
          />
        </div>

        {/* 条件配置 - 默认显示 */}
        <ConditionConfig
          configs={config.conditionConfigs}
          datasetColumns={datasetColumns}
          selectedElement={config.selectedElement}
          loading={loadingColumns}
          onChange={handleConditionConfigsUpdate}
          branchType={config.branchType}
          lastSubmittedAt={lastSubmittedAt}
        />
      </div>
    </div>
  );
};

export default BranchConfig;
