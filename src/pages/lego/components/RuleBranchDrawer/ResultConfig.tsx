import React, { useState, useMemo } from 'react';
import {
  Collapse,
  Select,
  Input,
  Table,
  Checkbox,
  Typography,
  BLMIconFont,
  Tooltip,
} from '@blmcp/ui';
import { SearchOutlined } from '@ant-design/icons';
import { ResultConfigState, DropdownOption, ActionType } from './types';
import {
  RESULT_ACTION_OPTIONS_LABEL,
  RESULT_ACTION_OPTIONS_FIELD,
  useFormLikeValidation,
} from './utils';

const { Panel } = Collapse;
const { Search } = Input;
const { Text } = Typography;

interface ResultConfigProps {
  config: ResultConfigState;
  dropdownList: DropdownOption[];
  branchType: 1 | 2 | null;
  onChange: (config: ResultConfigState) => void;
  lastSubmittedAt: number;
}

const ResultConfig: React.FC<ResultConfigProps> = ({
  config,
  dropdownList,
  branchType,
  onChange,
  lastSubmittedAt,
}) => {
  // const [activeKey, setActiveKey] = useState<string | string[]>(['1']);
  const [activeKey, setActiveKey] = useState<string | string[]>();
  console.log(config, 'config-----');

  // 过滤后的下拉列表数据
  const filteredData = useMemo(() => {
    if (!config.searchText.trim()) {
      return dropdownList;
    }
    return (
      dropdownList?.filter((item) =>
        item.label.toLowerCase().includes(config.searchText.toLowerCase()),
      ) || []
    );
  }, [dropdownList, config.searchText]);

  // 表格列定义
  const columns = [
    {
      title: branchType === 1 ? '图表名称' : '字段名称',
      dataIndex: 'label',
      key: 'label',
      render: (text: string) => <Text>{text}</Text>,
    },
    {
      title: '是否隐藏',
      dataIndex: 'value',
      key: 'selected',
      width: 100,
      render: (value: string | number) => {
        const isSelected = config.selectedFields.some(
          (field) => field.value === value,
        );
        // 计算剩余可隐藏的字段数量
        const unselectedCount =
          dropdownList.length - config.selectedFields.length;
        const isDisabled = !isSelected && unselectedCount <= 1;
        const checkboxElement = (
          <div>
            <Checkbox
              checked={isSelected}
              disabled={isDisabled}
              onChange={(e) => handleFieldSelection(value, e.target.checked)}
              style={{ marginRight: 8 }}
            />
            隐藏
          </div>
        );
        return isDisabled ? (
          <Tooltip title={'不支持所有字段隐藏'} placement="top">
            {checkboxElement}
          </Tooltip>
        ) : (
          checkboxElement
        );
      },
    },
  ];

  // 处理搜索文本变更
  const handleSearchChange = (value: string) => {
    onChange({
      ...config,
      searchText: value,
    });
  };

  // 处理字段选择
  const handleFieldSelection = (value: string | number, checked: boolean) => {
    const targetField = dropdownList.find((field) => field.value === value);
    if (!targetField) return;

    let updatedSelectedFields;
    if (checked) {
      // 检查是否只剩一个字段未被隐藏
      const unselectedCount =
        dropdownList.length - config.selectedFields.length;
      if (unselectedCount <= 1) {
        // 不允许隐藏最后一个字段
        return;
      }
      // 添加选中的字段
      updatedSelectedFields = [...config.selectedFields, targetField];
    } else {
      // 移除取消选中的字段
      updatedSelectedFields = config.selectedFields.filter(
        (field) => field.value !== value,
      );
    }

    onChange({
      ...config,
      selectedFields: updatedSelectedFields,
    });
  };

  const collapseItems = [
    {
      key: '1',
      label: branchType === 1 ? '图表级配置' : '字段级配置',
      extra: (
        <Select
          value={ActionType.HIDE}
          style={{ width: 142 }}
          options={
            branchType === 1
              ? RESULT_ACTION_OPTIONS_LABEL
              : RESULT_ACTION_OPTIONS_FIELD
          }
          onClick={(e) => e.stopPropagation()}
        />
      ),
      children: (
        <div>
          {/* 搜索框 */}
          <Input
            placeholder="输入字段名称查询"
            value={config.searchText}
            onChange={(e) => handleSearchChange(e.target.value)}
            prefix={<BLMIconFont type="BLM-ic-search-o" />}
            style={{ marginBottom: 24 }}
            allowClear
          />
          <Table
            columns={columns}
            dataSource={filteredData}
            rowKey="value"
            pagination={false}
            scroll={{ y: 200 }}
            bordered
          />
        </div>
      ),
    },
  ];
  const { status } = useFormLikeValidation({
    value: config.selectedFields.length,
    lastSubmittedAt,
  });

  return (
    <div className="result-config">
      <div className="result-config-title">结果配置</div>
      <Collapse
        activeKey={activeKey}
        onChange={setActiveKey}
        style={{ backgroundColor: '#f9f9f9' }}
        items={collapseItems}
      ></Collapse>
      {/* 校验提示 - 只在保存时显示 */}
      {status === 'error' && (
        <div style={{ color: '#ff4d4f', fontSize: '12px', marginTop: '8px' }}>
          {branchType === 1 ? '请勾选隐藏图表' : '请勾选隐藏字段'}
        </div>
      )}
    </div>
  );
};

export default ResultConfig;
