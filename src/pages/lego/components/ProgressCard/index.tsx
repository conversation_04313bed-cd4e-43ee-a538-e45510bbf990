import React, { useRef, useEffect, useState } from 'react';
import { Card as UICard, Tooltip, Popover } from '@blmcp/ui';
// import * as echarts from 'echarts';
import { CaretDownOutlined, CaretUpOutlined } from '@ant-design/icons';
import TooltipIcon from './image/tooltip.svg';
import DrageIcon from './image/drag.svg';
import './index.less';
import { getTextyWidthAndHeight } from '../../utils/css';

export const ProgressCard = ({ dataSource, width }: any) => {
  // 计算value的宽度
  // 如果没有出现省略号，则不显示tooltip
  const showValueToolTip =
    getTextyWidthAndHeight(
      dataSource?.value?.toLocaleString() || '-',
      {
        float: 'left',
        'font-size': '28px',
        'font-weight': '700',
        'white-space': 'nowrap',
        'font-family': 'PingFang SC',
      },
      'div',
    ).width >
    (width - 6) * 0.55;
  return (
    <div className="lego-card-com-wrap" style={{ height: '100%' }}>
      <UICard style={{ width: '100%', height: '100%' }}>
        <div className="lego-card-title">
          <DrageIcon style={{ marginRight: '4px', verticalAlign: 'sub' }} />
          {dataSource?.measureInfo?.[0]?.title || '-'}
          {
            // 按产品要求，没有toolTip的时候不显示
            dataSource.toolTip ? (
              <Tooltip title={dataSource.toolTip}>
                <span
                  style={{
                    marginLeft: '3px',
                    position: 'relative',
                    top: '2px',
                  }}
                >
                  <TooltipIcon></TooltipIcon>
                </span>
              </Tooltip>
            ) : undefined
          }
        </div>
        <div style={{ paddingLeft: '6px' }}>
          <Tooltip
            title={
              showValueToolTip ? dataSource?.value?.toLocaleString() || '-' : ''
            }
          >
            <div className="lego-card-measure-value">
              {dataSource?.value?.toLocaleString() || '-'}
            </div>
          </Tooltip>
        </div>
      </UICard>
    </div>
  );
};
