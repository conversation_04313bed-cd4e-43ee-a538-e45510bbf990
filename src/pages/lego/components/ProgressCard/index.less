.lego-card-com-wrap {
  * {
    font-family: PingFang SC;
  }

  .ant-card-bordered {
    border: none;

    .ant-card-body {
      padding: 0;
      height: 100%;
    }
  }

  .lego-card-title {
    font-size: 13px;
    color: rgba(0, 0, 0, 0.9);
    line-height: 20px;
  }

  .lego-card-measure-value {
    font-size: 28px;
    font-weight: 700;
    color: rgba(0, 0, 0, 0.9);
    float: left;
    max-width: 55%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .lego-card-measure-trend {
    margin-top: 10px;
    height: 40px;
    float: right;
    width: 45%;
  }

  .lego-card-measure-value-compare {
    padding-left: 6px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.6);
    line-height: 18px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .lego-card-measure-value-explain {
    margin: 6px 0 0 0;
    padding: 10px;
    border-radius: 6px;
    background-color: #F5F6F7;
    font-size: 12px;
    min-height: calc(100% - 95px);

    &.tooltip:hover {
      background: #EEEEF0;
    }

    .lego-card-measure-value-explain-text {
      display: -webkit-box;
      overflow: hidden;
      line-height: 18px;
      // -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      text-overflow: ellipsis;
      white-space: normal !important;
      word-wrap: break-word;
    }
  }
}
