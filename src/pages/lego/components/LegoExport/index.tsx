import { BLMExport } from '@blmcp/peento-businessComponents';
import LegoPublishExport from '@blm/bi-lego-sdk/dist/es/LegoPublishExport';
import request from '@/utils/request';
import { legoGeneralizeSwitch } from '@/utils/sceneSwitch';

import './index.less';

interface LegoExportProps {
  searchParams: any;
  otherParams: any;
  handleExportBeforeOpen?: any;
}

export const LegoExport = ({
  searchParams,
  otherParams,
  handleExportBeforeOpen,
  uuid,
}: LegoExportProps) => {
  return (
    <div className="lego-exp">
      {legoGeneralizeSwitch() ? (
        <LegoPublishExport
          uuid={uuid}
          searchParams={searchParams}
          otherParams={otherParams}
          handleExportBeforeOpen={handleExportBeforeOpen}
        />
      ) : (
        <BLMExport
          request={request}
          title={'数据乐高导出'}
          exportType={9} // ❗️指定为BI数据乐高导出类型
          searchParams={searchParams}
          otherParams={otherParams}
          onBeforeOpen={handleExportBeforeOpen}
        />
      )}
    </div>
  );
};
