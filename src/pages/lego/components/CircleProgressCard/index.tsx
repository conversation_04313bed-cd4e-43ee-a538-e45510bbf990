import React from 'react';
import { Card as UICard, Tooltip, Progress } from '@blmcp/ui';
import TooltipIcon from './image/tooltip.svg';
import DrageIcon from './image/drag.svg';
import './index.less';


export const CircleProgressCard = ({ dataSource, height, width }: any) => {
  return (
    <div className="lego-circle-progress-card-com-wrap" style={{ height: '100%' }}>
      <UICard style={{ width: '100%', height: '100%' }}>
        <div className="lego-circle-progress-card-title">
          <DrageIcon style={{ marginRight: '4px', verticalAlign: 'sub' }} />
          {dataSource?.measureInfo?.[0]?.title || '-'}
          {
            // 按产品要求，没有toolTip的时候不显示
            dataSource.toolTip ?
              <Tooltip title={dataSource.toolTip}>
                <span
                  style={{ marginLeft: '3px', position: 'relative', top: '2px' }}
                >
                  <TooltipIcon></TooltipIcon>
                </span>
              </Tooltip> : undefined
          }
        </div>
        <div className='lego-circle-progress-card-circle-progess'>
          <Progress type='circle' percent={20} size={Math.min(height, width)}></Progress>
        </div>
      </UICard>
    </div >
  );
};
