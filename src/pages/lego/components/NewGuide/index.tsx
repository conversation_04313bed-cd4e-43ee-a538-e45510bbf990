import Joyride, { Step } from "react-joyride";
import { doneStatus } from '@/pages/lego/components/GuideTooltip/config';
import { GuideTooltip } from "../GuideTooltip"

interface NewGuideProps {
  steps: Step;
  visible?: boolean;
  localKey: string;

}
export const NewGuide = ({ steps, visible = true, localKey }: NewGuideProps) => {
  return visible && localStorage.getItem(localKey) !== 'done' ?
    <Joyride
      steps={steps}
      tooltipComponent={GuideTooltip}
      spotlightPadding={0}
      scrollDuration={0}
      disableBeacon={true}
      showSkipButton={true}
      floaterProps={{
        styles: {
          arrow: {
            length: 8,
            spread: 14
          },
          floaterWithAnimation: { transition: "none" }
        }
      }}
      callback={(item) => {
        if (doneStatus.includes(item.action)) {
          localStorage.setItem(localKey, 'done');
        }
      }}
      run={true}
    /> : null
}
