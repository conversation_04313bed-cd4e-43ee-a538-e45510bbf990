import { useEffect, useRef } from 'react';
import { ComponentProps } from '@/pages/lego/type';
import echarts from '../echarts';
import { Colors } from '../constant';
import './index.less';

export const PieChart = ({ dataSource, useResize }: ComponentProps<any>) => {
  const ref = useRef<HTMLDivElement>(null);
  const { data, text, total } = dataSource;
  const chart = useRef<any>();

  useEffect(() => {
    return useResize(function () {
      chart?.current?.resize?.();
    });
  }, [useResize]);

  useEffect(() => {
    // 基于准备好的dom，初始化echarts实例
    chart.current = echarts().init(ref.current);
    chart.current.setOption({
      color: Colors,
      title: {
        text: '',
        subtext: '',
        left: 'center',
      },
      tooltip: {
        trigger: 'item',
        borderColor: '#fff',
        formatter: function (param: any) {
          const seriesName = param.seriesName;
          const matchColor = 0;
          const color =
            (matchColor && matchColor[1]) ||
            Colors[param.dataIndex % Colors.length];

          // 新增tooltip中显示数值和百分比
          return `<div style="z-index:200">${seriesName}<br /> <span style="display:inline-block;width: 8px;height: 8px;border-radius: 2px;background-color: ${color}"></span> <span style="margin-right:20px;">${param.name}</span>${param?.data?.valueText}</div>`;
        },
      },

      legend: {
        orient: 'horizontal',
        left: 'left',
        top: 'top',
        type: 'scroll',
        itemHeight: 8,
        itemWidth: 8,
      },
      // 在圆环中心显示总值
      graphic: {
        type: 'text',
        left: 'center',
        top: 'center',
        z: 100,
        style: {
          text: total,
          textAlign: 'center',
          rich: {
            label: {
              fontSize: 12,
              fontWeight: 'normal',
              fill: '#999',
              lineHeight: 20,
            },
            value: {
              fontSize: 16,
              fontWeight: 'bold',
              fill: '#333',
              lineHeight: 24,
            },
          },
        },
      },
      series: [
        {
          name: text,
          type: 'pie',
          radius: ['40%', '70%'],
          data: data,
          top: 20,
          label: {
            color: 'inherit',
            formatter: function (param: any) {
              // 新增在标签中显示数值和百分比
              return `${param.name}: ${param?.data?.valueText}`;
            },
          },
          itemStyle: {
            borderRadius: 2,
            borderColor: '#fff',
            borderWidth: 2,
          },
        },
      ],
    });
  }, [data, text, total]);

  return (
    <div className="lego-pie-chart-wrap">
      <div ref={ref} style={{ width: '100%', height: '100%' }}></div>
    </div>
  );
};
