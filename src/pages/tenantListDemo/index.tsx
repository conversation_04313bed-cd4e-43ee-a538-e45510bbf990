import React, { useState, useRef } from 'react';
import { BLMTenantList } from '@blmcp/peento-businessComponents';
import { Button, Select, Input, Form } from '@blmcp/ui';

const TenantListDemo = () => {
  const refTenant = useRef();
  // 非业务属性
  const [multiple, setMultiple] = useState(false); // 是否为多选
  const [filterable, setFilterable] = useState(true); // 是否为可以过滤
  const [clearable, setClearable] = useState(true); // 是否为可以清除
  const [defaultOption, setDefaultOption] = useState({}); //扩展项
  // 非跨平台业务属性
  const [isAccountPermission, setIsAccountPermission] = useState(false); // 是否当前账号下拥有的租户权限
  const [isBusinessGroup, setIsBusinessGroup] = useState(false); // 是否按业务线
  const [businessGroup, setBusinessGroup] = useState([]); // 业务线
  const [businessType, setBusinessType] = useState([]); // 业务属性
  const [platformUsageList, setPlatformUsageList] = useState([]); // 平台用途
  const [platformStatusEnum, setPlatformStatusEnum] = useState([]); // 平台用途

  // 跨平台业务属性

  const [isCrossPlatform, setIsCrossPlatform] = useState(false); // 是否跨平台
  const [tenantsCrossPlatform, setTenantsCrossPlatform] = useState(0); // 跨平台属性
  const [businessTypeCrossPlatform, setBusinessTypeCrossPlatform] = useState(
    [],
  ); // 跨平台属性
  const [managerIdCrossPlatform, setManagerIdCrossPlatform] = useState([]); // 管理方ID

  const [managerIdCrossPlatformValue, setManagerIdCrossPlatformValue] =
    useState('');

  const onChange = (value, option) => {
    console.log(value, option);
  };
  const getOrgData = () => {
    console.log(refTenant.current?.getOrgData());
  };
  return (
    <>
      <p style={{ color: 'darkorchid' }}>Demo</p>
      <div style={{ marginBottom: '10px' }}>
        <div style={{ marginBottom: '10px' }}>
          <Button onClick={getOrgData}>获取接口源数据（MOCK）</Button>
          <span style={{ marginLeft: '10px' }}></span>
          <Button
            onClick={() => {
              console.count('下拉所有数据为：');
              console.log(refTenant.current?.getOptions());
            }}
          >
            获取下拉所有数据
          </Button>
          <span style={{ marginLeft: '10px' }}></span>
          <Button
            onClick={() => {
              console.count('获取当前选中的数据：');
              console.log(refTenant.current?.getSelectedOptions());
            }}
          >
            获取当前选中的数据：
          </Button>
          <br />
        </div>
        <p style={{ marginBottom: '10px' }}></p>
        <p style={{ fontWeight: 'bold' }}>非业务属性</p>
        <span style={{ marginLeft: '10px', marginRight: '10px' }}>
          multiple:
        </span>
        <Select
          style={{ width: 120 }}
          options={[
            { value: 0, label: 'false' },
            { value: 1, label: 'true' },
          ]}
          defaultValue={0}
          onChange={(val) => {
            setMultiple(val === 1 ? true : false);
          }}
          allowClear
        ></Select>

        <span style={{ marginLeft: '10px', marginRight: '10px' }}>
          filterable:
        </span>
        <Select
          style={{ width: 120 }}
          options={[
            { value: 0, label: 'false' },
            { value: 1, label: 'true' },
          ]}
          defaultValue={1}
          onChange={(val) => {
            setFilterable(val === 1 ? true : false);
          }}
          allowClear
        ></Select>
        <br />
        <p style={{ marginBottom: '10px' }}></p>
        <span style={{ marginLeft: '10px', marginRight: '10px' }}>
          clearable:
        </span>
        <Select
          style={{ width: 120 }}
          options={[
            { value: 0, label: 'false' },
            { value: 1, label: 'true' },
          ]}
          defaultValue={1}
          onChange={(val) => {
            setClearable(val === 1 ? true : false);
          }}
          allowClear
        ></Select>
        <span style={{ marginLeft: '10px', marginRight: '10px' }}>
          defaultOption:
        </span>
        <Select
          style={{ width: 260 }}
          options={[
            { value: 0, label: `{label: '不限城市', value: 'all'}` },
            { value: 1, label: `{label: '全部', value: 'all'}` },
          ]}
          onChange={(val) => {
            setDefaultOption(
              val === 0
                ? { label: '不限城市', value: 'all' }
                : { label: '全部', value: 'all' },
            );
          }}
          allowClear
        ></Select>
        <p style={{ marginBottom: '10px' }}></p>
        <p style={{ fontWeight: 'bold' }}>非跨平台业务属性</p>
        <span style={{ marginLeft: '10px', marginRight: '10px' }}>
          isAccountPermission:
        </span>
        <Select
          style={{ width: 120 }}
          options={[
            { value: 0, label: 'false' },
            { value: 1, label: 'true' },
          ]}
          defaultValue={0}
          onChange={(val) => {
            setIsAccountPermission(val === 1 ? true : false);
          }}
          allowClear
        ></Select>
        <span style={{ marginLeft: '10px', marginRight: '10px' }}>
          isBusinessGroup:
        </span>
        <Select
          style={{ width: 120 }}
          options={[
            { value: 0, label: 'false' },
            { value: 1, label: 'true' },
          ]}
          defaultValue={0}
          onChange={(val) => {
            setIsBusinessGroup(val === 1 ? true : false);
          }}
          allowClear
        ></Select>

        <br />
        <br />
        <span style={{ marginLeft: '10px', marginRight: '10px', color: 'red' }}>
          businessGroup:
        </span>
        <Select
          mode="multiple"
          style={{ width: 180 }}
          options={[
            { value: 1, label: '白龙马-1' },
            { value: 4, label: '龙驹代驾-4' },
            { value: 5, label: '白驹易行-5' },
          ]}
          onChange={(val) => {
            console.log(val);
            setBusinessGroup(val);
          }}
          allowClear
        ></Select>

        <span style={{ marginLeft: '10px', marginRight: '10px', color: 'red' }}>
          businessType:
        </span>
        <Select
          mode="multiple"
          style={{ width: 180 }}
          options={[
            { value: 1, label: '网约车-1' },
            { value: 4, label: '代驾-4' },
            { value: 5, label: '定制客运-5' },
            { value: 8, label: '配送-8' },
          ]}
          onChange={(val) => {
            setBusinessType(val);
          }}
          allowClear
        ></Select>
        <br />
        <br />

        <span style={{ marginLeft: '10px', marginRight: '10px', color: 'red' }}>
          platformUsageList:
        </span>
        <Select
          mode="multiple"
          style={{ width: 180 }}
          options={[
            { value: 1, label: '常规-测试-1' },
            { value: 2, label: '常规-客户-2' },
            { value: 5, label: '空壳-测试-5' },
            { value: 6, label: '空格-客户-6' },
          ]}
          onChange={(val) => {
            setPlatformUsageList(val);
          }}
          allowClear
        ></Select>

        <span style={{ marginLeft: '10px', marginRight: '10px' }}>
          <span style={{ color: 'red' }}>platformStatusEnum</span>:
        </span>
        <Select
          mode="multiple"
          style={{ width: 180 }}
          options={[
            { value: 0, label: '下线-0' },
            { value: 1, label: '在线-1' },
            { value: 2, label: '启动-2' },
            { value: 3, label: '停滞-3' },
          ]}
          onChange={(val) => {
            setPlatformStatusEnum(val);
          }}
          allowClear
        ></Select>

        <p style={{ fontWeight: 'bold' }}>跨平台业务属性</p>
        <span style={{ marginLeft: '10px', marginRight: '10px' }}>
          isCrossPlatform:
        </span>
        <Select
          style={{ width: 120 }}
          options={[
            { value: 0, label: 'false' },
            { value: 1, label: 'true' },
          ]}
          defaultValue={0}
          onChange={(val) => {
            setIsCrossPlatform(val === 1 ? true : false);
          }}
          allowClear
        ></Select>

        <span style={{ marginLeft: '10px', marginRight: '10px' }}>
          <span style={{ color: 'red' }}>businessTypeCrossPlatform</span>:
        </span>
        <Select
          mode="multiple"
          style={{ width: 180 }}
          options={[
            { value: 1, label: '网约车-1' },
            { value: 2, label: '趣接单-2' },
            { value: 3, label: '巡改网-3' },
          ]}
          onChange={(val) => {
            setBusinessTypeCrossPlatform(val);
          }}
        ></Select>
        <br />
        <p style={{ marginBottom: '10px' }}></p>

        <span style={{ marginLeft: '10px', marginRight: '10px' }}>
          tenantsCrossPlatform:
        </span>
        <Select
          style={{ width: 120 }}
          options={[
            { value: 0, label: '0' },
            { value: 1, label: '1' },
          ]}
          defaultValue={0}
          onChange={(val) => {
            setTenantsCrossPlatform(val);
          }}
          allowClear
        ></Select>
        <span style={{ marginLeft: '10px', marginRight: '10px' }}>
          <span style={{ color: 'red' }}>managerIdCrossPlatform</span>:
        </span>
        <Input
          style={{ width: '120px' }}
          value={managerIdCrossPlatformValue}
          onChange={(event) => {
            setManagerIdCrossPlatformValue(event.target.value);
          }}
        />
        <Button
          onClick={() => {
            console.log(managerIdCrossPlatformValue);
            setManagerIdCrossPlatform(parseInt(managerIdCrossPlatformValue));
          }}
        >
          按管理方id查询租户
        </Button>
      </div>
      <p style={{ color: 'blue' }}>租户列表实例↓↓↓↓</p>
      <BLMTenantList
        multiple={multiple}
        filterable={filterable}
        clearable={clearable}
        defaultOption={defaultOption}
        isAccountPermission={isAccountPermission}
        isBusinessGroup={isBusinessGroup}
        businessGroup={businessGroup}
        businessType={businessType}
        platformUsageList={platformUsageList}
        platformStatusEnum={platformStatusEnum}
        isCrossPlatform={isCrossPlatform}
        businessTypeCrossPlatform={businessTypeCrossPlatform}
        tenantsCrossPlatform={tenantsCrossPlatform}
        managerIdCrossPlatform={managerIdCrossPlatform}
        ref={refTenant}
        onChange={onChange}
      ></BLMTenantList>
    </>
  );
};

const SingleDemo = () => {
  const onChange = (value, option) => {
    console.count('多选onChange');
    console.log(value, option);
  };
  return (
    <div>
      <p style={{ color: 'darkorchid' }}>单选</p>
      <div style={{ width: '74px' }}>
        <BLMTenantList onChange={onChange}></BLMTenantList>
      </div>
      <BLMTenantList onChange={onChange} width={375}></BLMTenantList>
    </div>
  );
};

const MDemo = () => {
  const onChange = (value, option) => {
    console.count('多选onChange');
    console.log(value, option);
  };
  return (
    <div>
      <p style={{ color: 'darkorchid' }}>多选</p>
      <div style={{ width: '74px' }}>
        <BLMTenantList multiple={true} onChange={onChange}></BLMTenantList>
      </div>
      <BLMTenantList
        multiple={true}
        onChange={onChange}
        width={375}
      ></BLMTenantList>
    </div>
  );
};

const HuiDemo = () => {
  const onChange = (value, option) => {
    console.log(value, option);
  };
  return (
    <>
      <p style={{ color: 'darkorchid' }}>回显</p>
      <BLMTenantList
        value={[805739, 805741, 805565]}
        multiple={true}
        onChange={onChange}
        width={300}
      ></BLMTenantList>
    </>
  );
};

const RefDemo = () => {
  const tenantRef = useRef();
  const tenantRefSingle = useRef();
  const onChange = (value, option) => {
    console.log(value, option);
  };
  const getOptions = (flag) => {
    const curRef = flag === true ? tenantRefSingle : tenantRef;
    console.log(curRef?.current?.getOptions());
  };
  const getSelectedOptions = (flag) => {
    const curRef = flag === true ? tenantRefSingle : tenantRef;
    console.log(curRef?.current?.getSelectedOptions());
  };
  const getComponentRef = (flag) => {
    const curRef = flag === true ? tenantRefSingle : tenantRef;
    console.log(curRef?.current?.getComponentRef());
  };
  return (
    <>
      <p style={{ color: 'darkorchid' }}>Ref使用方法</p>
      <BLMTenantList
        ref={tenantRefSingle}
        value={805739}
        onChange={onChange}
        width={300}
      ></BLMTenantList>
      <br />
      <br />
      <Button onClick={getOptions.bind(null, true)}>
        获取下拉列表所有数据
      </Button>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <Button onClick={getSelectedOptions.bind(null, true)}>
        获取当前选中的数据
      </Button>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <Button onClick={getComponentRef.bind(null, true)}>
        获取组件内部Ref
      </Button>
      <br />
      <br />
      <p>多选</p>
      <BLMTenantList
        ref={tenantRef}
        value={[805739, 805741]}
        multiple={true}
        onChange={onChange}
        width={300}
      ></BLMTenantList>
      <br />
      <br />
      <Button onClick={getOptions}>获取下拉列表所有数据</Button>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <Button onClick={getSelectedOptions}>获取当前选中的数据</Button>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <Button onClick={getComponentRef}>获取组件内部Ref</Button>
    </>
  );
};

const FromDemo = () => {
  const [tenantForm] = Form.useForm(); // 表单
  return (
    <>
      <p style={{ color: 'darkorchid' }}>配合form 使用</p>
      <Form form={tenantForm} colon={false}>
        <Form.Item label="租户列表一：" required name="tenantFirst">
          <BLMTenantList />
        </Form.Item>
        <Form.Item label="租户列表二：" required name="tenantSecond">
          <BLMTenantList width={300} multiple={true} />
        </Form.Item>
      </Form>
      <br />
      <br />
      <Button
        onClick={() => {
          console.log(tenantForm.getFieldsValue());
        }}
      >
        获取当前选中值
      </Button>
      &nbsp;&nbsp;&nbsp;&nbsp;
      <Button
        onClick={() => {
          console.log(tenantForm);
          tenantForm.setFieldValue('tenantFirst', 805753);
          tenantForm.setFieldValue('tenantSecond', [805751, 805745]);
        }}
      >
        设置值
      </Button>
    </>
  );
};

const RenderDemo = () => {
  return (
    <div>
      <TenantListDemo></TenantListDemo>
      <div style={{ height: '10px' }}></div>
      <SingleDemo></SingleDemo>
      <MDemo></MDemo>
      <HuiDemo></HuiDemo>
      <RefDemo></RefDemo>
      <FromDemo></FromDemo>
    </div>
  );
};

export default RenderDemo;
