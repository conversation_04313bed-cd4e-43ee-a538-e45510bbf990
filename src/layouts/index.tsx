import React, { useEffect, useState } from 'react';
import { Outlet } from 'umi';
import './index.less';

const REACT_MASK_STATUS_CHANGE = 'REACT_MASK_STATUS_CHANGE'; //脱敏状态改变通知常量

// 通用layout页面入口文件，可做通用逻辑处理
const BasicLayout: React.FC<any> = () => {
  const [layoutTime, setLayoutTime] = useState(0);

  const updatePage = (event: any) => {
    const newObject = (event && event.detail) || {};
    const { needRefresh } = newObject || {};
    if (needRefresh) {
      //刷新子页面
      const now: number = Date.now();
      setLayoutTime(now);
    }
  };

  useEffect(() => {
    window.addEventListener(REACT_MASK_STATUS_CHANGE, updatePage);
    return () => {
      window.removeEventListener(REACT_MASK_STATUS_CHANGE, updatePage);
    };
  }, []);

  return (
    <div className="sub-app-basic-layout" key={layoutTime}>
      <Outlet />
    </div>
  );
};

export default BasicLayout;
