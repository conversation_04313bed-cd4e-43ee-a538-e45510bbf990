declare global {
  interface Window {
    BlmUtils: any;
    blmcpUi: any;
    blmBusinessComponents: any;
    mobileLib: any;
    AliLowCodeEngine: any;
    $legoAssets: any;
  }
}

// 入口
import * as legoRequest from '@blmcp/peento-request';
// @ts-expect-error
import * as BlmUtils from 'blm-utils';
import dayjs from 'dayjs';
import localeData from '@/utils/dayjs_locale';

localeData(undefined, dayjs, dayjs);

if (process.env.NODE_ENV === 'development') {
  window.BlmUtils = BlmUtils;

  const originalConsoleError = console.error;
  console.error = (message) => {
    if (/Warning: React/.test(message)) {
      return; // 忽略这个特定的警告
    }
    originalConsoleError(message); // 其他错误正常打印
  };
}
// 挂载全局变量
window.blmRequest = legoRequest;
window.legoRequest = legoRequest; // 下期干掉
