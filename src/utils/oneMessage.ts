import { message } from '@blmcp/ui';

function wrapper(key: string) {
  const map: any = {};
  return function (...arg: any[]) {
    let message = typeof arg[0] === 'string' ? arg[0] : arg[0].content;
    if (!map[message]) {
      map[message] = true;
      (message as any)[key](...arg);
      setTimeout(() => {
        map[message] = false;
      }, 3000);
    }
  };
}

let _message: any = {};

for (let k in message) {
  if (typeof (message as any)[k] === 'function') {
    _message[k] = wrapper(k);
  }
}

export default _message;
