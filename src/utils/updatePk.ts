import axios from 'axios';
import md5 from 'md5';
import uuid from 'node-uuid';
import { apiMap as newAPiMap } from './oriAndCategoryFlag';

const pkLInkAPIProject = 'leopard-web-qbi';

const API = [
  '/admin/v1/ai/chart-manager/copyTemplate', // 复制模版
  '/bos/admin/v1/ai/chart-manager/copyTemplate',
  '/admin/v1/ai/chart-manager/queryElementResultData', // 数据获取
  '/admin/v1/ai/chart-manager/v2/queryElementResultData',
  '/bos/admin/v1/ai/chart-manager/queryElementResultData',
  '/admin/v1/ai/chart-manager/queryReportLayoutInfo', // 报告查询
  '/admin/v1/ai/chart-manager/v2/queryReportLayoutInfo',
  '/bos/admin/v1/ai/chart-manager/queryReportLayoutInfo',
  '/admin/v1/ai/chart-manager/exchangeOwner', // 转让
  '/bos/admin/v1/ai/chart-manager/exchangeOwner',
  '/admin/v1/ai/chart-manager/addShares', // 分享
  '/bos/admin/v1/ai/chart-manager/addShares',
  '/admin/v2/ai/inspire/marketing/attr/queryRuleConfigByAdcode',
  '/admin/v2/ai/inspire/marketing/attr/queryBaseCntOrderDoneChartReport',
  '/admin/v2/ai/inspire/marketing/attr/queryMultiCityChartReport',
  '/admin/v2/ai/inspire/marketing/attr/queryRuleConfigList',
  '/admin/v2/ai/inspire/marketing/attr/addRuleConfig',
  '/admin/v2/ai/inspire/marketing/attr/updateRuleConfig',
  '/admin/v2/ai/inspire/marketing/attr/deleteRuleConfig',
  '/admin/v2/ai/inspire/marketing/attr/addRuleConfigExistCheck',
  '/admin/v2/ai/inspire/marketing/attr/queryBaseCntOrderDoneReport',
  '/admin/v2/ai/inspire/marketing/attr/queryLayerChartsByPath',
  '/admin/v2/ai/inspire/marketing/attr/queryAllLayerChartsByPath',
  '/admin/v2/ai/inspire/marketing/attr/queryCityDetailList',
  '/admin/v2/ai/inspire/marketing/attr/queryCityDetailReport',
  '/bos/admin/v1/ai/marketing/attr/addRuleConfigExistCheck',
  '/bos/admin/v1/ai/marketing/attr/addRuleConfig',
  '/bos/admin/v1/ai/marketing/attr/updateRuleConfig',
  '/bos/admin/v1/ai/marketing/attr/deleteRuleConfig',
  '/bos/admin/v1/ai/marketing/attr/queryRuleConfigList',
  '/bos/admin/v1/ai/marketing/attr/queryRuleConfigByIdOrAdcode',
  '/bos/admin/v1/ai/marketing/attr/queryRuleConfigByAdcode',
  '/bos/admin/v1/ai/marketing/attr/queryBaseCntOrderDoneChartReport',
  '/bos/admin/v1/ai/marketing/attr/queryBaseCntOrderDoneReport',
  '/bos/admin/v1/ai/marketing/attr/queryLayerChartsByPath',
  '/bos/admin/v1/ai/marketing/attr/queryAllLayerChartsByPath',
  '/bos/admin/v1/ai/marketing/attr/queryMultiCityChartReport',
  '/bos/admin/v1/ai/marketing/attr/queryCityDetailList',
  '/bos/admin/v1/ai/marketing/attr/queryCityDetailReport',
];

export default function (dev: string, PK: any[]) {
  const hostIp =
    dev === 'pre'
      ? 'https://admin-pre.yueyuechuxing.cn'
      : 'https://admin-daily.yueyuechuxing.cn';
  const apiMap: any = {};
  const APIs = API.concat(
    API.filter((v) => (newAPiMap as any)[v]).map((v) => (newAPiMap as any)[v]),
  );
  for (let i = 0; i < APIs.length; i++) {
    const pk = PK.map((v: any) => v.pk);
    const url = APIs[i];
    apiMap[url] = apiMap[url] || {
      apiUrl: url,
      resourceKeys: [],
      type: 0,
      projectName: pkLInkAPIProject,
      reason: null,
    };
    apiMap[url].resourceKeys.push(...pk);
  }
  const apiAuthList = Object.values(apiMap);
  let pageList: any = [];
  let FKList: any = [];
  PK?.forEach((item) => {
    if (item.type === 'PK') {
      pageList.push({
        path: `/qbi/legoBI/report/${item.pk}`,
        pk: item.pk,
        rk: item.pk,
        pageName: item.name,
        pageMeta: '{"component":" \'./lego/modules/reportPage\'"}',
      });
    } else {
      FKList.push({
        name: item.name,
        // 功能FK
        fk: `${item.pk}__${item.functionKey}`,
        rk: item.functionKey,
        // 依赖页面
        parentPK: item.pk,
        // 功能信息 string
        functionMeta: JSON.stringify({
          // 功能类型  0-按钮（不传默认）， 1-列表按钮，2-查看详情按钮，3-编辑类详情按钮 ， 4接口，5-模块，6-表格权限编辑
          funType: 5,
        }),
      });
    }
  });

  const timeStamp = Date.now();
  const sign = md5(
    JSON.stringify(apiAuthList) + timeStamp + hostIp,
  ).toUpperCase();
  const result = {
    timeStamp,
    sign,
    apiAuthList,
  };
  const apiPromise = axios({
    url: `/dockingApi/v1/admin/invoker/adminApiAuthHService_uploadApiAuthData`,
    method: 'post',
    data: result,
    headers: {
      _admin_eid: 1,
      _admin_ts: 163827092,
    },
  });

  const pagePromise = axios({
    url: `/dockingApi/v1/admin/invoker/adminBlmPageHService_reportPage`,
    method: 'POST',
    data: {
      // 当前平台
      platform: 'qbi',
      // 上报标识  32
      id: uuid.v1().replace(/-/g, ''),
      // 环境 daily dev daily2 pre2 pre publish tolerant
      env: dev,
      // 扫描到的页面
      pageList,
    },
  });

  const FKPromise = axios({
    url: `/dockingApi/v1/admin/invoker/adminBlmFunctionHService_reportFunction`,
    method: 'POST',
    data: {
      // 当前平台
      platform: 'qbi',
      // 上报标识  32
      id: uuid.v1().replace(/-/g, ''),
      // 环境 daily dev daily2 pre2 pre publish tolerant
      env: dev,
      // 扫描到的页面
      functionList: FKList,
    },
  });

  return Promise.all([apiPromise, pagePromise, FKPromise]);
}
