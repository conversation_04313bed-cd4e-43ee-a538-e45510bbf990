/*
* 使用yapi平台来mock数据
* yapi平台地址： https://interface-platform-test.bailongma-inc.com/
* 使用文档：https://bailongma.yuque.com/pdc3yd/gdggt8/ghl4vyt3qfzxfs5l
*/

import axios from 'axios';
import {message} from "@blmcp/ui";

const devBaseURL = 'https://interface-platform-test.bailongma-inc.com/mock/272';

const yApiRequest = axios.create({
  baseURL: ['development', 'apitest', 'apitest2'].includes(process.env.NODE_ENV) ? devBaseURL : '',
  timeout: 5000
})

//添加拦截
yApiRequest.interceptors.request.use(config => {
  // baseURL以yapi平台生成的为准，可自行传入
  if (config?.yApiBaseUrl) {
    config.baseURL = config.yBaseUrl
  }

  return config
},error => {
   Promise.reject(error);
})

yApiRequest.interceptors.response.use(response => {
  if (!response?.data || response?.data?.code !== 1) {
    message.error(response.data.errmsg ?? '接口请求异常')
  }

  return response.data;
},error => {
  const msg = typeof error === 'object' ? error.msg : error;
  // 增加全局错误提示， 入参中包含error_code的场景，返回的错误信息为对象，在业务中单独处理
  if (typeof msg === 'string') {
    if (msg === '请勿重复请求') return Promise.reject(msg);
    if (msg.includes('请求超时')) {
      message.error('接口请求超时');
    } else {
      message.error(error);
    }
  }
  return Promise.reject(error);
})




export default yApiRequest;
