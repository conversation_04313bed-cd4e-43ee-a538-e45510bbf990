{"/bos/admin/v1/ai/crowd-tag/pageQueryCrowdList": {"request": {}, "response": {}, "originUrl": "/admin/v1/weklin/crowdTags/crowd/pageQueryCrowdList"}, "/bos/admin/v1/ai/chart-manager/saveElementStructureInfos": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/saveElementStructureInfos"}, "/bos/admin/v1/ai/chart-manager/createReportInitStatus": {"request": {"id": "reportId"}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/createReportInitStatus"}, "/bos/admin/v1/ai/chart-manager/deleteReport": {"request": {"id": "reportId"}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/deleteReport"}, "/bos/admin/v1/ai/chart-manager/publishReport": {"request": {"id": "reportId"}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/publishReport"}, "/bos/admin/v1/ai/chart-manager/copyTemplate": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/copyTemplate"}, "/bos/admin/v1/ai/chart-manager/queryReportPageList": {"request": {"q": "search<PERSON>ey"}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/queryReportPageList"}, "/bos/admin/v1/ai/chart-manager/addDataSetsToUser": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/addDataSetsToUser"}, "/bos/admin/v1/ai/chart-manager/delDataSetsToUser": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/delDataSetsToUser"}, "/bos/admin/v1/ai/chart-manager/addShares": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/addShares"}, "/bos/admin/v1/ai/chart-manager/exchangeOwner": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/exchangeOwner"}, "/bos/admin/v1/ai/chart-manager/validateVirtualField": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/validateVirtualField"}, "/bos/admin/v1/ai/chart-manager/deleteVirtualField": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/deleteVirtualField"}, "/bos/admin/v1/ai/chartexport/getTemplateData": {"request": {}, "response": {"is_blm_bos_map": "data.bizTemplateMap.is_blm_bos_map"}, "originUrl": "/admin/v1/chartexport/export/server/getTemplateData"}, "/bos/admin/v1/ai/chartexport/standardExport": {"request": {}, "response": {}, "originUrl": "/admin/v1/chartexport/export/server/standardExport"}, "/bos/admin/v1/ai/chartexport/getExportRecordsV2": {"request": {}, "response": {"page": "data.pageNum", "size": "data.pageSize", "total": "data.totalNum", "list": "data.items"}, "originUrl": "/admin/v2/common/export/server/getExportRecordsV2"}, "/admin/v1/ai/chart-manager/v2/queryDomainAndDatasetsInfo": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/queryDomainAndDatasetsInfo"}, "/admin/v1/ai/chart-manager/v2/queryUserAndDataSetsList": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/queryUserAndDataSetsList"}, "/admin/v1/ai/chart-manager/v2/queryAllDatasets": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/queryAllDatasets"}, "/admin/v1/ai/chart-manager/v2/queryDatasetsColumns": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/queryDatasetsColumns"}, "/admin/v1/ai/chart-manager/v2/queryDatasetsColumnsV2": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/queryDatasetsColumnsV2"}, "/admin/v1/ai/chart-manager/v2/saveReportLayoutInfo": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/saveReportLayoutInfo"}, "/admin/v1/ai/chart-manager/v2/queryReportLayoutInfo": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/queryReportLayoutInfo"}, "/admin/v1/ai/chart-manager/v2/queryElementResultData": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/queryElementResultData"}, "/admin/v1/ai/chart-manager/v2/queryUserListForAuthor": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/queryUserListForAuthor"}, "/admin/v1/ai/chart-manager/v2/queryShareList": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/queryShareList"}, "/admin/v1/ai/chart-manager/v2/saveVirtualField": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/saveVirtualField"}, "/admin/v1/ai/chart-manager/v2/queryVirtualFieldByColumnId": {"request": {}, "response": {}, "originUrl": "/admin/v1/ai/chart-manager/queryVirtualFieldByColumnId"}}