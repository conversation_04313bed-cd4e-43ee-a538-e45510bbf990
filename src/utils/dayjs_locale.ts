const t = function (format: string) {
  return format.replace(/(\[[^\]]+])|(MMMM|MM|DD|dddd)/g, function (_, a, b) {
    return a || b.slice(1);
  });
};

export default (function (o, c, dayjs) {
  if (!c || !dayjs) return;
  // locale needed later
  let proto = c.prototype;

  let getLocalePart = function getLocalePart(part) {
    return part && (part.indexOf ? part : part.s);
  };

  let getShort = function (ins, target, full, num, localeOrder) {
    let locale = ins.name ? ins : ins.$locale();
    let targetLocale = getLocalePart(locale[target]);
    let fullLocale = getLocalePart(locale[full]);
    let result =
      targetLocale ||
      fullLocale.map(function (f) {
        return f.slice(0, num);
      });
    if (!localeOrder) return result;
    let weekStart = locale.weekStart;
    return result.map(function (_, index) {
      return result[(index + (weekStart || 0)) % 7];
    });
  };

  let getDayjsLocaleObject = function getDayjsLocaleObject() {
    return dayjs.Ls[dayjs.locale()];
  };

  let getLongDateFormat = function getLongDateFormat(l, format) {
    return l.formats[format] || t(l.formats[format.toUpperCase()]);
  };

  let localeData = function localeData() {
    let _this = this;

    return {
      months: function months(instance) {
        return instance ? instance.format('MMMM') : getShort(_this, 'months');
      },
      monthsShort: function monthsShort(instance) {
        return instance
          ? instance.format('MMM')
          : getShort(_this, 'monthsShort', 'months', 3);
      },
      firstDayOfWeek: function firstDayOfWeek() {
        return _this.$locale().weekStart || 0;
      },
      weekdays: function weekdays(instance) {
        return instance ? instance.format('dddd') : getShort(_this, 'weekdays');
      },
      weekdaysMin: function weekdaysMin(instance) {
        return instance
          ? instance.format('dd')
          : getShort(_this, 'weekdaysMin', 'weekdays', 2);
      },
      weekdaysShort: function weekdaysShort(instance) {
        return instance
          ? instance.format('ddd')
          : getShort(_this, 'weekdaysShort', 'weekdays', 3);
      },
      longDateFormat: function longDateFormat(format) {
        return getLongDateFormat(_this.$locale(), format);
      },
      meridiem: this.$locale().meridiem,
      ordinal: this.$locale().ordinal,
    };
  };

  proto.localeData = function () {
    return localeData.bind(this)();
  };

  dayjs.localeData = function () {
    let localeObject = getDayjsLocaleObject();
    return {
      firstDayOfWeek: function firstDayOfWeek() {
        return localeObject.weekStart || 0;
      },
      weekdays: function weekdays() {
        return dayjs.weekdays();
      },
      weekdaysShort: function weekdaysShort() {
        return dayjs.weekdaysShort();
      },
      weekdaysMin: function weekdaysMin() {
        return dayjs.weekdaysMin();
      },
      months: function months() {
        return dayjs.months();
      },
      monthsShort: function monthsShort() {
        return dayjs.monthsShort();
      },
      longDateFormat: function longDateFormat(format) {
        return getLongDateFormat(localeObject, format);
      },
      meridiem: localeObject.meridiem,
      ordinal: localeObject.ordinal,
    };
  };

  dayjs.months = function () {
    return getShort(getDayjsLocaleObject(), 'months');
  };

  dayjs.monthsShort = function () {
    return getShort(getDayjsLocaleObject(), 'monthsShort', 'months', 3);
  };

  dayjs.weekdays = function (localeOrder) {
    return getShort(
      getDayjsLocaleObject(),
      'weekdays',
      null,
      null,
      localeOrder,
    );
  };

  dayjs.weekdaysShort = function (localeOrder) {
    return getShort(
      getDayjsLocaleObject(),
      'weekdaysShort',
      'weekdays',
      3,
      localeOrder,
    );
  };

  dayjs.weekdaysMin = function (localeOrder) {
    return getShort(
      getDayjsLocaleObject(),
      'weekdaysMin',
      'weekdays',
      2,
      localeOrder,
    );
  };
});
