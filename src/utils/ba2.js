(function () {
  const _ba2_version = process.env.VERSION;
  const hostName = location.hostname;
  const isTEST = /(dev|daily|test|localhost|\d+\.\d+\.\d+\.\d+)/.test(hostName);
  const isLocal = /(localhost|\d+\.\d+\.\d+\.\d+)/.test(hostName);
  const appId = 1;
  let appVersion = '4.0.0'; //根据项目版本定义

  /* <- CP后台独享 */
  const isCP = /^admin(\-(pre|dev|daily|daily))?\.yueyuechuxing\.cn$/.test(
    hostName,
  );
  // 哈勃不引用
  if (!isLocal && !isCP) {
    return;
  }

  // const versionData = document.querySelector('link[rel*="icon"]').getAttribute('href').match(/\/([\d\.\_]+)/);
  // appVersion = versionData && versionData[1] || appVersion;
  /* CP后台独享 -> */

  window._ba2 = window._ba2 || [];
  window._ba2.push(['conf', 'mode', 'history']);
  window._ba2.push(['set', 'app_id', appId]); //CP后台为1, 代驾后台为2，油站后台(车后)为3
  window._ba2.push(['set', 'app_version', appVersion]);
  window._ba2.push(['set', 'app_system', 2]); //新增可配置客户端类型 2:web
  window._ba2.push(['set', 'log_version', _ba2_version]); //埋点字段变更版本
  window._ba2.push(['conf', 'img_url', '/admin/v1/common/log/collect']);

  const reportTabPv = function () {
    setTimeout(() => {
      const tmpDataExt =
        (this.tmpData.ext && this.tmpData.ext.tab_name) || null;
      const tabList = document.querySelectorAll(
        '.el-tabs__item.is-active,.el-radio-button.is-active',
      );
      const ext = {
        tab_name: {},
      };
      let i = 0;
      tabList.forEach((elem) => {
        //判断是否可见
        const text =
          (elem.childNodes &&
            elem.childNodes.length === 1 &&
            elem.childNodes[0].title) ||
          elem.title ||
          elem.innerText;
        if (
          text &&
          !!(
            elem.offsetWidth ||
            elem.offsetHeight ||
            elem.getClientRects().length
          )
        ) {
          ext.tab_name[`tab_name_${++i}`] = text;
        }
      });
      if (JSON.stringify(ext.tab_name) !== JSON.stringify(tmpDataExt)) {
        if (tmpDataExt) {
          this.report({
            event_type: 'lv',
            event_time: new Date().getTime(),
            ext: this.tmpData.ext,
          });
        }
        if (i > 0) {
          window._ba2.push(['setTmp', 'ext', ext]);
          this.report({
            event_type: 'pv',
            event_time: new Date().getTime(),
            ext,
          });
        } else {
          try {
            delete this.tmpData.ext.tab_name;
          } catch (e) {}
        }
      }
    }, 0);
  };

  // 记录当前选中tab
  window._ba2.push([
    'afterPv',
    function () {
      let reported = false;
      const observer = new MutationObserver((mutations) => {
        if (!mutations || !mutations.forEach) {
          return;
        }
        const node = {
          time: Math.round(performance.now()),
          roots: [],
        };
        mutations.forEach((mut) => {
          if (!mut || !mut.addedNodes || !mut.addedNodes.forEach) {
            return;
          }
          mut.addedNodes.forEach((ele) => {
            if (
              !reported &&
              ele.classList &&
              ele.classList.length &&
              [...ele.classList].includes('el-tabs__item')
            ) {
              reported = true;
              reportTabPv.call(this);
              observer.disconnect();
            }
          });
        });
      });
      observer.observe(document, {
        childList: true,
        subtree: true,
      });
      setTimeout(() => {
        try {
          observer.disconnect();
        } catch (e) {
          console.log(e);
        }
      }, 10000);
    },
  ]);

  // 自定义元素，绑定事件
  window._ba2.push([
    'listen',
    'click',
    'click',
    [
      ['el-form-item', '>el-form-item__label'], //查找 formItem 对应的label，如果没有就过
      ['el-switch', 'is-active'], //查找 formItem 对应的label，如果没有就过
      'el-step',
      'el-tabs__item',
      ['el-tabs__item', reportTabPv],
      'el-radio-button',
      ['el-radio-button', reportTabPv],
      'el-radio',
      'el-checkbox',
      'el-date-editor', //parentNode.children.['el-form-item__label']
      'el-select-dropdown__item',
      'el-table__cell',
    ],
  ]);
  const am = document.createElement('script');
  am.src = `https://${Number(window.cdnState)===1?"yycx-test-txgz":"cdntest"}.yueyuechuxing.cn/yueyue/admin/static_umd/daily/js/_ba.js?appid=${appId}&v=${_ba2_version}${
    isTEST ? '&debug=true' : ''
  }`;
  const s = document.getElementsByTagName('script')[0];
  s.parentNode.insertBefore(am, s);
})();
