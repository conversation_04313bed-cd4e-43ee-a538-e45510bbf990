declare global {
  interface Window {
    $HbUtils: any;
  }
}

import { isHubble } from './hubble';

export const oriThreePhase = isHubble ? false : true;

export const apiMap = {
  '/admin/v1/ai/chart-manager/saveElementStructureInfos':
    '/bos/admin/v1/ai/chart-manager/saveElementStructureInfos',
  '/admin/v1/ai/chart-manager/createReportInitStatus':
    '/bos/admin/v1/ai/chart-manager/createReportInitStatus',
  '/admin/v1/ai/chart-manager/deleteReport':
    '/bos/admin/v1/ai/chart-manager/deleteReport',
  '/admin/v1/ai/chart-manager/publishReport':
    '/bos/admin/v1/ai/chart-manager/publishReport',
  '/admin/v1/ai/chart-manager/copyTemplate':
    '/bos/admin/v1/ai/chart-manager/copyTemplate',
  '/admin/v1/ai/chart-manager/queryReportPageList':
    '/bos/admin/v1/ai/chart-manager/queryReportPageList',
  '/admin/v1/ai/chart-manager/addDataSetsToUser':
    '/bos/admin/v1/ai/chart-manager/addDataSetsToUser',
  '/admin/v1/ai/chart-manager/delDataSetsToUser':
    '/bos/admin/v1/ai/chart-manager/delDataSetsToUser',
  '/admin/v1/ai/chart-manager/addShares':
    '/bos/admin/v1/ai/chart-manager/addShares',
  '/admin/v1/ai/chart-manager/exchangeOwner':
    '/bos/admin/v1/ai/chart-manager/exchangeOwner',
  '/admin/v1/ai/chart-manager/validateVirtualField':
    '/bos/admin/v1/ai/chart-manager/validateVirtualField',
  '/admin/v1/ai/chart-manager/deleteVirtualField':
    '/bos/admin/v1/ai/chart-manager/deleteVirtualField',
  '/admin/v1/chartexport/export/server/getTemplateData':
    '/bos/admin/v1/ai/chartexport/getTemplateData',
  '/admin/v1/chartexport/export/server/standardExport':
    '/bos/admin/v1/ai/chartexport/standardExport',
  '/admin/v2/common/export/server/getExportRecordsV2':
    '/bos/admin/v1/ai/chartexport/getExportRecordsV2',
  '/admin/v1/ai/chart-manager/queryDomainAndDatasetsInfo':
    '/admin/v1/ai/chart-manager/v2/queryDomainAndDatasetsInfo',
  '/admin/v1/ai/chart-manager/queryUserAndDataSetsList':
    '/admin/v1/ai/chart-manager/v2/queryUserAndDataSetsList',
  '/admin/v1/ai/chart-manager/queryAllDatasets':
    '/admin/v1/ai/chart-manager/v2/queryAllDatasets',
  '/admin/v1/ai/chart-manager/queryDatasetsColumns':
    '/admin/v1/ai/chart-manager/v2/queryDatasetsColumns',
  '/admin/v1/ai/chart-manager/queryDatasetsColumnsV2':
    '/admin/v1/ai/chart-manager/v2/queryDatasetsColumnsV2',
  '/admin/v1/ai/chart-manager/saveReportLayoutInfo':
    '/admin/v1/ai/chart-manager/v2/saveReportLayoutInfo',
  '/admin/v1/ai/chart-manager/queryReportLayoutInfo':
    '/admin/v1/ai/chart-manager/v2/queryReportLayoutInfo',
  '/admin/v1/ai/chart-manager/queryElementResultData':
    '/admin/v1/ai/chart-manager/v2/queryElementResultData',
  '/admin/v1/ai/chart-manager/queryUserListForAuthor':
    '/admin/v1/ai/chart-manager/v2/queryUserListForAuthor',
  '/admin/v1/ai/chart-manager/queryShareList':
    '/admin/v1/ai/chart-manager/v2/queryShareList',
  '/admin/v1/ai/chart-manager/saveVirtualField':
    '/admin/v1/ai/chart-manager/v2/saveVirtualField',
  '/admin/v1/ai/chart-manager/queryVirtualFieldByColumnId':
    '/admin/v1/ai/chart-manager/v2/queryVirtualFieldByColumnId',
};
