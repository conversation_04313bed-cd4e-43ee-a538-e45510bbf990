import { Spin } from '@blmcp/ui';
import { useEffect, useState } from 'react';
import * as legoRequest from '@blmcp/peento-request';
import dayjs from 'dayjs';
import React from 'react';
import ReactDOM from 'react-dom';
import PropTypes from 'prop-types';
import { IPublicTypeRootSchema } from '@alilc/lowcode-types';
import { getPageStructure } from '@/pages/lego/api';
import LegoRenderer from '@/pages/lego/modules/viewPage/LegoRenderer';
import { LegoReportIdKey, clearLocaPageId } from '@/pages/lego/utils';
import { blmAnalysisPageView } from '@/utils/eventTracking';
import './style.less';
import isMobile from '@/pages/lego/utils/isMobile';
// import { ComponentValue } from '@/pages/lego/hooks/useComponent';

window.legoRequest = legoRequest;
window.dayjs = dayjs;
window.React = React;
window.ReactDOM = window.ReactDom || ReactDOM;
window.PropTypes = PropTypes;

const filterNameMap: any = {
  CityFilter: 'adcode',
  CapacityCompanyFilter: 'car_team_id',
  DateFilterGlobal: 't_date',
};

const newFilterNameMap: any = {
  NewCityFilter: 'adcode',
  NewCarTeamFilter: 'car_team_id',
  DateFilterGlobal: 't_date',
  NewFleetFilter: 'fleet_id',
};

declare global {
  interface Window {
    legoRequest: any;
    antd: any;
    dayjs: any;
    PropTypes: any;
    ReactDom: any;
  }
}

interface ViewProps {
  reportKey?: string;
  reportId?: string;
  edit?: boolean;
  share?: boolean;
  filterProps?: {
    [key: string]: {
      defaultValue: number[] | string[];
      disabled: boolean;
      hidden: boolean;
      clearable: boolean;
    };
  };
  walkerComponents?: (item: IPublicTypeRootSchema) => void; // 组件渲染
  libAssets: string[];
  aiAttribution?: boolean;
}

export default function (props: ViewProps) {
  const [data, setData] = useState<any>();
  const {
    reportKey,
    reportId,
    edit = false,
    share = false,
    filterProps = {},
    libAssets = [],
    aiAttribution = true,
  } = props;

  useEffect(() => {
    setData(null);
    if (reportKey) {
      blmAnalysisPageView({
        pageId: 'p_leopard_cp_00000668',
        eventId: 'e_leopard_cp_pv_00003084',
        ext: {
          str0_e: reportKey,
        },
      });
    }
    if (reportId) {
      blmAnalysisPageView({
        pageId: 'p_leopard_cp_00000662',
        eventId: 'e_leopard_cp_pv_00003064',
        ext: {
          str0_e: reportId,
        },
      });
    }

    getPageStructure({
      reportId,
      pageMark: reportKey,
      publishStatus: 1,
    })
      .then((res: any) => {
        if (res.code === 1) {
          sessionStorage.setItem(LegoReportIdKey, res.data.reportId);
        } else {
          clearLocaPageId();
        }
        setData(res as any);
      })
      .catch((res) => {
        setData(res as any);
      });

    return function () {
      clearLocaPageId();
    };
  }, [reportKey, reportId]);

  if (!data) {
    return <Spin size="large" className="lego-page-loading" />;
  }

  return (
    <LegoRenderer
      assets={true}
      libraryAssetCallback={(packages: any[]) => {
        packages.forEach((item) => {
          if (item.package === 'leopard-web-qbi' && libAssets.length) {
            item.urls = libAssets;
          }
        });
        // packages.push({
        //   library: 'antd',
        //   package: 'antd',
        //   await: true,
        //   priority: 1,
        //   urls: [
        //     'https://webstatic.yueyuechuxing.cn/yueyue/admin/lego-base/v1.0.0/antd.js',
        //   ],
        // });
      }}
      data={data}
      edit={edit}
      share={share}
      aiAttribution={aiAttribution}
      walkerComponents={(item: any, parent: any) => {
        const key =
          (item.componentName === 'ListFilter' &&
            item.props?.dataSetConfig?.dimensionInfo?.[0].key) ||
          filterNameMap[item.componentName] ||
          newFilterNameMap[item.componentName];
        const setting = filterProps[key];
        if (setting) {
          item.props.defaultValue = setting.defaultValue || [];
          item.props.disabled = !!setting.disabled;
          if (setting.hidden) {
            parent.props.style.display = 'none';
          }
          if (setting.clearable) {
            parent.props.style.display = 'none';
            // 先通过纯组件搜索按钮方式去替换
            // PC端才进行替换
            if (!isMobile()) {
              item.componentName = 'SearchButton';
            }
          }
        }
        props?.walkerComponents?.(item);
      }}
    ></LegoRenderer>
  );
}
