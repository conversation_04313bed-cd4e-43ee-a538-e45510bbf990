.lego-exp {
    display: inline-block;
  }
  .ant-segmented
    .ant-segmented-item:hover:not(.ant-segmented-item-selected):not(
      .ant-segmented-item-disabled
    )::after {
    background-color: transparent !important;
  }
  
  .standard-export-drawer .ant-drawer-header .ant-drawer-extra svg {
    width: 16px;
    height: 16px;
  }
  .standard-export-drawer .ant-drawer-footer .export-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  .export-content {
    user-select: none;
    -webkit-user-select: none;
  }
  .export-content .export-content-item {
    padding: 0px 10px;
    background: #fff;
    border-radius: 8px;
    margin-bottom: 10px;
  }
  .export-content .export-content-item .export-content-item-title {
    font-size: 15px;
    font-weight: 500;
    line-height: 24px;
    color: rgba(0, 0, 0, 0.9);
    padding: 13px 0px;
    display: flex;
    align-items: center;
  }
  .export-content .export-content-item .export-search-empty-box {
    font-size: 13px;
    line-height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: rgba(0, 0, 0, 0.6);
    padding: 20px 0px 30px 0px;
  }
  .export-content .export-content-item .export-columns-span-box .export-columns-span {
    font-size: 13px;
    line-height: 20px;
    color: rgba(0, 0, 0, 0.6);
    display: inline-flex;
    justify-content: center;
    align-items: center;
    padding: 5px 10px;
    margin: 0px 5px 10px 0px;
    background: rgba(37, 52, 79, 0.03);
    border-radius: 6px;
  }
  .export-content .export-content-item .export-indesens-switch-box {
    padding-bottom: 10px;
  }
  .export-content .export-content-item .export-columns-check-box {
    padding-bottom: 10px;
  }
  .export-content .export-content-item .export-columns-check-box .export-columns-check-item {
    display: inline-flex;
    padding: 10px 10px 10px 0px;
  }
  .export-content .export-content-item .remark-length-span {
    margin-inline-end: 0px;
    color: rgba(0, 0, 0, 0.3);
  }
  .export-content .export-content-item .remark-max-span {
    color: rgba(0, 0, 0, 0.3);
  }
  .export-content .export-content-item .export-description {
    font-size: 13px;
    color: rgba(0, 0, 0, 0.6);
    line-height: 18px;
    padding-bottom: 10px;
  }
  .export-audit-modal .export-auditors-span {
    font-size: 12px;
    line-height: 18px;
    color: rgba(0, 0, 0, 0.6);
    margin-bottom: 10px;
  }
  .export-audit-modal .export-auditors-box {
    font-size: 14px;
  }
  .export-animation-ball {
    position: absolute;
    width: 50px;
    height: 50px;
    z-index: 9999;
    border-radius: 20px;
    background-color: #FFF;
    background-size: 32px 32px;
    background-position: center;
    background-repeat: no-repeat;
    box-shadow: 0px 5px 10px 0px rgba(42, 48, 72, 0.1);
    transition:
      left 0.6s linear,
      top 0.6s cubic-bezier(0, 1.23, 0.92, 1.04),
      opacity 0.6s step-end,
      transform 0.6s linear;
  }
  .export-button-no-hover-bg:hover {
    background-color: transparent !important;
  }
  