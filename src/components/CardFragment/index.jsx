import React, { useRef, useEffect, useState, useMemo } from 'react';
import { Tooltip } from '@blmcp/ui';
import useIntersectionObserver from '@/hooks/useIntersectionObserver';
import style from './index.less';
import { blmAnalysisModuleExposure } from '@/utils/eventTracking';
const Card = ({
  title,
  tips = [],
  children,
  bodyHeight = 300,
  comId,
  rightExtra,
  virtual = true,
}) => {
  const containerRef = React.useRef(null);
  const info = Array.isArray(tips) ? tips : [tips];
  const [loading, setLoading] = useState(true);
  const [footRef, setFootRef] = useState(null);
  const [bodyRef, setBodyRef] = useState(null);
  const [footContentRef, setFootContentRef] = useState(null);
  const [isShowWholeContent, setShowWholeContent] = useState(false);

  const divRef = useIntersectionObserver((entries) => {
    setLoading(false);
  });
  useEffect(() => {
    //数据变化时收起折叠面板
    setShowWholeContent(false);
  }, [info]);

  //判断图表是否占据整行
  const isShortFoot = useMemo(() => {
    const moduleWrap = document.querySelector('#sence-analysis');
    //图表宽度小于容器内容区
    return bodyRef?.clientWidth < moduleWrap?.clientWidth - 20;
  }, [bodyRef]);

  //获取foot高度
  const footHeight = useMemo(() => {
    if (!info.length) return 0;
    const { clientHeight, clientTop } = footRef || {};
    return clientHeight + clientTop;
  }, [footRef, info, isShortFoot]);

  //计算foot内容区是否超高溢出
  const isTextOverFlow = useMemo(() => {
    if (!info.length || !isShortFoot) return false;
    const { scrollHeight, clientHeight } = footContentRef || {};
    return scrollHeight > clientHeight;
  }, [footContentRef, info, isShortFoot]);

  const handlefoldContent = () => {
    setShowWholeContent(!isShowWholeContent);
  };
  return (
    <div className={style['card-fragment-container']} ref={divRef}>
      <div className={style['header']}>
        <div className={style['title']}>
          <div>{title}</div>
          {/* {tips && (
            <div className={style['tip-number']}>
              {info?.map((v) => {
                return (
                  <Tooltip
                    title={v.explaination}
                    placement="top"
                    key={comId + '-' + v.idx}
                    trigger={['click', 'hover']}
                  >
                    <span
                      id={comId + '-' + v.idx}
                      onMouseEnter={() => {
                        // 图表-运营建议曝光
                        blmAnalysisModuleExposure({
                          eventId: 'e_leopard_cp_exposure_00000028',
                          pageId: 'p_leopard_cp_00000004',
                          ext: {
                            str0_e: v.explaination,
                          },
                        });
                      }}
                    >
                      [{v.idx}]
                    </span>
                  </Tooltip>
                );
              })}
            </div>
          )} */}
        </div>
        {rightExtra && <div>{rightExtra}</div>}
      </div>
      <div
        className={style['body']}
        ref={setBodyRef}
        style={{
          height: `${isShortFoot ? bodyHeight - footHeight : bodyHeight}px`,
        }}
      >
        {virtual ? <>{loading ? null : children}</> : <>{children}</>}
      </div>
      {info?.length ? (
        <>
          <div
            ref={setFootRef}
            className={`${style['foot']} 
            ${isShortFoot ? style['foot--short'] : ''} 
            ${isShowWholeContent ? style['foot--expend'] : ''}
            ${isTextOverFlow ? style['foot--absolute'] : ''}`}
            onClick={isTextOverFlow ? handlefoldContent : null}
          >
            <p className={style['foot-title']}>数据解读：</p>
            <p className={style['foot-content']} ref={setFootContentRef}>
              {info?.map((v, index) => (
                <span key={index}>
                  {v.explaination}
                  {index !== info?.length - 1 ? <br /> : ''}
                </span>
              ))}
            </p>
            {isTextOverFlow && (
              <img
                className={style['arrow-icon']}
                src={require('./img/arrow-icon.svg').default}
              />
            )}
          </div>
          {/* 占位元素 */}
          {isTextOverFlow && <div style={{ height: footHeight }}></div>}
        </>
      ) : (
        ''
      )}
    </div>
  );
};

export default Card;
