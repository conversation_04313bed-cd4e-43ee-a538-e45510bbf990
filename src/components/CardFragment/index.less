.card-fragment-container {
  // min-height: 300px;
  border-radius: 8px;
  background: #fff;
  position: relative;
  overflow: hidden;
  .flex {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .body {
    padding: 10px;
    height: 300px;
    // overflow: hidden;
    display: flex;
    align-items: center;
    & > div {
      flex: 1;
    }
  }

  .header {
    height: 50px;
    .flex();
    font-size: 15px;
    font-family: PingFangSC-Medium;
    border-bottom: 1px solid rgba(37, 52, 79, 8%);
    padding: 13px 10px;
    color: rgba(0, 0, 0, 0.9);
    .title {
      display: flex;
    }
  }

  .tip-number {
    color: #2761f3;
    display: flex;
    flex-direction: row;
    font-size: 12px;
    font-family: PingFangSC-Regular;
    margin-left: 10px;

    span {
      background: rgba(39, 97, 243, 0.05);
      display: inline-block;
      padding: 0 5px;
      height: 20px;
      line-height: 20px;
      margin-right: 5px;
      border-radius: 4px;
    }
  }
  .foot {
    display: flex;
    align-items: flex-start;
    padding: 10px;
    border-top: 1px solid rgba(37, 52, 79, 0.0784);
    background-color: white;
    &-title {
      min-width: 64px;
      font-family: PingFang SC;
      font-size: 12px;
      font-weight: 500;
      line-height: 18px;
      letter-spacing: 0em;
      color: #f89003;
    }
    &-content {
      font-family: PingFang SC;
      font-size: 12px;
      font-weight: normal;
      line-height: 18px;
      letter-spacing: 0em;
      /* 时段 */
      color: rgba(0, 0, 0, 0.6);
    }
  }
  .foot--short {
    max-height: 56px;
    transition: 0.3s;
    .foot-content {
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
    }
  }
  .foot--absolute {
    height: 56px;
    position: absolute;
    bottom: 0;
    cursor: pointer;
  }
  .foot--expend {
    height: 301px;
    max-height: 301px;
    .foot-content {
      -webkit-line-clamp: unset;
    }
    .arrow-icon {
      transform: rotate(180deg);
    }
  }
}
