import { useEffect, useMemo, useState } from 'react';
import { createForm, onFormMount, onFormValuesChange } from '@formily/core';
import { FormProvider, createSchemaField, ISchema } from '@formily/react';
import { FormItem, Reset, FormButtonGroup, FormGrid } from '@formily/antd-v5';
import { Select, Input, DatePicker } from '@formily/antd-v5';
import {
  BLMCityCascader,
  BLMCarTeamSelect,
} from '@blmcp/peento-businessComponents';
import { BLMButton, ConfigProvider } from '@blmcp/ui';
import { isFunction, throttle } from 'lodash-es';

interface IProps {
  schema?: ISchema;
  scope?: any;
  data?: { [T: string]: any };
  grid?: number;
  showFooter?: boolean;
  onReset?: () => void;
  onSearch?: (data?: any) => void;
  onChange?: (data?: any) => void;
}

const SchemaField = createSchemaField({
  components: {
    FormItem,
    Select,
    Input,
    DatePicker,
    BLMCityCascader,
    BLMCarTeamSelect,
  },
});

const SearchForm: React.FC<IProps> = (props: IProps) => {
  const [_schema, setSchema] = useState<ISchema>(props.schema || {});
  const [_scope, setScope] = useState<any>(props.scope || {});

  const form = useMemo(() => {
    return createForm({
      effects: () => {
        onFormMount((form) => {
          if (props.data) {
            form.setValues(props.data);
          }
        });
        onFormValuesChange((form) => {
          if (props.onChange) {
            const fn = throttle(props.onChange, 500);
            fn(form.values);
          }
        });
      },
    });
  }, [_schema, _scope]);

  const handleReset = () => {
    form.reset();
    if (isFunction(props.onReset)) {
      props.onReset();
    }
  };

  const handleSearch = () => {
    if (!props.onSearch || !form) return;
    form.submit((values) => {
      if (isFunction(props.onSearch)) {
        props.onSearch(values); // 不判断是否是函数，会报错
      }
    });
  };

  useEffect(() => {
    if (!props.schema) return;
    setSchema(props.schema);
  }, [props.schema]);

  useEffect(() => {
    if (!props.scope) return;
    setScope(props.scope);
  }, [props.scope]);

  useEffect(() => {
    form.setValues(props.data);
  }, [props.data]);

  return (
    <div className="form-container">
      <ConfigProvider>
        <FormProvider form={form}>
          {!!props.grid ? (
            <FormGrid maxColumns={props.grid} minColumns={props.grid}>
              <SchemaField schema={_schema} scope={_scope} />
            </FormGrid>
          ) : (
            <SchemaField schema={_schema} scope={_scope} />
          )}
          {props.showFooter ? (
            <FormButtonGroup gutter={0}>
              <BLMButton
                type="primary"
                onClick={handleSearch}
                style={{ width: '60px' }}
              >
                搜索
              </BLMButton>
              <Reset
                onClick={handleReset}
                className="search-form-reset-button"
                forceClear
              >
                重置
              </Reset>
            </FormButtonGroup>
          ) : null}
        </FormProvider>
      </ConfigProvider>
    </div>
  );
};

export default SearchForm;
