.selectBox {
  font-size: 14px;
  overflow: hidden;
}
.selectCard {
  border: 1px solid #E7E8EB;
  border-radius: 4px;
  height: 330px;
  min-width: 225px;
}
.selectTitle {
  padding: 10px 16px;
  height: 43px;
  border-bottom: 1px solid #E7E8EB;
}
.selectcontent{
  height: 287px !important;
  //overflow-y: auto;
}
.virtual {
  height: 247px !important;
}
.checks {
  padding: 8px 16px;
  &:hover{
    background-color: #F7F7F9;
    cursor: pointer;
  }
}
.checkedLabel {
  width: 125px;
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 使用省略号表示截断的部分 */
  display: inline-block;
}

.checksTwo {
  padding: 8px 16px 0 16px
}
.checksContent {
  padding: 8px;
  height: 38px;
  border-radius: 4px;
  background-color: #F7F7F9
}
.noData {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  justify-content: center; /* 水平居中对齐 */
  height: 287px;
  color: rgba(0,0,0, 0.26);
}

// #366CFE
