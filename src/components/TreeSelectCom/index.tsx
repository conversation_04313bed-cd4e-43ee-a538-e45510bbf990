import React, { useEffect, useRef, useState } from 'react';
import { CloseOutlined, SearchOutlined } from '@ant-design/icons';
import { Tooltip, TreeSelect } from '@blmcp/ui';
import { cloneDeep } from 'lodash';
import VirtualList from 'rc-virtual-list';
import styles from './index.less';
import CheckBoxComPage from './components/checkBoxCom';

const TreeSelectCom = ({
  options = [],
  defaultCheckedKeys = [], // 源数据 默认选中节点的id集合
  pid = 'pid',
  defaultProps = { label: 'label', value: 'value', children: 'children' },
  onChange = (treeInfo: any, checkedKeys: any) => {},
}) => {
  //
  const [optionsCopy, setOptionsCopy] = useState([]);
  // 选中的数据的nodeKey集合
  const [defaultCheckedKeysCopy, setDefaultCheckedKeysCopy] = useState(
    cloneDeep(defaultCheckedKeys),
  );
  // 选中数据的数组集合
  const [defaultCheckedList, setDefaultCheckedList] = useState([]);
  // 每个层级元素的集合
  const [dateList, setDateList] = useState([]);
  const [checkedList, setCheckedList] = useState([]);

  // 搜索
  const [searchData, setSearchData] = useState([]);

  // 搜索-选中项的值
  const [selectedItems, setSelectedItems] = useState([]);
  // 隐藏tag时显示的内容
  const [maxTagPlaceholder, setmaxTagPlaceholder] = useState('');

  const checkedRef = useRef(null);
  // 虚拟滚动的高度
  const [containerHeight, setContainerHeight] = useState(285);

  // 搜索-选中树节点时调用此函数
  const handleTreeSelectChange = (selectedValues: any) => {
    // 更新选中项
    setSelectedItems(selectedValues);
    // 更新选中值的nodeKey集合，从而更新已选项列表
    setDefaultCheckedKeysCopy(selectedValues);
    setmaxTagPlaceholder('');
  };
  // 搜索-展开下拉菜单的回调
  const onDropdownVisibleChange = (open: boolean) => {
    if (open) {
      // 下拉框展开时初始化选中项
      setSelectedItems([...defaultCheckedKeysCopy]);

      if (defaultCheckedKeysCopy?.length || selectedItems?.length) {
        setmaxTagPlaceholder('');
      } else {
        setmaxTagPlaceholder('搜索名称');
      }
    } else {
      setmaxTagPlaceholder('搜索名称');
    }
  };

  // 递归方法， 用来改变选中状态， 将选中状态的数据放入一个数组中用于右边展示
  const checkedRecursion = (val: any, type: any, arr: any) => {
    if (
      Array.isArray(val[defaultProps.children]) &&
      val[defaultProps.children].length
    ) {
      val[defaultProps.children].forEach((item: any) => {
        item.checkedState = type ?? false;
        checkedRecursion(item, type, arr);
      });
    } else {
      arr.push(val[defaultProps.value]);
    }
    return { val, type, arr };
  };
  // 子组件中选项改变后的回调方法
  const callBackClick = ({ changeList }: { changeList: any }) => {
    let defaultCheckedKeysArr = [...defaultCheckedKeysCopy]; // 默认已选中的数据
    changeList.forEach((item: any) => {
      // 添加到选中的id中
      if (item.checkedState) {
        const { arr } = checkedRecursion(item, true, []);
        defaultCheckedKeysArr = defaultCheckedKeysArr.concat(arr);
      } else {
        // 需要从选中id中删除
        const { arr } = checkedRecursion(item, true, []);
        // unCheckedArr = unCheckedArr.concat(arr)
        defaultCheckedKeysArr = defaultCheckedKeysArr.filter(function (item) {
          return !arr.includes(item);
        });
      }
    });
    // 去重
    defaultCheckedKeysArr = [...new Set(defaultCheckedKeysArr)];
    setDefaultCheckedKeysCopy(defaultCheckedKeysArr);
  };
  // 展示多个盒子
  const showSonBoxFun = ({ levelId, item }: { levelId: any; item: any }) => {
    const arr = [...dateList];
    // item.levelId的层级是几就保留多长的数组层级，再添加上item.children
    arr.splice(levelId);
    if (item && item[defaultProps.children]?.length) {
      const level = levelId + 1;
      const info = { ...item };
      info.keyId = item[defaultProps.value];
      info.levelId = level;
      info[defaultProps.children] = item[defaultProps.children];
      arr.push(info);
    }
    setDateList(arr);
  };

  // 清除所有选项
  const clearAll = () => {
    setDefaultCheckedKeysCopy([]);
  };
  // 删除单个的选项
  const singleClear = (item: any) => {
    let defaultCheckedKeysArr = [...defaultCheckedKeysCopy];
    defaultCheckedKeysArr = defaultCheckedKeysArr.filter(function (i) {
      if (item[defaultProps.value] !== i) {
        return i;
      }
    });
    setDefaultCheckedKeysCopy(defaultCheckedKeysArr);
  };

  // 根据id去拼接树结构
  const treeFilter = (tree: any[], func: any) => {
    return tree
      .map((node) => ({ ...node }))
      .filter((node) => {
        if (node.childNodeList) {
          node.childNodeList = treeFilter(node.childNodeList, func);
        }
        return func(node) || (node.childNodeList && node.childNodeList.length);
      });
  };
  // 将树形数据向下递归为一维数组； arr 数据源， childs  子集key
  const flattenDeep = (arr: any = [], childs = 'children') => {
    return arr.reduce((flat: any, item: any) => {
      return flat.concat(
        item,
        item[childs] ? flattenDeep(item[childs], childs) : [],
      );
    }, []);
  };
  // 改变父级的checkedBlm状态
  const updateParentType = (data: any) => {
    //  0-不选，1-选中状态， 2-半选
    let checkedState = 0;
    // 没有子级返回当前选中状态
    if (data[defaultProps.children]?.length === 0) {
      data.checkedState && (checkedState = 1);
      return checkedState;
    }
    // 循环子级 childTypes子级状态的集合
    const childTypes = data[defaultProps.children]?.map((child: any) =>
      updateParentType(child),
    );
    // 子级全部选中
    const allChildrenTrue = childTypes?.every((type: any) => type === 1);
    // 子级均未选中
    const allChildrenFalse = childTypes?.every((type: any) => type === 0);
    // 存在半选状态
    const allChildrenFalse2 = childTypes?.every((type: any) => type === 2);

    // 子级全选，当前选项的状态为选中状态
    if (allChildrenTrue) {
      data.checkedState = true;
      checkedState = 1;
    } else {
      data.checkedState = false;
      checkedState = 0;
    }
    data.indeterminateState = false;
    // 子级只有部分选中时，当前选项为半选状态
    if (!(allChildrenFalse || allChildrenTrue) || allChildrenFalse2) {
      data.indeterminateState = true;
      checkedState = 2;
    }
    return checkedState;
  };

  // 递归 初始化数据 defaultCheckedKeyArr-默认选中的数据的id集合
  const getOptionsData = (optionArr = [], defaultCheckedKeyArr = []) => {
    if (Array.isArray(optionArr) && Array.isArray(defaultCheckedKeyArr)) {
      optionArr.forEach((item) => {
        // 初始化checkedState状态， 是否在已选数据中
        if (defaultCheckedKeyArr.indexOf(item[defaultProps.value]) > -1) {
          item.checkedState = true;
        } else {
          item.checkedState = false;
        }
        item.indeterminateState = false;
        // 还有子级时进行递归
        if (item[defaultProps.children]?.length) {
          getOptionsData(item[defaultProps.children], defaultCheckedKeyArr);
        }
        // 改变数据的半选状态
        updateParentType(item);
      });
    }
    return optionArr;
  };

  // 查找treeData数组中没有子级的节点输出，并添加nodeNameInfo字段
  const processTreeData = (treeData = [], parentName = '') => {
    let processedNodes: any[] = [];
    treeData.forEach((item) => {
      const nodeName = item?.[defaultProps.label];
      const childNodeList = item?.[defaultProps.children];
      // 父级名称/子级名称
      const nodeNameInfo =
        parentName === '' ? nodeName : parentName + '/' + nodeName;
      // 存在子级继续递归
      if (childNodeList?.length) {
        processedNodes = processedNodes.concat(
          processTreeData(childNodeList, nodeNameInfo),
        );
      } else {
        item.nodeNameInfo = nodeNameInfo;
        processedNodes.push(item);
      }
    });
    return processedNodes;
  };

  useEffect(() => {
    // 搜索下拉展示数据
    let searchArr: any = [];
    searchArr = processTreeData(cloneDeep(options));
    setSearchData(searchArr);
  }, []);

  useEffect(() => {
    const optionsArr = cloneDeep(options); // 原数据结构数组
    setOptionsCopy(getOptionsData(optionsArr, defaultCheckedKeysCopy));
  }, [JSON.stringify(defaultCheckedKeysCopy)]);

  useEffect(() => {
    if (optionsCopy.length) {
      const flattenDeepArr = flattenDeep(optionsCopy, defaultProps.children);
      // 数据变更后 对dateList数组进行更新渲染
      if (dateList.length) {
        const dateListArr = dateList.map((item, index) => {
          // 第一级
          if (item.keyId === 999999) {
            item[defaultProps.children] = optionsCopy;
          }
          // 其他层级
          else {
            const filteredArray = flattenDeepArr.filter(
              (i: any) => i[defaultProps.value] === item.keyId,
            );
            if (filteredArray[0]) {
              item[defaultProps.children] =
                filteredArray[0][defaultProps.children];
            }
          }
          return item;
        });
        setDateList(dateListArr);
      } else {
        // 初始化传入的数组数据
        const itemInfo = [
          {
            keyId: 999999, // keyId用于区分组件
            levelId: 1, // 组件的层级，默认从1开始
            [defaultProps.children]: optionsCopy,
          },
        ];
        setDateList([...itemInfo]);
      }
      // 更新已选项列表
      let checkedArr: any = [];
      flattenDeepArr.forEach((item: any) => {
        if (
          !item[defaultProps.children].length &&
          defaultCheckedKeysCopy.includes(item[defaultProps.value])
        ) {
          // 将数组通过slice()先拷贝一份，之后用reverse()进行顺序的颠倒，使后选中的数据在已选列表的最顶端
          const reversedArray = defaultCheckedKeysCopy.slice().reverse();
          const inx = reversedArray.indexOf(item[defaultProps.value]);
          checkedArr[inx] = item;
        }
      });
      setCheckedList(checkedArr);

      // 数据更新完毕之后，需要对选中节点输出
      const defaultCheckedKeysCopyArr = cloneDeep(defaultCheckedKeysCopy);
      const treeInfo = treeFilter(cloneDeep(options), (node) =>
        defaultCheckedKeysCopyArr.includes(node[defaultProps.value]),
      );
      onChange(treeInfo, defaultCheckedKeysCopyArr);
    }
  }, [JSON.stringify(optionsCopy)]);

  return (
    <div>
      {/* 搜索框 */}
      <TreeSelect
        virtual
        multiple
        placeholder={'搜索名称'}
        value={selectedItems}
        treeData={searchData}
        autoClearSearchValue={false}
        treeCheckable={true}
        showCheckedStrategy="SHOW_CHILD"
        suffixIcon={<SearchOutlined />}
        maxTagCount={0}
        maxTagPlaceholder={
          <span style={{ color: '#ccc' }}>{maxTagPlaceholder}</span>
        }
        onDropdownVisibleChange={(val: any) => onDropdownVisibleChange(val)}
        fieldNames={{
          label: 'nodeNameInfo',
          value: defaultProps.value,
          children: defaultProps.children,
        }}
        onChange={(val: any) => handleTreeSelectChange(val)}
        filterTreeNode={(inputValue: any, treeNode: any) => {
          return (
            treeNode?.nodeNameInfo.includes(inputValue) ||
            treeNode?.nodeNameInfo.toLowerCase().includes(inputValue)
          );
        }}
        style={{ width: '450px', margin: '20px 0' }}
      ></TreeSelect>
      {/* 选择部分 */}
      <div>
        {dateList.map((item, index) => {
          return (
            <div
              key={item.keyId}
              style={{ display: 'inline-block', verticalAlign: 'top' }}
            >
              <CheckBoxComPage
                cardNum={dateList.length}
                levelId={item.levelId}
                options={item[defaultProps.children]}
                defaultProps={defaultProps}
                callBackClick={(changeList: any) =>
                  callBackClick({ changeList: changeList })
                }
                showSonBoxFun={(levelId: any, item: any) =>
                  showSonBoxFun({ levelId: levelId, item: item })
                }
              ></CheckBoxComPage>
            </div>
          );
        })}
        {/* 已选列表 */}
        <div
          className={styles.selectBox}
          style={{ display: 'inline-block', marginLeft: '16px' }}
        >
          <div className={styles.selectCard}>
            <div className={styles.selectTitle}>
              已选{checkedList.length}项
              <div
                onClick={() => clearAll()}
                style={{ float: 'right', cursor: 'pointer', color: '#366CFE' }}
              >
                清空
              </div>
            </div>
            <div ref={checkedRef} className={styles.selectcontent}>
              {checkedList.length ? (
                <VirtualList
                  data={checkedList}
                  height={containerHeight}
                  itemHeight={46}
                  itemKey={(item) => item?.[defaultProps.value]}
                >
                  {(item, index) => (
                    <div key={index} className={styles.checksTwo}>
                      <div className={styles.checksContent}>
                        {item[defaultProps.label]?.length > 10 ? (
                          <Tooltip
                            placement="top"
                            title={item[defaultProps.label]}
                          >
                            <span className={styles.checkedLabel}>
                              {item[defaultProps.label]}
                            </span>
                          </Tooltip>
                        ) : (
                          <span>{item[defaultProps.label]}</span>
                        )}
                        <CloseOutlined
                          onClick={() => singleClear(item)}
                          style={{
                            float: 'right',
                            cursor: 'pointer',
                            marginTop: '3px',
                          }}
                        />
                      </div>
                    </div>
                  )}
                </VirtualList>
              ) : (
                <div className={styles.noData}>暂无数据</div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TreeSelectCom;

/*
*  这是友善的小文档
* import TreeSelectCom from '@/components/TreeSelectCom/index'
* <TreeSelectCom
   options={[]} // 绑定的源数据
   defaultCheckedKeys={['XXX']} // 源数据 默认选中节点
   pid={'parentId'} // 父级id的属性名 （默认为 pid 字段）
   defaultProps={ {label: 'label',value: 'value', children: 'children'} } // 自定义节点 label、value、children 的字段
   onChange={(treeInfo, checkedKeys) => {} } // 选项改变的change事件； treeInfo：选中的节点(树结构); checkedKeys:选中的节点的id集合
 ></TreeSelectCom>
*
*
* */
