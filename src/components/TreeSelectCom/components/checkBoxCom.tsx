import React, { useEffect, useRef, useState } from 'react';
import { RightOutlined } from '@ant-design/icons';
import { Checkbox, Tooltip } from '@blmcp/ui';
import VirtualList from 'rc-virtual-list';
import styles from '../index.less';

const CheckBoxComPage = ({
  options = [],
  levelId = 1, // 代表层级
  defaultProps = { label: 'label', value: 'value', children: 'children' },
  callBackClick = (changeList: any) => {},
  showSonBoxFun = (levelId: any, item: any) => {},
  cardNum = null,
}) => {
  const [optionsList, SetOptionsList] = useState([]);
  // 选中的数组
  const [checkedList, setCheckedList] = useState([]);
  // 全选/全不选都是false； 只有半选的时候为true
  const indeterminateAll =
    checkedList.length > 0 && checkedList.length < optionsList.length;
  // 当前层级的全选按钮是否选中
  const [checkAll, setCheckAll] = useState(false);
  const containerRef = useRef(null);
  // 虚拟滚动的高度
  const [containerHeight, setContainerHeight] = useState(200);

  // 全选事件改变
  const onCheckAllChange = (e) => {
    let optionsArrCopy = [];
    optionsArrCopy = optionsList.map((i) => {
      i.checkedState = e.target.checked;
      return i;
    });
    SetOptionsList(optionsArrCopy);

    if (e.target.checked) {
      setCheckedList(optionsArrCopy);
    } else {
      setCheckedList([]);
    }

    callBackClick(optionsArrCopy);
  };

  // 某个选项改变的时候
  const onChangeSingle = ({ list, item }) => {
    // 阻止事件冒泡，防止点击文本时触发复选框的onClick事件
    list?.stopPropagation();
    // 对单个元素状态进行取反
    item.checkedState = !item.checkedState;
    let checkedListCopy = [...checkedList];
    // 判断选项的状态，决定是添加还是删除
    const resence = checkedListCopy.find(
      (i) => i[defaultProps.value] === item[defaultProps.value],
    );
    if (resence) {
      // 删除已存在的数据
      checkedListCopy = checkedListCopy.filter((info) => info !== item);
    } else {
      // 添加选中数据
      checkedListCopy.push(item);
    }
    setCheckedList(...[checkedListCopy]);
    // 选项改变的回调
    callBackClick([item]);
  };
  const onTextClick = (event) => {
    // 阻止默认行为，防止点击文本时选中复选框
    event?.preventDefault();
  };

  const showSonBox = ({ e, item }) => {
    showSonBoxFun(levelId, item);
    // 阻止默认行为，防止点击文本时选中复选框
    e?.preventDefault();
  };

  useEffect(() => {
    // 确认虚拟滚动的高度
    if (containerRef && containerRef.current) {
      setContainerHeight(containerRef.current.offsetHeight);
    }
  }, []);

  useEffect(() => {
    const _options = [...options];
    // 将选中状态的数据放入选中的数组中
    let list = [];
    _options.forEach((item) => {
      if (item.checkedState) {
        list.push(item);
      }
    });
    setCheckedList(list);
    SetOptionsList(_options);
  }, [JSON.stringify(options)]);

  useEffect(() => {
    // 改变全选的状态
    const isCheck = optionsList.length === checkedList.length;
    setCheckAll(isCheck);
  }, [checkedList, optionsList]);
  const getWidth = (minWidth = '225px', maxWidth = '450px') => {
    if (cardNum && cardNum === 1) {
      return {
        width: maxWidth,
      };
    } else {
      return {
        width: minWidth,
      };
    }
  };
  const getLength = (str) => {
    let len = 0;
    for (let i = 0; i < str.length; i++) {
      // 通过检查unicode编码来判断字符是否是中文字符
      if (str.charCodeAt(i) > 127) {
        len += 2; // 中文字符长度为2
      } else {
        len += 1; // 英文字符长度为1
      }
    }
    return len;
  };

  return (
    <div>
      <div className={styles.selectBox}>
        <div style={getWidth()} className={styles.selectCard}>
          <div className={styles.selectTitle}>
            {optionsList?.[0]?.labelName}
          </div>
          <div className={styles.checks}>
            <Checkbox
              indeterminate={indeterminateAll}
              onChange={onCheckAllChange}
              checked={checkAll}
            >
              全选
            </Checkbox>
          </div>
          <div ref={containerRef} className={styles.virtual}>
            <VirtualList
              data={optionsList}
              height={containerHeight}
              itemHeight={40}
              itemKey={(item) => item[defaultProps.value]}
            >
              {(item, index) => (
                <div
                  key={index}
                  className={styles.checks}
                  onClick={(val) => showSonBox({ e: val, item: item })}
                >
                  <Checkbox
                    indeterminate={item?.indeterminateState ?? false}
                    checked={item?.checkedState ?? false}
                    onChange={(val) =>
                      onChangeSingle({ list: val, item: item })
                    }
                    onClick={(e) => e.stopPropagation()}
                    style={{ height: '22px' }}
                  >
                    <div
                      onClick={onTextClick}
                      className={styles.checkedLabel}
                      style={getWidth('125px', '260px')}
                    >
                      {getLength(item?.[defaultProps.label]) > 20 ? (
                        <Tooltip
                          placement="top"
                          title={item?.[defaultProps.label]}
                        >
                          <span>{item?.[defaultProps.label]}</span>
                        </Tooltip>
                      ) : (
                        <span>{item?.[defaultProps.label]}</span>
                      )}
                    </div>
                  </Checkbox>
                  {item?.[defaultProps.children]?.length ? (
                    <RightOutlined
                      style={{ float: 'right', marginTop: '3px' }}
                    />
                  ) : null}
                </div>
              )}
            </VirtualList>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckBoxComPage;
