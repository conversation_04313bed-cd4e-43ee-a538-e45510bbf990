import { useRef, useEffect, MutableRefObject } from 'react';

type IntersectionObserverCallback = (entry: IntersectionObserverEntry) => void;

type IntersectionObserverOptions = {
  root?: Element | Document | null;
  rootMargin?: string;
  threshold?: number | number[];
};

const useIntersectionObserver = (
  callback: IntersectionObserverCallback,
  options?: IntersectionObserverOptions,
): MutableRefObject<null | HTMLElement> => {
  const targetRef = useRef<null | HTMLElement>(null);
  const observerRef = useRef<IntersectionObserver | null>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(([entry]) => {
      if (entry.isIntersecting) {
        callback(entry);
      }
    }, options);

    observerRef.current = observer;

    if (targetRef.current) {
      observer.observe(targetRef.current);
    }

    return () => {
      if (targetRef.current) {
        observerRef.current?.unobserve(targetRef.current);
      }
      observerRef.current?.disconnect();
    };
  }, [callback, options]);

  return targetRef;
};

export default useIntersectionObserver;
