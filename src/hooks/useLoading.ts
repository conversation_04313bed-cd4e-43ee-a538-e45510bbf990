//一个请求加loading的hook
// import { useState, useCallback } from 'react';
// export default function useLoading(request) {
//   const [loading, setLoading] = useState(false);
//   const wrapReq = useCallback((...args) => {
//     setLoading(true);
//     return request(...args)
//       .then((res) => {
//         setLoading(false);
//         return Promise.resolve(res);
//       })
//       .catch((err) => {
//         setLoading(false);
//         return Promise.reject(err);
//       });
//   });
//   return [loading, wrapReq];
// }

import { useState, useCallback } from 'react';

type RequestFunction<T> = (...args: any[]) => Promise<T>;

/**
 * A custom hook that wraps a request function and returns a tuple containing a boolean loading state and the wrapped request function.
 * @template T - The expected response type of the request function.
 * @param {RequestFunction<T>} request - The request function to be wrapped.
 * @returns {[boolean, RequestFunction<T>]} - A tuple containing a boolean loading state and the wrapped request function.
 */
const useLoading = <T>(
  request: RequestFunction<T>,
): [boolean, RequestFunction<T>] => {
  const [loading, setLoading] = useState(false);

  const wrapReq = useCallback(
    (...args: any[]): Promise<T> => {
      setLoading(true);
      return request(...args)
        .then((res: T) => {
          setLoading(false);
          return Promise.resolve(res);
        })
        .catch((err: any) => {
          setLoading(false);
          return Promise.reject(err);
        });
    },
    [request],
  );

  return [loading, wrapReq];
};

export default useLoading;
