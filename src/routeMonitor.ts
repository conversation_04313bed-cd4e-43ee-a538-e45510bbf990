// @ts-nocheck
const ROUTE_BASE = 'qbi';
// 路由base
const routePrefix = ROUTE_BASE.includes('/') ? ROUTE_BASE : `/${ROUTE_BASE}`;

// 匹配两个数组中每个内容是否相同
const judgePath = (index: number, listA: Array, currentList: Array) => {
  let value = false;
  for (let i = 0; i < index; i++) {
    // 匹配到动态路由无需精确比较，非动态路由需完全一样
    value = listA[i].includes(':') ? true : listA[i] === currentList[i];
    // 如果有一个path不一致，就退出循环
    if (!value) break;
  }
  return value;
};

// 动态路由确认路径是否一致
const getDynamicRoutePath = (pathName: string) => {
  const pathList = pathName.split('/');
  const currentPathList = location.pathname.split('/');
  let isMatch = false;
  if (pathName.includes('*')) {
    // 匹配到通用路由，比较*前每个位置上的path是否一致
    const prePathIndex = pathList.indexOf('*');
    isMatch = judgePath(prePathIndex, pathList, currentPathList);
  } else if (pathName.includes(':')) {
    // 匹配到动态路由
    const pathLen = pathList.length;
    const currentPathLen = currentPathList.length;
    // 长度不一致直接拦截，一致再比较每个位置的path名字是否一样
    isMatch =
      pathLen === currentPathLen
        ? judgePath(pathLen, pathList, currentPathList)
        : false;
  }
  return isMatch;
};

const publishRoute = (cuRoute) => {
  //通知路由变化
  const routeEvent = new CustomEvent('PLATFORM_ROUTER_CHANGE', {
    detail: {
      cuRoute,
    },
  });
  // 触发事件
  if (window.dispatchEvent) {
    window.dispatchEvent(routeEvent);
  } else {
    window.fireEvent(routeEvent);
  }
};

// 通用页面跳转基座处理
const redirectToBase = (newR) => {
  const newHref = `/${ROUTE_BASE}${newR}`;
  if (window.$baseRouter && window.$baseRouter.replace) {
    // 保证不刷新整个系统，复用基座的路由跳转方式跳转
    window.$baseRouter.replace(newHref);
  } else {
    window.location.replace(`${window.location.origin}${newHref}`);
  }
};

// 获取当前路由全路径
const findAllPath = (routes, current, path) => {
  let pathName = path;
  // 如果当前路由不已/开头，需拼接路由其父级
  const nest = /^\//.test(pathName);
  if (!nest && current.parentId) {
    const findParent = routes[current.parentId];
    return (pathName = findAllPath(
      routes,
      findParent,
      `${findParent.path}/${pathName}`,
    ));
  }
  return pathName;
};

// 注册路由监听
export function onRouteChange({ location, routes }: any) {
  const startTime = Date.now();
  const rou = location.pathname.split('/');
  // 如果不是本项目路由，直接拦截
  if (ROUTE_BASE !== rou[1]) return;

  // 当前项目所有路由匹配找当前页面路由
  const currentRoute = Object.values(routes).find((route: any) => {
    // 获取当前路由全路径，避免有嵌套路由场景
    let pathName = findAllPath(routes, route, route.path);
    // routes中的路由path不包含项目前缀 需要拼接一下
    pathName = `${routePrefix}${pathName}`;
    return (
      pathName === location.pathname ||
      (/[*:]/.test(pathName) && getDynamicRoutePath(pathName))
    );
  });

  // 找到对应页面
  if (currentRoute && currentRoute.pageKey) {
    let cuRoute;
    // 判断当前页面是否为菜单报告页面
    if (currentRoute.pageKey === 'legoReportPage') {
      cuRoute = {
        name: rou[4] || currentRoute.pageKey,
        path: '/qbi/legoBI/report/' + rou[4],
      };
      if (window.$BLM_BASE_GLOBAL_AUTH_RESOURCE?.flatMenuData) {
        const fRoute = window.$BLM_BASE_GLOBAL_AUTH_RESOURCE.flatMenuData.find(
          (f) => f.resourceUrl === location.pathname,
        );
        if (fRoute) {
          cuRoute.name = fRoute.resourceKey;
          cuRoute.path = fRoute.resourceUrl;
        }
      }
    } else if (currentRoute.pageKey === 'newLegoReportPage') {
      cuRoute = {
        name: rou[3] || currentRoute.pageKey,
        path: '/qbi/report/' + rou[3],
      };
      if (window.$BLM_BASE_GLOBAL_AUTH_RESOURCE?.flatMenuData) {
        const fRoute = window.$BLM_BASE_GLOBAL_AUTH_RESOURCE.flatMenuData.find(
          (f) => f.resourceUrl === location.pathname,
        );
        if (fRoute) {
          cuRoute.name = fRoute.resourceKey;
          cuRoute.path = fRoute.resourceUrl;
        }
      }
    } else {
      cuRoute = {
        name: currentRoute.pageKey || '',
        path: findAllPath(routes, currentRoute, currentRoute.path) || '',
      };
    }
    // 有currentRoute表明是本项目存在页面
    // 校验页面权限
    if (window.authVersionChangeCloudConfig) {
      window.BLMBaseRouterController(cuRoute, {}, (res) => {
        if (res) {
          // 满足路由拦截场景
          redirectToBase(res);
          return;
        }
      });
    }
    // 同步微前端基座当前路由
    publishRoute(cuRoute);
  } else {
    // 找不到页面处理
    const whiteList = ['welcome', '404', '403'];
    if (whiteList.includes(rou[2])) {
      // 通用页面场景需要周知基座更新store的curRoute，触发收起菜单操作
      publishRoute({
        name: '',
        path: currentRoute.path,
      });
    } else {
      // 处理其他无页面场景
      redirectToBase('/404');
    }
  }
  window.$baseEventReport &&
    window.$baseEventReport({ name: 'router', startTime, endTime: Date.now() });
}
