// import '@blmcp/ui/dist/lib/index.css';
// import '@blmcp/peento-businessComponents/dist/lib/BLMExport/style.css';
// import './utils/ba2';
// 运行时配置
import './index.less';
// 增加路由监听
export { onRouteChange } from './routeMonitor';
import { legoInit } from '@blm/bi-lego-sdk/utils';
import request from '@/utils/request';
import {
  asyncLoadCompontent,
  loadStyle,
  loadScript,
} from './utils/loadCompontent';
import Layout from './layouts/index';
// import ViewPage from './pages/lego/modules/viewPage';
// import ReportPage from './pages/lego/modules/reportPage';
import { legoGeneralizeSwitch } from './utils/sceneSwitch';
import { isHubble } from './utils/hubble';

if (legoGeneralizeSwitch()) {
  console.log('process.env.BUILD_ENV', process.env.BUILD_ENV);
  const testEnv = ['local', 'dev', 'sit', 'test', 'daily', 'daily2'];
  const envMap = {
    local: '1bf7ab7ac94bbf3d152ce27598fc243c',
    dev: '46d72856d9d2ad5475a6688e1f23b2c7',
    daily: '7d3e45cab1c85cf9b0d9d210a11757f8',
    pre2: 'a9f7f4357a7d8cc373541c256071ca60',
    pre: 'b885dc71c011927a2ee3d95c13a9166f',
    prod: '77ebb16c2cf2b423fa30476f8ecfb3a3',
  };
  legoInit({
    env: isHubble ? 'dev' : 'use',
    // @ts-expect-error
    key: envMap[process.env.BUILD_ENV] || envMap.prod,
    request,
    configApi: !isHubble
      ? '/bos/admin/v1/ai/platform/config/queryAppInfo'
      : '/lego/admin/v1/ai/chart-manager/config/queryAppDetail',
    cdnPrefix: `https://${
      // @ts-expect-error
      testEnv.includes(process.env.BUILD_ENV)
        ? 'webstatic'
        : window.cdnPubCosUrl || 'webstatic'
    }.yueyuechuxing.cn`,
  });
}

console.log('app 加载完时间55', Date.now() - window.htmlStartTime);
export function patchClientRoutes({ routes }: { routes: any[] }) {
  routes[0].element = <Layout />;
  asyncLoadCompontent(routes, {
    // 编辑页
    legoReportEditBI: () => {
      return Promise.all([
        loadScript(
          window.currentScript.replace(
            '<%= mainDomain %>',
            window.mainDomain || 'yueyuechuxing.cn',
          ),
        ),
        loadStyle(
          `https://${
            window.cdnPubCosUrl || 'webstatic'
          }.yueyuechuxing.cn/yueyue/admin/lego-base/v1.0.0/engine-core.css`,
        ),
      ]).then(() => import('./pages/newLego/editPage'));
    },
    // // 查看页
    // legoReportViewBI: () => {
    //   return legoGeneralizeSwitch()
    //     ? require('./pages/newLego/viewPage').default
    //     : require('./pages/lego/modules/viewPage').default;
    // },
    // // 菜单查看页
    // legoReportPage: () => {
    //   return legoGeneralizeSwitch()
    //     ? require('./pages/newLego/reportPage').default
    //     : require('./pages/lego/modules/reportPage').default;
    // },
    // newLegoReportPage: () => {
    //   return legoGeneralizeSwitch()
    //     ? require('./pages/newLego/reportPage').default
    //     : require('./pages/lego/modules/reportPage').default;
    // },
    // // 报告列表页
    // legoReportListBI: () => {
    //   return legoGeneralizeSwitch()
    //     ? require('./pages/newLego/pageList').default
    //     : require('./pages/lego/modules/pageList/index').default;
    // },
    // // 权限集分配页
    // legoPermissionBI: () => {
    //   return legoGeneralizeSwitch()
    //     ? require('./pages/newLego/permission').default
    //     : require('./pages/lego/modules/permission/index').default;
    // },
  });
}

export const qiankun = {
  async mount() {
    // eslint-disable-next-line @typescript-eslint/no-unused-expressions
    window.$baseEventReport &&
      window.$baseEventReport({ name: 'mount', time: Date.now() });
  },
};
