export {};

interface SceneActionStartMonitor {
  sceneId: string; // 场景ID：场景-描述-风险等级，与起点一致
  uniqueId: number | string; // 唯一ID，若相同场景下存在同时触发多次此动作场景，需保证单次动作唯一性标识，与起点一致
  maxTime?: number; // 否	单位s，默认10分钟，若超过最大有效时间未触发结束点上报，则上报异常
  newPage?: boolean; // 是否是多页场景，若为true，在无对应起点时触发此终点不上报异常。此场景用于业务方无需做缓存逻辑可频繁触发上报终点。
  noLogin?: boolean; // 否	是否走非登录态场景上报
}

declare global {
  interface Window {
    $MAppRouterEndTime: any;
    $MAppMountTime: any;
    $legoPageMontTime: any;
    $baseSelfReport: any;
    currentScript: string;
    $baseEventReport: any;
    $BLMLogReportCenter: {
      reportBusinessMonitor: (
        eventType: string,
        ext: object,
        level?: string,
      ) => void;
      sceneActionStartMonitor: (params: SceneActionStartMonitor) => void;
      sceneActionEndMonitor: (params: SceneActionStartMonitor) => void;
    };
    $BLMPermissionAuth: any;
    $legoAssets: any;
    BLMPermissionAuth: any;
    AliLowCodeEngine: any;
    LeopardWebQbiMeta: any;
    BlmAnalysis: any;
    htmlStartTime: number;
    $routerBeforeEachTime: number;
    $HbUtils: any;
    blmRequest: any;
    blmcpUi: any;
    blmBusinessComponents: any;
    mobileLib: any;
    lodash: any;
    legoConfigIsInit: boolean; // 是否已经初始化过legoConfig
    cdnPubCosUrl: string;
    SET_BASE_WINDOW: (key: string, value: any) => void;
  }
}
