{"js/ts.implicitProjectConfig.experimentalDecorators": false, "javascript.validate.enable": false, "typescript.validate.enable": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.formatOnSave": true, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[less]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[scss]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}, "[json]": {"editor.defaultFormatter": "vscode.json-language-features"}, "[svg]": {"editor.defaultFormatter": "jock.svg"}}