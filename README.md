# README

> `@umijs/max` 脚手架项目，更多功能参考 [Umi Max 简介](https://umijs.org/docs/max/introduce)

## 安装

执行`npm i -d`，安装项目依赖（需要`node`版本，否则可能会安装失败。最好是`14.x`，如`14.18.3`）

## 开发

本地执行：

```
npm run dev
npm run lowcode:dev
```

> 新增组件后需要重新执行`npm run lowcode:dev`

启动后访问[http://localhost:8000/qbi/legoBI/edit](http://localhost:8000/qbi/legoBI/edit)

## 新项目需要修改的地方

1. `package.json` 中 `name` 字段值修改为 子应用的名字
2. `.env` 中 `SYSTEM_SHORT_NAME` 字段值修改为 子应用的名字

## 使用公约

1. 子应用尽可能只在 `src/pages` 下面写具体业务代码
2. 文件路径即为url，文件路径禁止大写
3. 上面第二条暂时不强制，配合鉴权体系，还是写了显示的 `routes/index.ts` 路由声明文件，注意路由 `path` 一定全小写，绝对禁止大写

### 关于数据流

中后台场景下，绝大多数页面的数据流转都是在当前页完成，在页面挂载的时候请求后端接口获取并消费，这种场景下并不需要复杂的数据流方案。但是也存在需要全局共享的数据，如用户的角色权限信息或者其他一些页面间共享的数据。那么怎么才能缓存并支持在多个页面直接去共享这部分数据呢。

## 开发提示🔔

### 修改配置

请勿随意修改 config 配置  
如需修改，请复制 `config.ts` 文件，改名 `config.local.ts` 进行本地调试，此文件默认忽略，不会提交到代码仓库  
请勿随意修改 .env 配置  
如需修改，请复制 `.env` 文件，改名 `.env.local` 进行本地调试，此文件默认忽略，不会提交到代码仓库

### 调试

> <https://umijs.org/docs/guides/debug#xswitch>

### Mock接口数据

### 对比目前项目构建配置

- 移除 copy
- 移除 auto-commit
- 收敛打包构建环节，将构建逻辑放到插件中，将depoy部分逻辑放到打包机器上
- 本地关闭了mfsu
