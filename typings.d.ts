import '@umijs/max/typings';



declare global {

  type AnalysisFunction = (params: { pageId: string; eventId: string; ext?: object; }) => void
  interface Window {
    $ReactRefreshToken?: () => void;
    $ReactLogOut?: () => void;
    BlmAnalysis?: {
      pageView?: AnalysisFunction;
      pageLeave?: () => void;
      moduleClick?: AnalysisFunction;
      moduleExposure?: AnalysisFunction;
      
    };
  }
}