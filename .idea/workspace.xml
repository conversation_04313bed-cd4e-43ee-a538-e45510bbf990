<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="5e305ed0-cb29-499c-8283-881975e842ca" name="Changes" comment="feat: 解决冲突">
      <change afterPath="$PROJECT_DIR$/src/hooks/useLayoutUpdateEffect.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/lego/context/filterContext.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/NewCarTeamFilter/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/NewCityFilter/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/NewFleetFilter/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/wrapper/DDispatchWrapper.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/wrapper/FilterWrapper.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/wrapper/stateWrapper.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/lego/modules/editPage/services/defaultPageSchemaV2.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/lego/utils/visibleFilter.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/tenantListDemo/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/test/index.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/test/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/userPortrait/components/operationPool/components/preCalibrationModal/index.less" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/userPortrait/components/operationPool/components/preCalibrationModal/index.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/pages/userPortrait/components/operationPool/operationPoolOrg.tsx" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/bosProxy/Proxy.json" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/bosProxy/bosProxyMapV2.js" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/oneMessage.ts" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/src/utils/oriAndCategoryFlag.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/config/config.production.ts" beforeDir="false" afterPath="$PROJECT_DIR$/config/config.production.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/config/config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/config/config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/config/project.config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/config/project.config.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/lowcode-scan-plugin.js" beforeDir="false" afterPath="$PROJECT_DIR$/lowcode-scan-plugin.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/package.json" beforeDir="false" afterPath="$PROJECT_DIR$/package.json" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/routes/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/routes/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/components/LegoRender/src/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/components/LegoRender/src/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/global.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/global.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/api/hubble.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/api/hubble.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/api/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/api/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/api/requestQueue.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/api/requestQueue.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/CityConfig.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/CityConfig.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/LayerConfig.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/LayerConfig.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/cityCards.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/cityCards.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/cityDetailedData/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/cityDetailedData/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/reportDetails/fullyLayered.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/reportDetails/fullyLayered.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/reportDetails/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/AiAttribution/reportDetails/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/Card/index.less" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/Card/index.less" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/Card/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/Card/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/Filter/BLMCityCascader/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/Filter/BLMCityCascader/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/IndexCard/index.less" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/IndexCard/index.less" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/IndexCard/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/IndexCard/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/module/ChartContainer/index.less" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/module/ChartContainer/index.less" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/module/ComponentTitle/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/module/ComponentTitle/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/module/ComponentTitle/style.less" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/module/ComponentTitle/style.less" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/components/module/ComponentWrapper/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/components/module/ComponentWrapper/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/hooks/useComponentData.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/hooks/useComponentData.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/BrandFilter/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/BrandFilter/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/CityFilter/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/CityFilter/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/ListFilter/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/ListFilter/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/module/Linkage/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/module/Linkage/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/module/Query/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/module/Query/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/setter/AnalysisConfig/utils.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/setter/AnalysisConfig/utils.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/style.less" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/libraryMaterials/style.less" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/editPage/components/AddCalculateFields/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/editPage/components/AddCalculateFields/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/editPage/components/AddCalculateFields/style.less" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/editPage/components/AddCalculateFields/style.less" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/editPage/plugins/plugin-component-panel/pane/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/editPage/plugins/plugin-component-panel/pane/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/editPage/plugins/plugin-editor-init/pluginEditorInit.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/editPage/plugins/plugin-editor-init/pluginEditorInit.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/editPage/plugins/plugin-head-sample/event.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/editPage/plugins/plugin-head-sample/event.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/editPage/plugins/plugin-head-sample/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/editPage/plugins/plugin-head-sample/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/editPage/services/assets-antd.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/editPage/services/assets.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/editPage/services/defaultI18nSchema.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/editPage/services/mockService.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/editPage/services/mockService.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/editPage/services/schema.json" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/pageList/components/CommonTable/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/pageList/components/CommonTable/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/pageList/components/Tempate/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/pageList/components/Tempate/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/permission/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/permission/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/viewPage/AddAiaToSchem.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/viewPage/AddAiaToSchem.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/viewPage/LegoRenderer.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/viewPage/LegoRenderer.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/viewPage/layout/FDRow.jsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/viewPage/layout/FDRow.jsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/modules/viewPage/layout/style.scss" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/modules/viewPage/layout/style.scss" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/lego/utils/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/lego/utils/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/sub.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/sub.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/userPortrait/api/index.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/userPortrait/api/index.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/userPortrait/components/operationPool/components/carTeamBatchAdd/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/userPortrait/components/operationPool/components/carTeamBatchAdd/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/userPortrait/components/operationPool/components/cityBatchAdd/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/userPortrait/components/operationPool/components/cityBatchAdd/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/userPortrait/components/operationPool/index.less" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/userPortrait/components/operationPool/index.less" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/pages/userPortrait/index.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/src/pages/userPortrait/index.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/request.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/request.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/store.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/store.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/utils/updatePk.ts" beforeDir="false" afterPath="$PROJECT_DIR$/src/utils/updatePk.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="TypeScript JSX File" />
        <option value="TypeScript File" />
        <option value="Less File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="feature/2412/02_2And3" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitToolBoxStore">
    <option name="projectConfigVersion" value="5" />
    <option name="recentBranches">
      <RecentBranches>
        <option name="branchesForRepo">
          <list>
            <RecentBranchesForRepo>
              <option name="branches">
                <list>
                  <RecentBranch>
                    <option name="branchName" value="publishrel/241202_0103105240" />
                    <option name="lastUsedInstant" value="1735875239" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="feature/2412/02_2And3" />
                    <option name="lastUsedInstant" value="1735565485" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="master" />
                    <option name="lastUsedInstant" value="1735561695" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="sitrel/241202_4179" />
                    <option name="lastUsedInstant" value="1732777678" />
                  </RecentBranch>
                  <RecentBranch>
                    <option name="branchName" value="feature/2412/02_2And3_g" />
                    <option name="lastUsedInstant" value="1732777649" />
                  </RecentBranch>
                </list>
              </option>
              <option name="repositoryRootUrl" value="file://$PROJECT_DIR$" />
            </RecentBranchesForRepo>
          </list>
        </option>
      </RecentBranches>
    </option>
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2nEgnJwjIQncIXDLro9e3xf13Ku" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;list.type.of.created.stylesheet&quot;: &quot;Less&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.standard&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.stylelint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.standard&quot;: &quot;&quot;,
    &quot;node.js.selected.package.stylelint&quot;: &quot;/Applications/BLMproject/leopard-web-qbi/node_modules/stylelint&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;prettierjs.PrettierConfiguration.Package&quot;: &quot;/Applications/BLMproject/leopard-web-qbi/node_modules/prettier&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;actions.on.save&quot;,
    &quot;ts.external.directory.path&quot;: &quot;/Applications/BLMproject/leopard-web-qbi/node_modules/typescript/lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="5e305ed0-cb29-499c-8283-881975e842ca" name="Changes" comment="" />
      <created>1728543815551</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1728543815551</updated>
      <workItem from="1728543816945" duration="366000" />
      <workItem from="1728614166237" duration="1724000" />
      <workItem from="1728875297086" duration="122000" />
      <workItem from="1728982126788" duration="1400000" />
      <workItem from="1729059005389" duration="5020000" />
      <workItem from="1729066371685" duration="7479000" />
      <workItem from="1729081734938" duration="31668000" />
      <workItem from="1729240758134" duration="5000" />
      <workItem from="1729512488590" duration="625000" />
      <workItem from="1729754590268" duration="645000" />
      <workItem from="1730086450196" duration="19314000" />
      <workItem from="1730182555306" duration="4777000" />
      <workItem from="1730189994482" duration="707000" />
      <workItem from="1730194142361" duration="36000" />
      <workItem from="1730195469333" duration="337000" />
      <workItem from="1730268792289" duration="269000" />
      <workItem from="1730446408631" duration="2303000" />
      <workItem from="1730514380195" duration="22102000" />
      <workItem from="1730706959583" duration="209000" />
      <workItem from="1730776131721" duration="373000" />
      <workItem from="1730787186338" duration="1769000" />
      <workItem from="1730789920883" duration="129000" />
      <workItem from="1730790211188" duration="2931000" />
      <workItem from="1730797778193" duration="21763000" />
      <workItem from="1730860965480" duration="9673000" />
      <workItem from="1730894509478" duration="4688000" />
      <workItem from="1730912342991" duration="2703000" />
      <workItem from="1730917154432" duration="5841000" />
      <workItem from="1730967720747" duration="188000" />
      <workItem from="1730968233403" duration="202000" />
      <workItem from="1731293644930" duration="39371000" />
      <workItem from="1731485702884" duration="3675000" />
      <workItem from="1731496120622" duration="2962000" />
      <workItem from="1731588581882" duration="2714000" />
      <workItem from="1731608612418" duration="5374000" />
      <workItem from="1731644409139" duration="5999000" />
      <workItem from="1731659899618" duration="24341000" />
      <workItem from="1731897876416" duration="2050000" />
      <workItem from="1732074913415" duration="322000" />
      <workItem from="1732109224148" duration="58000" />
      <workItem from="1732147746213" duration="10578000" />
      <workItem from="1732192936678" duration="1220000" />
      <workItem from="1732256947520" duration="878000" />
      <workItem from="1732711009411" duration="1054000" />
      <workItem from="1732761396525" duration="360000" />
      <workItem from="1732761973732" duration="7422000" />
      <workItem from="1732872329832" duration="3106000" />
      <workItem from="1732937773091" duration="7207000" />
      <workItem from="1733060329455" duration="19000" />
      <workItem from="1733060372951" duration="1024000" />
      <workItem from="1733106256110" duration="5076000" />
      <workItem from="1733130636790" duration="15237000" />
      <workItem from="1733192797819" duration="1169000" />
      <workItem from="1733208230725" duration="2849000" />
      <workItem from="1733239168605" duration="34000" />
      <workItem from="1733239269457" duration="2702000" />
      <workItem from="1733460928318" duration="3575000" />
      <workItem from="1733715985981" duration="8643000" />
      <workItem from="1733815150031" duration="31000" />
      <workItem from="1733905605846" duration="24000" />
      <workItem from="1733906241990" duration="4000" />
      <workItem from="1734057808067" duration="4566000" />
      <workItem from="1734334436419" duration="229000" />
      <workItem from="1735180503493" duration="582000" />
      <workItem from="1735290661100" duration="2030000" />
      <workItem from="1735294980966" duration="7374000" />
      <workItem from="1735614282092" duration="1505000" />
      <workItem from="1735616264430" duration="1127000" />
      <workItem from="1735625200764" duration="1941000" />
      <workItem from="1735628010061" duration="4043000" />
      <workItem from="1735636054604" duration="6697000" />
      <workItem from="1735824055117" duration="942000" />
      <workItem from="1735875207879" duration="758000" />
    </task>
    <task id="LOCAL-00001" summary="feat: 组织模型组件更新1.0">
      <created>1729237815133</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1729237815133</updated>
    </task>
    <task id="LOCAL-00002" summary="feat: 组织模型组件逻辑修改">
      <created>1730187856687</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1730187856687</updated>
    </task>
    <task id="LOCAL-00003" summary="feat: 组织模型树级拼接">
      <created>1730542480014</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1730542480014</updated>
    </task>
    <task id="LOCAL-00004" summary="feat: 组织模型系统圈选校验">
      <created>1730707091906</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1730707091906</updated>
    </task>
    <task id="LOCAL-00005" summary="feat: 组织模型系统圈选校验">
      <created>1730859402660</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1730859402661</updated>
    </task>
    <task id="LOCAL-00006" summary="feat: 解决冲突">
      <created>1730864320211</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1730864320211</updated>
    </task>
    <task id="LOCAL-00007" summary="feat: 系统圈选修改">
      <created>1730873015248</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1730873015248</updated>
    </task>
    <task id="LOCAL-00008" summary="feat: 系统圈选创建接口增加参数">
      <created>1730874063452</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1730874063452</updated>
    </task>
    <task id="LOCAL-00009" summary="feat: 基础信息部分回退拆分出新组件">
      <created>1730909421858</created>
      <option name="number" value="00009" />
      <option name="presentableId" value="LOCAL-00009" />
      <option name="project" value="LOCAL" />
      <updated>1730909421858</updated>
    </task>
    <task id="LOCAL-00010" summary="feat: 组织模型解决复制链路问题">
      <created>1730909476544</created>
      <option name="number" value="00010" />
      <option name="presentableId" value="LOCAL-00010" />
      <option name="project" value="LOCAL" />
      <updated>1730909476544</updated>
    </task>
    <task id="LOCAL-00011" summary="feat: 组织模型层级结构拼接修改">
      <created>1730947232425</created>
      <option name="number" value="00011" />
      <option name="presentableId" value="LOCAL-00011" />
      <option name="project" value="LOCAL" />
      <updated>1730947232425</updated>
    </task>
    <task id="LOCAL-00012" summary="fix: 城市变更对应运力公司车队值无变化">
      <created>1730968329966</created>
      <option name="number" value="00012" />
      <option name="presentableId" value="LOCAL-00012" />
      <option name="project" value="LOCAL" />
      <updated>1730968329966</updated>
    </task>
    <task id="LOCAL-00013" summary="fix: 切换圈选力度后更新城市&amp;运力公司组件下拉数据">
      <created>1731315985336</created>
      <option name="number" value="00013" />
      <option name="presentableId" value="LOCAL-00013" />
      <option name="project" value="LOCAL" />
      <updated>1731315985336</updated>
    </task>
    <task id="LOCAL-00014" summary="feat: 人群标签交互调整">
      <created>1731354095235</created>
      <option name="number" value="00014" />
      <option name="presentableId" value="LOCAL-00014" />
      <option name="project" value="LOCAL" />
      <updated>1731354095235</updated>
    </task>
    <task id="LOCAL-00015" summary="feat: 人群标签交互调整">
      <created>1731378123923</created>
      <option name="number" value="00015" />
      <option name="presentableId" value="LOCAL-00015" />
      <option name="project" value="LOCAL" />
      <updated>1731378123923</updated>
    </task>
    <task id="LOCAL-00016" summary="feat: 预估人数出参结构修改">
      <created>1731486878574</created>
      <option name="number" value="00016" />
      <option name="presentableId" value="LOCAL-00016" />
      <option name="project" value="LOCAL" />
      <updated>1731486878574</updated>
    </task>
    <task id="LOCAL-00017" summary="feat: 预估人数出参结构修改">
      <created>1731488245690</created>
      <option name="number" value="00017" />
      <option name="presentableId" value="LOCAL-00017" />
      <option name="project" value="LOCAL" />
      <updated>1731488245690</updated>
    </task>
    <task id="LOCAL-00018" summary="fix: 预估人数报错">
      <created>1731489026240</created>
      <option name="number" value="00018" />
      <option name="presentableId" value="LOCAL-00018" />
      <option name="project" value="LOCAL" />
      <updated>1731489026240</updated>
    </task>
    <task id="LOCAL-00019" summary="fix: 预估人数-999场景兼容">
      <created>1731591055196</created>
      <option name="number" value="00019" />
      <option name="presentableId" value="LOCAL-00019" />
      <option name="project" value="LOCAL" />
      <updated>1731591055196</updated>
    </task>
    <task id="LOCAL-00020" summary="fix: 圈选粒度变更清除对应的树级结构">
      <created>1731612238371</created>
      <option name="number" value="00020" />
      <option name="presentableId" value="LOCAL-00020" />
      <option name="project" value="LOCAL" />
      <updated>1731612238371</updated>
    </task>
    <task id="LOCAL-00021" summary="fix: 城运车回填的时候组件数据源返回较慢导致层级结构获取不到">
      <created>1731648473674</created>
      <option name="number" value="00021" />
      <option name="presentableId" value="LOCAL-00021" />
      <option name="project" value="LOCAL" />
      <updated>1731648473674</updated>
    </task>
    <task id="LOCAL-00022" summary="feat: 圈选力度变更，清空选中的数据">
      <created>1731657564447</created>
      <option name="number" value="00022" />
      <option name="presentableId" value="LOCAL-00022" />
      <option name="project" value="LOCAL" />
      <updated>1731657564447</updated>
    </task>
    <task id="LOCAL-00023" summary="fix: 树及结构获取不正确导致接口报错">
      <created>1731837898573</created>
      <option name="number" value="00023" />
      <option name="presentableId" value="LOCAL-00023" />
      <option name="project" value="LOCAL" />
      <updated>1731837898573</updated>
    </task>
    <task id="LOCAL-00024" summary="fix: 点击按钮后loading">
      <created>1731858349645</created>
      <option name="number" value="00024" />
      <option name="presentableId" value="LOCAL-00024" />
      <option name="project" value="LOCAL" />
      <updated>1731858349645</updated>
    </task>
    <task id="LOCAL-00025" summary="fix: 修改执行时机">
      <created>1731859531714</created>
      <option name="number" value="00025" />
      <option name="presentableId" value="LOCAL-00025" />
      <option name="project" value="LOCAL" />
      <updated>1731859531714</updated>
    </task>
    <task id="LOCAL-00026" summary="fix: 修改执行时机">
      <created>1731866134758</created>
      <option name="number" value="00026" />
      <option name="presentableId" value="LOCAL-00026" />
      <option name="project" value="LOCAL" />
      <updated>1731866134758</updated>
    </task>
    <task id="LOCAL-00027" summary="fix: 车队权限问题修复">
      <created>1731920422177</created>
      <option name="number" value="00027" />
      <option name="presentableId" value="LOCAL-00027" />
      <option name="project" value="LOCAL" />
      <updated>1731920422177</updated>
    </task>
    <task id="LOCAL-00028" summary="feat: 系统圈选文案变化">
      <created>1732150890565</created>
      <option name="number" value="00028" />
      <option name="presentableId" value="LOCAL-00028" />
      <option name="project" value="LOCAL" />
      <updated>1732150890565</updated>
    </task>
    <task id="LOCAL-00029" summary="feat: 智能归因组件更换">
      <created>1732179470687</created>
      <option name="number" value="00029" />
      <option name="presentableId" value="LOCAL-00029" />
      <option name="project" value="LOCAL" />
      <updated>1732179470687</updated>
    </task>
    <task id="LOCAL-00030" summary="feat: 系统圈选城运车组件添加全选功能">
      <created>1732179554067</created>
      <option name="number" value="00030" />
      <option name="presentableId" value="LOCAL-00030" />
      <option name="project" value="LOCAL" />
      <updated>1732179554067</updated>
    </task>
    <task id="LOCAL-00031" summary="feat: 系统圈选城运组件权限粒度变更">
      <created>1732257144939</created>
      <option name="number" value="00031" />
      <option name="presentableId" value="LOCAL-00031" />
      <option name="project" value="LOCAL" />
      <updated>1732257144940</updated>
    </task>
    <task id="LOCAL-00032" summary="feat: 系统圈选预校验隐藏无权限提示">
      <created>1732764869515</created>
      <option name="number" value="00032" />
      <option name="presentableId" value="LOCAL-00032" />
      <option name="project" value="LOCAL" />
      <updated>1732764869515</updated>
    </task>
    <task id="LOCAL-00033" summary="feat: 代码优化/删除无用代码">
      <created>1732777328978</created>
      <option name="number" value="00033" />
      <option name="presentableId" value="LOCAL-00033" />
      <option name="project" value="LOCAL" />
      <updated>1732777328978</updated>
    </task>
    <task id="LOCAL-00034" summary="feat: 智能归因未进入分层配置页面时不调用对应接口">
      <created>1732874251907</created>
      <option name="number" value="00034" />
      <option name="presentableId" value="LOCAL-00034" />
      <option name="project" value="LOCAL" />
      <updated>1732874251907</updated>
    </task>
    <task id="LOCAL-00035" summary="feat: 隐藏智能归因模块">
      <created>1732945354687</created>
      <option name="number" value="00035" />
      <option name="presentableId" value="LOCAL-00035" />
      <option name="project" value="LOCAL" />
      <updated>1732945354687</updated>
    </task>
    <task id="LOCAL-00036" summary="feat: 隐藏智能归因模块">
      <created>1732947321863</created>
      <option name="number" value="00036" />
      <option name="presentableId" value="LOCAL-00036" />
      <option name="project" value="LOCAL" />
      <updated>1732947321863</updated>
    </task>
    <task id="LOCAL-00037" summary="fix: 智能归因单城市权限接口城市传参错误">
      <created>1733107575785</created>
      <option name="number" value="00037" />
      <option name="presentableId" value="LOCAL-00037" />
      <option name="project" value="LOCAL" />
      <updated>1733107575785</updated>
    </task>
    <task id="LOCAL-00038" summary="feat: 版本调试">
      <created>1733130805644</created>
      <option name="number" value="00038" />
      <option name="presentableId" value="LOCAL-00038" />
      <option name="project" value="LOCAL" />
      <updated>1733130805644</updated>
    </task>
    <task id="LOCAL-00039" summary="feat: 智能归因添加无权限逻辑">
      <created>1733161200412</created>
      <option name="number" value="00039" />
      <option name="presentableId" value="LOCAL-00039" />
      <option name="project" value="LOCAL" />
      <updated>1733161200412</updated>
    </task>
    <task id="LOCAL-00040" summary="feat: 智能归因解释说明添加新的文案">
      <created>1733209591561</created>
      <option name="number" value="00040" />
      <option name="presentableId" value="LOCAL-00040" />
      <option name="project" value="LOCAL" />
      <updated>1733209591561</updated>
    </task>
    <task id="LOCAL-00041" summary="fix: 空集合或空数组时逻辑报错BLMA-58697">
      <created>1733731792243</created>
      <option name="number" value="00041" />
      <option name="presentableId" value="LOCAL-00041" />
      <option name="project" value="LOCAL" />
      <updated>1733731792243</updated>
    </task>
    <task id="LOCAL-00042" summary="fix: 分层配置城市组件重置后没有将值回填给组件">
      <created>1735295597944</created>
      <option name="number" value="00042" />
      <option name="presentableId" value="LOCAL-00042" />
      <option name="project" value="LOCAL" />
      <updated>1735295597944</updated>
    </task>
    <task id="LOCAL-00043" summary="fix: 分层配置城市组件值与form表单值未关联导致未放量时回显失败">
      <created>1735641485505</created>
      <option name="number" value="00043" />
      <option name="presentableId" value="LOCAL-00043" />
      <option name="project" value="LOCAL" />
      <updated>1735641485506</updated>
    </task>
    <option name="localTasksCounter" value="44" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
    <option name="exactExcludedFiles">
      <list>
        <option value="$PROJECT_DIR$/src/.umi/core/loaders.js" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="OPEN_GENERIC_TABS">
      <map>
        <entry key="22acd5ca-11b4-4250-9a07-08b10aef29e9" value="TOOL_WINDOW" />
      </map>
    </option>
    <option name="RECENT_FILTERS">
      <map>
        <entry key="User">
          <value>
            <list>
              <RecentGroup>
                <option name="FILTER_VALUES">
                  <option value="*" />
                </option>
              </RecentGroup>
            </list>
          </value>
        </entry>
      </map>
    </option>
    <option name="TAB_STATES">
      <map>
        <entry key="22acd5ca-11b4-4250-9a07-08b10aef29e9">
          <value>
            <State>
              <option name="CUSTOM_BOOLEAN_PROPERTIES">
                <map>
                  <entry key="Show.Git.Branches" value="true" />
                </map>
              </option>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="HEAD" />
                      </list>
                    </value>
                  </entry>
                  <entry key="roots">
                    <value>
                      <list>
                        <option value="$PROJECT_DIR$" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
              <option name="SHOW_ONLY_AFFECTED_CHANGES" value="true" />
            </State>
          </value>
        </entry>
        <entry key="MAIN">
          <value>
            <State>
              <option name="FILTERS">
                <map>
                  <entry key="branch">
                    <value>
                      <list>
                        <option value="feature/2412/02_2And3" />
                      </list>
                    </value>
                  </entry>
                </map>
              </option>
            </State>
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="fix: 预估人数报错" />
    <MESSAGE value="fix: 预估人数-999场景兼容" />
    <MESSAGE value="fix: 圈选粒度变更清除对应的树级结构" />
    <MESSAGE value="fix: 城运车回填的时候组件数据源返回较慢导致层级结构获取不到" />
    <MESSAGE value="feat: 圈选力度变更，清空选中的数据" />
    <MESSAGE value="fix: 树及结构获取不正确导致接口报错" />
    <MESSAGE value="fix: 点击按钮后loading" />
    <MESSAGE value="fix: 修改执行时机" />
    <MESSAGE value="fix: 车队权限问题修复" />
    <MESSAGE value="feat: 系统圈选文案变化" />
    <MESSAGE value="feat: 智能归因组件更换" />
    <MESSAGE value="feat: 系统圈选城运车组件添加全选功能" />
    <MESSAGE value="feat: 系统圈选城运组件权限粒度变更" />
    <MESSAGE value="feat: 系统圈选预校验隐藏无权限提示" />
    <MESSAGE value="feat: 代码优化/删除无用代码" />
    <MESSAGE value="feat: 智能归因未进入分层配置页面时不调用对应接口" />
    <MESSAGE value="feat: 隐藏智能归因模块" />
    <MESSAGE value="fix: 智能归因单城市权限接口城市传参错误" />
    <MESSAGE value="feat: 版本调试" />
    <MESSAGE value="feat: 智能归因添加无权限逻辑" />
    <MESSAGE value="feat: 智能归因解释说明添加新的文案" />
    <MESSAGE value="fix: 空集合或空数组时逻辑报错BLMA-58697" />
    <MESSAGE value="fix: 分层配置城市组件重置后没有将值回填给组件" />
    <MESSAGE value="fix: 分层配置城市组件值与form表单值未关联导致未放量时回显失败" />
    <MESSAGE value="feat: 解决冲突" />
    <option name="LAST_COMMIT_MESSAGE" value="feat: 解决冲突" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" type="javascript">
          <url>file://$PROJECT_DIR$/src/pages/lego/modules/pageList/components/HubblePage/schema.tsx</url>
          <line>146</line>
          <option name="timeStamp" value="1" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>