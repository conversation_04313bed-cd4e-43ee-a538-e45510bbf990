project: CP4.1
env:
  packageManager: npm
  node: 14
app:
  qbi:
    daily,daily2:
      webServeHtml: true
      output: dist
      ossEnv: test
      remoteOssPath: yueyue/admin
      stage:
        build:
          - npm run lint
          - npm run build:daily
    sit,dev:
      webServeHtml: true
      output: dist
      ossEnv: test
      remoteOssPath: yueyue/admin
      stage:
        build:
          - npm run lint
          - npm run build:dev
    pre2:
      webServeHtml: true
      output: dist
      ossEnv: prod
      remoteOssPath: yueyue/admin
      stage:
        build:
          - npm run lint
          - npm run build:pre2
    pre:
      webServeHtml: true
      output: dist
      ossEnv: prod
      remoteOssPath: yueyue/admin
      stage:
        build:
          - npm run lint
          - npm run build:pre
    publish:
      webServeHtml: true
      output: dist
      ossEnv: prod
      remoteOssPath: yueyue/admin
      stage:
        build:
          - npm run lint
          - npm run build:prod
