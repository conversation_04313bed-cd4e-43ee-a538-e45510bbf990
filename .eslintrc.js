module.exports = {
  extends: require.resolve('@umijs/max/eslint'),
  plugins: ["import"],
  rules: {
    'react-hooks/rules-of-hooks': 1,
    'react-hooks/exhaustive-deps': 1,
    "import/newline-after-import": ["error", { "count": 1 }],
    "import/order": [
      "error",
      {
        groups: ["builtin", "external", "internal", "type", "parent", "sibling", "object", "index"],
        "pathGroups": [
          {
            "pattern": "@/**",
            "group": "external",
            "position": "after"
          }
        ],
      }
    ],
  },
};
