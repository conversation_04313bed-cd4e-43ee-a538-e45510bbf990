const mateTs = [
  'ComponentBehavior',
  'ComponentRule',
  'ComponentExtendField',
  'LinkSetterComponent',
  'MergedComponentMate',
];
const path = require('path');
const fs = require('fs');
const babelParser = require('@babel/parser');
const { merge } = require('lodash');

function camelToKebab(camelCase) {
  if (camelCase[0] === camelCase[0].toUpperCase()) {
    // eslint-disable-next-line no-param-reassign
    camelCase = camelCase.replace(camelCase[0], camelCase[0].toLowerCase());
  }
  return camelCase.replace(/[A-Z]/g, (match) => `-${match.toLowerCase()}`);
}
module.exports = class {
  static name = 'lowcodeScanPlugin';

  constructor(op) {
    this.tsMap = new Map();
    Promise.resolve().then(() => {
      this.option = this.getSetting(op);
      this.directory = path.resolve('./', this.option.directory);
    });
  }

  // ExportNamedDeclaration({url},{node}){
  //   if(node?.declaration?.type === 'TSInterfaceDeclaration' && mateTs.includes(node?.declaration?.id.name)){
  //     if(node.declaration.body.type === 'TSInterfaceBody'){
  //       const path = url.split(this.directory)[1].split('/')[1]
  //       const attrObj = this.tsMap.get(path) || this.createJSON()
  //       node.declaration.body.body.forEach((v)=>{
  //         if(v?.typeAnnotation?.typeAnnotation?.literal){
  //           attrObj[node.declaration.id.name][v.key.name] = v.typeAnnotation.typeAnnotation.literal.value
  //         }
  //       })
  //       this.tsMap.set(path, attrObj)
  //     }
  //   }
  // }

  ExportNamedDeclaration({ url }, { node }) {
    if (node.declaration && node.declaration.declarations) {
      const fnode = node.declaration.declarations.find((f) =>
        mateTs.includes(f.id.name),
      );
      if (fnode) {
        const { getJsNodeValue } = require('router-scan-bigbang/lib/utils');
        const path = url.split(this.directory)[1].split('/')[1];
        const attrObj = this.tsMap.get(path) || this.createJSON();
        fnode.init.properties.forEach((v) => {
          Object.assign(attrObj[fnode?.id.name], getJsNodeValue(v));
        });
        this.tsMap.set(path, attrObj);
      }
    }
  }

  AssignmentExpression({ url }, { node }) {
    if (node?.left?.property?.name === 'displayName') {
      const path = url.split(this.directory)[1]?.split('/')?.[1];
      if (path) {
        const attrObj = this.tsMap.get(path) || this.createJSON();
        attrObj.componentName = node.right.value;
      }
    }
  }

  transFromRouterData(data) {
    const { getFullPath } = require('router-scan-bigbang/lib/utils');
    data.push({
      [this.option.routerPathKey]: 'lowcodeScanPlugin',
      [this.option.routerRkKey]: '*',
      [this.option.routerComponentKey]: '*',
      [this.option.routerNameKey]: '*',
      entry: [
        getFullPath(this.option.projectPath + '/src', this.option.libEntry),
      ],
    });
  }
  async ScanDone() {
    const {
      ensureDirectoryExistence,
      getJsNodeQuoteTypeValue,
    } = require('router-scan-bigbang/lib/utils');
    this.tsMap.forEach((obj, pathUrl) => {
      const metaUrl = path.resolve(
        './',
        this.option.metaPath,
        camelToKebab(pathUrl) + '/meta.ts',
      );
      const script = fs.readFileSync(metaUrl, 'utf-8');

      const AST = babelParser.parse(script, {
        allowImportExportEverywhere: true,
        errorRecovery: true,
        decoratorsBeforeExport: true,
        plugins: ['jsx', 'typescript', 'classProperties'],
      });

      const match = script.match(
        /const ([^]*?): IPublicTypeComponentMetadata = {/,
      );
      // const repRue = new RegExp(`${match[1]}.configure.component = ComponentRule;`,'g')
      // const meta = script.replace(/[\s]{0,}const +(componentBehavior|ComponentExtendField|ComponentRule)[\s]{0,}:[\s]{0,}any[\s]{0,}=[\s]{0,}{[^]*?};/g,'');

      const ast_var_node = AST.program.body.filter(
        (v) => v.type === 'VariableDeclaration',
      );
      let metaConfig, snippets;
      ast_var_node.forEach((node) => {
        node.declarations.forEach((item) => {
          if (item.id && item.id.name === match[1]) {
            metaConfig = getJsNodeQuoteTypeValue(item.init);
          } else if (item.id && item.id.name === 'snippets') {
            snippets = getJsNodeQuoteTypeValue(item.init);
          }
        });
      });
      metaConfig.configure.props = this.createConfigJSON(
        obj.LinkSetterComponent,
      );
      // if('isExtend' in obj.ComponentRule){
      //   metaConfig.configure.isExtend = obj.ComponentRule.isExtend
      //   delete obj.ComponentRule.isExtend
      // }
      metaConfig.configure.component = obj.ComponentRule;
      metaConfig.name = obj.componentName;

      if (typeof obj.MergedComponentMate === 'object') {
        if (obj.MergedComponentMate.snippets) {
          const _snippets = obj.MergedComponentMate.snippets;
          delete obj.MergedComponentMate.snippets;
          merge(snippets, _snippets);
        }
        merge(metaConfig, obj.MergedComponentMate);
      }

      if (obj.LinkSetterComponent.dataSetConfig) {
        obj.ComponentBehavior.dataSetConfig = {};
        const config = obj.LinkSetterComponent.dataSetConfig?.props?.list || [];
        config.forEach((v) => {
          obj.ComponentBehavior.dataSetConfig[v.key] = {
            limit: v.limit || [1, 1],
            type: v.type || 'dhcr',
            to: v.to,
          };
        });
      }

      const mewMeta = `
import { IPublicTypeComponentMetadata, IPublicTypeSnippet } from '@alilc/lowcode-types';

const ${match[1]}: IPublicTypeComponentMetadata = ${JSON.stringify(
        metaConfig,
        null,
        2,
      )};
const snippets: IPublicTypeSnippet[] = ${JSON.stringify(snippets, null, 2)};
const componentBehavior: any = ${JSON.stringify(
        obj.ComponentBehavior,
        null,
        2,
      )};
const ComponentExtendField: any = ${JSON.stringify(
        obj.ComponentExtendField,
        null,
        2,
      )};
export default {
  ...${match[1]},
  ...ComponentExtendField,
  componentBehavior,
  snippets
};`;
      console.log('执行写入');
      ensureDirectoryExistence(metaUrl, mewMeta);
    });
  }

  createJSON() {
    const obj = {};
    mateTs.forEach((v) => {
      obj[v] = {};
    });
    return obj;
  }

  createConfigJSON(ob) {
    let arr = [];
    // eslint-disable-next-line guard-for-in
    for (let k in ob) {
      const item = ob[k];
      const op = {
        sort: item.sort,
        title: {
          label: {
            type: 'i18n',
            'en-US': item.label,
            'zh-CN': item.label,
          },
        },
        name: k,
        defaultValue: item.defaultValue,
        setter: {
          componentName: item.componentName,
          props: item.props,
        },
      };
      arr.push(op);
    }
    return arr.sort((a, b) => a.sort > b.sort);
  }
};
