/*! For license information please see factory.js.LICENSE.txt */
(()=>{var e={762:(e,a,n)=>{e.exports={parallel:n(5131),serial:n(3356),serialOrdered:n(8423)}},4552:e=>{function a(e){"function"==typeof this.jobs[e]&&this.jobs[e]()}e.exports=function(e){Object.keys(e.jobs).forEach(a.bind(e)),e.jobs={}}},2818:(e,a,n)=>{var i=n(8754);e.exports=function(e){var a=!1;return i((function(){a=!0})),function(n,o){a?e(n,o):i((function(){e(n,o)}))}}},8754:e=>{e.exports=function(e){var a="function"==typeof setImmediate?setImmediate:"object"==typeof process&&"function"==typeof process.nextTick?process.nextTick:null;a?a(e):setTimeout(e,0)}},3748:(e,a,n)=>{var i=n(2818),o=n(4552);e.exports=function(e,a,n,s){var t=n.keyedList?n.keyedList[n.index]:n.index;n.jobs[t]=function(e,a,n,o){return 2==e.length?e(n,i(o)):e(n,a,i(o))}(a,t,e[t],(function(e,a){t in n.jobs&&(delete n.jobs[t],e?o(n):n.results[t]=a,s(e,n.results))}))}},5783:e=>{e.exports=function(e,a){var n=!Array.isArray(e),i={index:0,keyedList:n||a?Object.keys(e):null,jobs:{},results:n?{}:[],size:n?Object.keys(e).length:e.length};return a&&i.keyedList.sort(n?a:function(n,i){return a(e[n],e[i])}),i}},8469:(e,a,n)=>{var i=n(4552),o=n(2818);e.exports=function(e){Object.keys(this.jobs).length&&(this.index=this.size,i(this),o(e)(null,this.results))}},5131:(e,a,n)=>{var i=n(3748),o=n(5783),s=n(8469);e.exports=function(e,a,n){for(var t=o(e);t.index<(t.keyedList||e).length;)i(e,a,t,(function(e,a){e?n(e,a):0!==Object.keys(t.jobs).length||n(null,t.results)})),t.index++;return s.bind(t,n)}},3356:(e,a,n)=>{var i=n(8423);e.exports=function(e,a,n){return i(e,a,null,n)}},8423:(e,a,n)=>{var i=n(3748),o=n(5783),s=n(8469);function t(e,a){return e<a?-1:e>a?1:0}e.exports=function(e,a,n,t){var r=o(e,n);return i(e,a,r,(function n(o,s){o?t(o,s):(r.index++,r.index<(r.keyedList||e).length?i(e,a,r,n):t(null,r.results))})),s.bind(r,t)},e.exports.ascending=t,e.exports.descending=function(e,a){return-1*t(e,a)}},7012:e=>{var a={utf8:{stringToBytes:function(e){return a.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(a.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var a=[],n=0;n<e.length;n++)a.push(255&e.charCodeAt(n));return a},bytesToString:function(e){for(var a=[],n=0;n<e.length;n++)a.push(String.fromCharCode(e[n]));return a.join("")}}};e.exports=a},2047:(e,a,n)=>{var i=n(9023),o=n(2203).Stream,s=n(3038);function t(){this.writable=!1,this.readable=!0,this.dataSize=0,this.maxDataSize=2097152,this.pauseStreams=!0,this._released=!1,this._streams=[],this._currentStream=null,this._insideLoop=!1,this._pendingNext=!1}e.exports=t,i.inherits(t,o),t.create=function(e){var a=new this;for(var n in e=e||{})a[n]=e[n];return a},t.isStreamLike=function(e){return"function"!=typeof e&&"string"!=typeof e&&"boolean"!=typeof e&&"number"!=typeof e&&!Buffer.isBuffer(e)},t.prototype.append=function(e){if(t.isStreamLike(e)){if(!(e instanceof s)){var a=s.create(e,{maxDataSize:1/0,pauseStream:this.pauseStreams});e.on("data",this._checkDataSize.bind(this)),e=a}this._handleErrors(e),this.pauseStreams&&e.pause()}return this._streams.push(e),this},t.prototype.pipe=function(e,a){return o.prototype.pipe.call(this,e,a),this.resume(),e},t.prototype._getNext=function(){if(this._currentStream=null,this._insideLoop)this._pendingNext=!0;else{this._insideLoop=!0;try{do{this._pendingNext=!1,this._realGetNext()}while(this._pendingNext)}finally{this._insideLoop=!1}}},t.prototype._realGetNext=function(){var e=this._streams.shift();void 0!==e?"function"==typeof e?e(function(e){t.isStreamLike(e)&&(e.on("data",this._checkDataSize.bind(this)),this._handleErrors(e)),this._pipeNext(e)}.bind(this)):this._pipeNext(e):this.end()},t.prototype._pipeNext=function(e){if(this._currentStream=e,t.isStreamLike(e))return e.on("end",this._getNext.bind(this)),void e.pipe(this,{end:!1});var a=e;this.write(a),this._getNext()},t.prototype._handleErrors=function(e){var a=this;e.on("error",(function(e){a._emitError(e)}))},t.prototype.write=function(e){this.emit("data",e)},t.prototype.pause=function(){this.pauseStreams&&(this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.pause&&this._currentStream.pause(),this.emit("pause"))},t.prototype.resume=function(){this._released||(this._released=!0,this.writable=!0,this._getNext()),this.pauseStreams&&this._currentStream&&"function"==typeof this._currentStream.resume&&this._currentStream.resume(),this.emit("resume")},t.prototype.end=function(){this._reset(),this.emit("end")},t.prototype.destroy=function(){this._reset(),this.emit("close")},t.prototype._reset=function(){this.writable=!1,this._streams=[],this._currentStream=null},t.prototype._checkDataSize=function(){if(this._updateDataSize(),!(this.dataSize<=this.maxDataSize)){var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this._emitError(new Error(e))}},t.prototype._updateDataSize=function(){this.dataSize=0;var e=this;this._streams.forEach((function(a){a.dataSize&&(e.dataSize+=a.dataSize)})),this._currentStream&&this._currentStream.dataSize&&(this.dataSize+=this._currentStream.dataSize)},t.prototype._emitError=function(e){this._reset(),this.emit("error",e)}},6480:e=>{var a,n;a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n={rotl:function(e,a){return e<<a|e>>>32-a},rotr:function(e,a){return e<<32-a|e>>>a},endian:function(e){if(e.constructor==Number)return 16711935&n.rotl(e,8)|4278255360&n.rotl(e,24);for(var a=0;a<e.length;a++)e[a]=n.endian(e[a]);return e},randomBytes:function(e){for(var a=[];e>0;e--)a.push(Math.floor(256*Math.random()));return a},bytesToWords:function(e){for(var a=[],n=0,i=0;n<e.length;n++,i+=8)a[i>>>5]|=e[n]<<24-i%32;return a},wordsToBytes:function(e){for(var a=[],n=0;n<32*e.length;n+=8)a.push(e[n>>>5]>>>24-n%32&255);return a},bytesToHex:function(e){for(var a=[],n=0;n<e.length;n++)a.push((e[n]>>>4).toString(16)),a.push((15&e[n]).toString(16));return a.join("")},hexToBytes:function(e){for(var a=[],n=0;n<e.length;n+=2)a.push(parseInt(e.substr(n,2),16));return a},bytesToBase64:function(e){for(var n=[],i=0;i<e.length;i+=3)for(var o=e[i]<<16|e[i+1]<<8|e[i+2],s=0;s<4;s++)8*i+6*s<=8*e.length?n.push(a.charAt(o>>>6*(3-s)&63)):n.push("=");return n.join("")},base64ToBytes:function(e){e=e.replace(/[^A-Z0-9+\/]/gi,"");for(var n=[],i=0,o=0;i<e.length;o=++i%4)0!=o&&n.push((a.indexOf(e.charAt(i-1))&Math.pow(2,-2*o+8)-1)<<2*o|a.indexOf(e.charAt(i))>>>6-2*o);return n}},e.exports=n},2446:(e,a,n)=>{a.formatArgs=function(a){if(a[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+a[0]+(this.useColors?"%c ":" ")+"+"+e.exports.humanize(this.diff),!this.useColors)return;const n="color: "+this.color;a.splice(1,0,n,"color: inherit");let i=0,o=0;a[0].replace(/%[a-zA-Z%]/g,(e=>{"%%"!==e&&(i++,"%c"===e&&(o=i))})),a.splice(o,0,n)},a.save=function(e){try{e?a.storage.setItem("debug",e):a.storage.removeItem("debug")}catch(e){}},a.load=function(){let e;try{e=a.storage.getItem("debug")}catch(e){}return!e&&"undefined"!=typeof process&&"env"in process&&(e=process.env.DEBUG),e},a.useColors=function(){return!("undefined"==typeof window||!window.process||"renderer"!==window.process.type&&!window.process.__nwjs)||("undefined"==typeof navigator||!navigator.userAgent||!navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))&&("undefined"!=typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!=typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||"undefined"!=typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/))},a.storage=function(){try{return localStorage}catch(e){}}(),a.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),a.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],a.log=console.debug||console.log||(()=>{}),e.exports=n(4689)(a);const{formatters:i}=e.exports;i.j=function(e){try{return JSON.stringify(e)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}},4689:(e,a,n)=>{e.exports=function(e){function a(e){let n,o,s,t=null;function r(...e){if(!r.enabled)return;const i=r,o=Number(new Date),s=o-(n||o);i.diff=s,i.prev=n,i.curr=o,n=o,e[0]=a.coerce(e[0]),"string"!=typeof e[0]&&e.unshift("%O");let t=0;e[0]=e[0].replace(/%([a-zA-Z%])/g,((n,o)=>{if("%%"===n)return"%";t++;const s=a.formatters[o];if("function"==typeof s){const a=e[t];n=s.call(i,a),e.splice(t,1),t--}return n})),a.formatArgs.call(i,e),(i.log||a.log).apply(i,e)}return r.namespace=e,r.useColors=a.useColors(),r.color=a.selectColor(e),r.extend=i,r.destroy=a.destroy,Object.defineProperty(r,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==t?t:(o!==a.namespaces&&(o=a.namespaces,s=a.enabled(e)),s),set:e=>{t=e}}),"function"==typeof a.init&&a.init(r),r}function i(e,n){const i=a(this.namespace+(void 0===n?":":n)+e);return i.log=this.log,i}function o(e){return e.toString().substring(2,e.toString().length-2).replace(/\.\*\?$/,"*")}return a.debug=a,a.default=a,a.coerce=function(e){return e instanceof Error?e.stack||e.message:e},a.disable=function(){const e=[...a.names.map(o),...a.skips.map(o).map((e=>"-"+e))].join(",");return a.enable(""),e},a.enable=function(e){let n;a.save(e),a.namespaces=e,a.names=[],a.skips=[];const i=("string"==typeof e?e:"").split(/[\s,]+/),o=i.length;for(n=0;n<o;n++)i[n]&&("-"===(e=i[n].replace(/\*/g,".*?"))[0]?a.skips.push(new RegExp("^"+e.slice(1)+"$")):a.names.push(new RegExp("^"+e+"$")))},a.enabled=function(e){if("*"===e[e.length-1])return!0;let n,i;for(n=0,i=a.skips.length;n<i;n++)if(a.skips[n].test(e))return!1;for(n=0,i=a.names.length;n<i;n++)if(a.names[n].test(e))return!0;return!1},a.humanize=n(9173),a.destroy=function(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")},Object.keys(e).forEach((n=>{a[n]=e[n]})),a.names=[],a.skips=[],a.formatters={},a.selectColor=function(e){let n=0;for(let a=0;a<e.length;a++)n=(n<<5)-n+e.charCodeAt(a),n|=0;return a.colors[Math.abs(n)%a.colors.length]},a.enable(a.load()),a}},6222:(e,a,n)=>{"undefined"==typeof process||"renderer"===process.type||!0===process.browser||process.__nwjs?e.exports=n(2446):e.exports=n(8676)},8676:(e,a,n)=>{const i=n(2018),o=n(9023);a.init=function(e){e.inspectOpts={};const n=Object.keys(a.inspectOpts);for(let i=0;i<n.length;i++)e.inspectOpts[n[i]]=a.inspectOpts[n[i]]},a.log=function(...e){return process.stderr.write(o.format(...e)+"\n")},a.formatArgs=function(n){const{namespace:i,useColors:o}=this;if(o){const a=this.color,o="[3"+(a<8?a:"8;5;"+a),s=`  ${o};1m${i} [0m`;n[0]=s+n[0].split("\n").join("\n"+s),n.push(o+"m+"+e.exports.humanize(this.diff)+"[0m")}else n[0]=(a.inspectOpts.hideDate?"":(new Date).toISOString()+" ")+i+" "+n[0]},a.save=function(e){e?process.env.DEBUG=e:delete process.env.DEBUG},a.load=function(){return process.env.DEBUG},a.useColors=function(){return"colors"in a.inspectOpts?Boolean(a.inspectOpts.colors):i.isatty(process.stderr.fd)},a.destroy=o.deprecate((()=>{}),"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."),a.colors=[6,2,3,4,5,1];try{const e=n(4741);e&&(e.stderr||e).level>=2&&(a.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch(e){}a.inspectOpts=Object.keys(process.env).filter((e=>/^debug_/i.test(e))).reduce(((e,a)=>{const n=a.substring(6).toLowerCase().replace(/_([a-z])/g,((e,a)=>a.toUpperCase()));let i=process.env[a];return i=!!/^(yes|on|true|enabled)$/i.test(i)||!/^(no|off|false|disabled)$/i.test(i)&&("null"===i?null:Number(i)),e[n]=i,e}),{}),e.exports=n(4689)(a);const{formatters:s}=e.exports;s.o=function(e){return this.inspectOpts.colors=this.useColors,o.inspect(e,this.inspectOpts).split("\n").map((e=>e.trim())).join(" ")},s.O=function(e){return this.inspectOpts.colors=this.useColors,o.inspect(e,this.inspectOpts)}},3038:(e,a,n)=>{var i=n(2203).Stream,o=n(9023);function s(){this.source=null,this.dataSize=0,this.maxDataSize=1048576,this.pauseStream=!0,this._maxDataSizeExceeded=!1,this._released=!1,this._bufferedEvents=[]}e.exports=s,o.inherits(s,i),s.create=function(e,a){var n=new this;for(var i in a=a||{})n[i]=a[i];n.source=e;var o=e.emit;return e.emit=function(){return n._handleEmit(arguments),o.apply(e,arguments)},e.on("error",(function(){})),n.pauseStream&&e.pause(),n},Object.defineProperty(s.prototype,"readable",{configurable:!0,enumerable:!0,get:function(){return this.source.readable}}),s.prototype.setEncoding=function(){return this.source.setEncoding.apply(this.source,arguments)},s.prototype.resume=function(){this._released||this.release(),this.source.resume()},s.prototype.pause=function(){this.source.pause()},s.prototype.release=function(){this._released=!0,this._bufferedEvents.forEach(function(e){this.emit.apply(this,e)}.bind(this)),this._bufferedEvents=[]},s.prototype.pipe=function(){var e=i.prototype.pipe.apply(this,arguments);return this.resume(),e},s.prototype._handleEmit=function(e){this._released?this.emit.apply(this,e):("data"===e[0]&&(this.dataSize+=e[1].length,this._checkIfMaxDataSizeExceeded()),this._bufferedEvents.push(e))},s.prototype._checkIfMaxDataSizeExceeded=function(){if(!(this._maxDataSizeExceeded||this.dataSize<=this.maxDataSize)){this._maxDataSizeExceeded=!0;var e="DelayedStream#maxDataSize of "+this.maxDataSize+" bytes exceeded.";this.emit("error",new Error(e))}}},8778:(e,a,n)=>{var i;e.exports=function(){if(!i){try{i=n(6222)("follow-redirects")}catch(e){}"function"!=typeof i&&(i=function(){})}i.apply(null,arguments)}},8725:(e,a,n)=>{var i=n(7016),o=i.URL,s=n(8611),t=n(5692),r=n(2203).Writable,c=n(2613),p=n(8778),l=!1;try{c(new o)}catch(e){l="ERR_INVALID_URL"===e.code}var u=["auth","host","hostname","href","path","pathname","port","protocol","query","search","hash"],m=["abort","aborted","connect","error","socket","timeout"],d=Object.create(null);m.forEach((function(e){d[e]=function(a,n,i){this._redirectable.emit(e,a,n,i)}}));var f=O("ERR_INVALID_URL","Invalid URL",TypeError),x=O("ERR_FR_REDIRECTION_FAILURE","Redirected request failed"),h=O("ERR_FR_TOO_MANY_REDIRECTS","Maximum number of redirects exceeded",x),v=O("ERR_FR_MAX_BODY_LENGTH_EXCEEDED","Request body larger than maxBodyLength limit"),b=O("ERR_STREAM_WRITE_AFTER_END","write after end"),g=r.prototype.destroy||k;function y(e,a){r.call(this),this._sanitizeOptions(e),this._options=e,this._ended=!1,this._ending=!1,this._redirectCount=0,this._redirects=[],this._requestBodyLength=0,this._requestBodyBuffers=[],a&&this.on("response",a);var n=this;this._onNativeResponse=function(e){try{n._processResponse(e)}catch(e){n.emit("error",e instanceof x?e:new x({cause:e}))}},this._performRequest()}function w(e){var a={maxRedirects:21,maxBodyLength:10485760},n={};return Object.keys(e).forEach((function(i){var s=i+":",t=n[s]=e[i],r=a[i]=Object.create(t);Object.defineProperties(r,{request:{value:function(e,i,t){return o&&e instanceof o?e=_(e):T(e)?e=_(S(e)):(t=i,i=j(e),e={protocol:s}),R(i)&&(t=i,i=null),(i=Object.assign({maxRedirects:a.maxRedirects,maxBodyLength:a.maxBodyLength},e,i)).nativeProtocols=n,T(i.host)||T(i.hostname)||(i.hostname="::1"),c.equal(i.protocol,s,"protocol mismatch"),p("options",i),new y(i,t)},configurable:!0,enumerable:!0,writable:!0},get:{value:function(e,a,n){var i=r.request(e,a,n);return i.end(),i},configurable:!0,enumerable:!0,writable:!0}})})),a}function k(){}function S(e){var a;if(l)a=new o(e);else if(!T((a=j(i.parse(e))).protocol))throw new f({input:e});return a}function j(e){if(/^\[/.test(e.hostname)&&!/^\[[:0-9a-f]+\]$/i.test(e.hostname))throw new f({input:e.href||e});if(/^\[/.test(e.host)&&!/^\[[:0-9a-f]+\](:\d+)?$/i.test(e.host))throw new f({input:e.href||e});return e}function _(e,a){var n=a||{};for(var i of u)n[i]=e[i];return n.hostname.startsWith("[")&&(n.hostname=n.hostname.slice(1,-1)),""!==n.port&&(n.port=Number(n.port)),n.path=n.search?n.pathname+n.search:n.pathname,n}function E(e,a){var n;for(var i in a)e.test(i)&&(n=a[i],delete a[i]);return null==n?void 0:String(n).trim()}function O(e,a,n){function i(n){Error.captureStackTrace(this,this.constructor),Object.assign(this,n||{}),this.code=e,this.message=this.cause?a+": "+this.cause.message:a}return i.prototype=new(n||Error),Object.defineProperties(i.prototype,{constructor:{value:i,enumerable:!1},name:{value:"Error ["+e+"]",enumerable:!1}}),i}function C(e,a){for(var n of m)e.removeListener(n,d[n]);e.on("error",k),e.destroy(a)}function T(e){return"string"==typeof e||e instanceof String}function R(e){return"function"==typeof e}y.prototype=Object.create(r.prototype),y.prototype.abort=function(){C(this._currentRequest),this._currentRequest.abort(),this.emit("abort")},y.prototype.destroy=function(e){return C(this._currentRequest,e),g.call(this,e),this},y.prototype.write=function(e,a,n){if(this._ending)throw new b;if(!(T(e)||"object"==typeof(i=e)&&"length"in i))throw new TypeError("data should be a string, Buffer or Uint8Array");var i;R(a)&&(n=a,a=null),0!==e.length?this._requestBodyLength+e.length<=this._options.maxBodyLength?(this._requestBodyLength+=e.length,this._requestBodyBuffers.push({data:e,encoding:a}),this._currentRequest.write(e,a,n)):(this.emit("error",new v),this.abort()):n&&n()},y.prototype.end=function(e,a,n){if(R(e)?(n=e,e=a=null):R(a)&&(n=a,a=null),e){var i=this,o=this._currentRequest;this.write(e,a,(function(){i._ended=!0,o.end(null,null,n)})),this._ending=!0}else this._ended=this._ending=!0,this._currentRequest.end(null,null,n)},y.prototype.setHeader=function(e,a){this._options.headers[e]=a,this._currentRequest.setHeader(e,a)},y.prototype.removeHeader=function(e){delete this._options.headers[e],this._currentRequest.removeHeader(e)},y.prototype.setTimeout=function(e,a){var n=this;function i(a){a.setTimeout(e),a.removeListener("timeout",a.destroy),a.addListener("timeout",a.destroy)}function o(a){n._timeout&&clearTimeout(n._timeout),n._timeout=setTimeout((function(){n.emit("timeout"),s()}),e),i(a)}function s(){n._timeout&&(clearTimeout(n._timeout),n._timeout=null),n.removeListener("abort",s),n.removeListener("error",s),n.removeListener("response",s),n.removeListener("close",s),a&&n.removeListener("timeout",a),n.socket||n._currentRequest.removeListener("socket",o)}return a&&this.on("timeout",a),this.socket?o(this.socket):this._currentRequest.once("socket",o),this.on("socket",i),this.on("abort",s),this.on("error",s),this.on("response",s),this.on("close",s),this},["flushHeaders","getHeader","setNoDelay","setSocketKeepAlive"].forEach((function(e){y.prototype[e]=function(a,n){return this._currentRequest[e](a,n)}})),["aborted","connection","socket"].forEach((function(e){Object.defineProperty(y.prototype,e,{get:function(){return this._currentRequest[e]}})})),y.prototype._sanitizeOptions=function(e){if(e.headers||(e.headers={}),e.host&&(e.hostname||(e.hostname=e.host),delete e.host),!e.pathname&&e.path){var a=e.path.indexOf("?");a<0?e.pathname=e.path:(e.pathname=e.path.substring(0,a),e.search=e.path.substring(a))}},y.prototype._performRequest=function(){var e=this._options.protocol,a=this._options.nativeProtocols[e];if(!a)throw new TypeError("Unsupported protocol "+e);if(this._options.agents){var n=e.slice(0,-1);this._options.agent=this._options.agents[n]}var o=this._currentRequest=a.request(this._options,this._onNativeResponse);for(var s of(o._redirectable=this,m))o.on(s,d[s]);if(this._currentUrl=/^\//.test(this._options.path)?i.format(this._options):this._options.path,this._isRedirect){var t=0,r=this,c=this._requestBodyBuffers;!function e(a){if(o===r._currentRequest)if(a)r.emit("error",a);else if(t<c.length){var n=c[t++];o.finished||o.write(n.data,n.encoding,e)}else r._ended&&o.end()}()}},y.prototype._processResponse=function(e){var a=e.statusCode;this._options.trackRedirects&&this._redirects.push({url:this._currentUrl,headers:e.headers,statusCode:a});var n,s=e.headers.location;if(!s||!1===this._options.followRedirects||a<300||a>=400)return e.responseUrl=this._currentUrl,e.redirects=this._redirects,this.emit("response",e),void(this._requestBodyBuffers=[]);if(C(this._currentRequest),e.destroy(),++this._redirectCount>this._options.maxRedirects)throw new h;var t=this._options.beforeRedirect;t&&(n=Object.assign({Host:e.req.getHeader("host")},this._options.headers));var r=this._options.method;((301===a||302===a)&&"POST"===this._options.method||303===a&&!/^(?:GET|HEAD)$/.test(this._options.method))&&(this._options.method="GET",this._requestBodyBuffers=[],E(/^content-/i,this._options.headers));var u,m,d=E(/^host$/i,this._options.headers),f=S(this._currentUrl),x=d||f.host,v=/^\w+:/.test(s)?this._currentUrl:i.format(Object.assign(f,{host:x})),b=(u=s,m=v,l?new o(u,m):S(i.resolve(m,u)));if(p("redirecting to",b.href),this._isRedirect=!0,_(b,this._options),(b.protocol!==f.protocol&&"https:"!==b.protocol||b.host!==x&&!function(e,a){c(T(e)&&T(a));var n=e.length-a.length-1;return n>0&&"."===e[n]&&e.endsWith(a)}(b.host,x))&&E(/^(?:(?:proxy-)?authorization|cookie)$/i,this._options.headers),R(t)){var g={headers:e.headers,statusCode:a},y={url:v,method:r,headers:n};t(this._options,g,y),this._sanitizeOptions(this._options)}this._performRequest()},e.exports=w({http:s,https:t}),e.exports.wrap=w},4511:(e,a,n)=>{var i=n(2047),o=n(9023),s=n(6928),t=n(8611),r=n(5692),c=n(7016).parse,p=n(9896),l=n(2203).Stream,u=n(453),m=n(762),d=n(4184);function f(e){if(!(this instanceof f))return new f(e);for(var a in this._overheadLength=0,this._valueLength=0,this._valuesToMeasure=[],i.call(this),e=e||{})this[a]=e[a]}e.exports=f,o.inherits(f,i),f.LINE_BREAK="\r\n",f.DEFAULT_CONTENT_TYPE="application/octet-stream",f.prototype.append=function(e,a,n){"string"==typeof(n=n||{})&&(n={filename:n});var s=i.prototype.append.bind(this);if("number"==typeof a&&(a=""+a),o.isArray(a))this._error(new Error("Arrays are not supported."));else{var t=this._multiPartHeader(e,a,n),r=this._multiPartFooter();s(t),s(a),s(r),this._trackLength(t,a,n)}},f.prototype._trackLength=function(e,a,n){var i=0;null!=n.knownLength?i+=+n.knownLength:Buffer.isBuffer(a)?i=a.length:"string"==typeof a&&(i=Buffer.byteLength(a)),this._valueLength+=i,this._overheadLength+=Buffer.byteLength(e)+f.LINE_BREAK.length,a&&(a.path||a.readable&&a.hasOwnProperty("httpVersion")||a instanceof l)&&(n.knownLength||this._valuesToMeasure.push(a))},f.prototype._lengthRetriever=function(e,a){e.hasOwnProperty("fd")?null!=e.end&&e.end!=1/0&&null!=e.start?a(null,e.end+1-(e.start?e.start:0)):p.stat(e.path,(function(n,i){var o;n?a(n):(o=i.size-(e.start?e.start:0),a(null,o))})):e.hasOwnProperty("httpVersion")?a(null,+e.headers["content-length"]):e.hasOwnProperty("httpModule")?(e.on("response",(function(n){e.pause(),a(null,+n.headers["content-length"])})),e.resume()):a("Unknown stream")},f.prototype._multiPartHeader=function(e,a,n){if("string"==typeof n.header)return n.header;var i,o=this._getContentDisposition(a,n),s=this._getContentType(a,n),t="",r={"Content-Disposition":["form-data",'name="'+e+'"'].concat(o||[]),"Content-Type":[].concat(s||[])};for(var c in"object"==typeof n.header&&d(r,n.header),r)r.hasOwnProperty(c)&&null!=(i=r[c])&&(Array.isArray(i)||(i=[i]),i.length&&(t+=c+": "+i.join("; ")+f.LINE_BREAK));return"--"+this.getBoundary()+f.LINE_BREAK+t+f.LINE_BREAK},f.prototype._getContentDisposition=function(e,a){var n,i;return"string"==typeof a.filepath?n=s.normalize(a.filepath).replace(/\\/g,"/"):a.filename||e.name||e.path?n=s.basename(a.filename||e.name||e.path):e.readable&&e.hasOwnProperty("httpVersion")&&(n=s.basename(e.client._httpMessage.path||"")),n&&(i='filename="'+n+'"'),i},f.prototype._getContentType=function(e,a){var n=a.contentType;return!n&&e.name&&(n=u.lookup(e.name)),!n&&e.path&&(n=u.lookup(e.path)),!n&&e.readable&&e.hasOwnProperty("httpVersion")&&(n=e.headers["content-type"]),n||!a.filepath&&!a.filename||(n=u.lookup(a.filepath||a.filename)),n||"object"!=typeof e||(n=f.DEFAULT_CONTENT_TYPE),n},f.prototype._multiPartFooter=function(){return function(e){var a=f.LINE_BREAK;0===this._streams.length&&(a+=this._lastBoundary()),e(a)}.bind(this)},f.prototype._lastBoundary=function(){return"--"+this.getBoundary()+"--"+f.LINE_BREAK},f.prototype.getHeaders=function(e){var a,n={"content-type":"multipart/form-data; boundary="+this.getBoundary()};for(a in e)e.hasOwnProperty(a)&&(n[a.toLowerCase()]=e[a]);return n},f.prototype.setBoundary=function(e){this._boundary=e},f.prototype.getBoundary=function(){return this._boundary||this._generateBoundary(),this._boundary},f.prototype.getBuffer=function(){for(var e=new Buffer.alloc(0),a=this.getBoundary(),n=0,i=this._streams.length;n<i;n++)"function"!=typeof this._streams[n]&&(e=Buffer.isBuffer(this._streams[n])?Buffer.concat([e,this._streams[n]]):Buffer.concat([e,Buffer.from(this._streams[n])]),"string"==typeof this._streams[n]&&this._streams[n].substring(2,a.length+2)===a||(e=Buffer.concat([e,Buffer.from(f.LINE_BREAK)])));return Buffer.concat([e,Buffer.from(this._lastBoundary())])},f.prototype._generateBoundary=function(){for(var e="--------------------------",a=0;a<24;a++)e+=Math.floor(10*Math.random()).toString(16);this._boundary=e},f.prototype.getLengthSync=function(){var e=this._overheadLength+this._valueLength;return this._streams.length&&(e+=this._lastBoundary().length),this.hasKnownLength()||this._error(new Error("Cannot calculate proper length in synchronous way.")),e},f.prototype.hasKnownLength=function(){var e=!0;return this._valuesToMeasure.length&&(e=!1),e},f.prototype.getLength=function(e){var a=this._overheadLength+this._valueLength;this._streams.length&&(a+=this._lastBoundary().length),this._valuesToMeasure.length?m.parallel(this._valuesToMeasure,this._lengthRetriever,(function(n,i){n?e(n):(i.forEach((function(e){a+=e})),e(null,a))})):process.nextTick(e.bind(this,null,a))},f.prototype.submit=function(e,a){var n,i,o={method:"post"};return"string"==typeof e?(e=c(e),i=d({port:e.port,path:e.pathname,host:e.hostname,protocol:e.protocol},o)):(i=d(e,o)).port||(i.port="https:"==i.protocol?443:80),i.headers=this.getHeaders(e.headers),n="https:"==i.protocol?r.request(i):t.request(i),this.getLength(function(e,i){if(e&&"Unknown stream"!==e)this._error(e);else if(i&&n.setHeader("Content-Length",i),this.pipe(n),a){var o,s=function(e,i){return n.removeListener("error",s),n.removeListener("response",o),a.call(this,e,i)};o=s.bind(this,null),n.on("error",s),n.on("response",o)}}.bind(this)),n},f.prototype._error=function(e){this.error||(this.error=e,this.pause(),this.emit("error",e))},f.prototype.toString=function(){return"[object FormData]"}},4184:e=>{e.exports=function(e,a){return Object.keys(a).forEach((function(n){e[n]=e[n]||a[n]})),e}},1217:(e,a,n)=>{"use strict";const i=n(9851),o=n(6928),s=n(2551).mkdirsSync,t=n(5180).utimesMillisSync,r=n(6525);function c(e,a,n,s){const t=(s.dereference?i.statSync:i.lstatSync)(a);if(t.isDirectory())return function(e,a,n,o,s){return a?u(n,o,s):function(e,a,n,o){return i.mkdirSync(n),u(a,n,o),l(n,e)}(e.mode,n,o,s)}(t,e,a,n,s);if(t.isFile()||t.isCharacterDevice()||t.isBlockDevice())return function(e,a,n,o,s){return a?function(e,a,n,o){if(o.overwrite)return i.unlinkSync(n),p(e,a,n,o);if(o.errorOnExist)throw new Error(`'${n}' already exists`)}(e,n,o,s):p(e,n,o,s)}(t,e,a,n,s);if(t.isSymbolicLink())return function(e,a,n,s){let t=i.readlinkSync(a);if(s.dereference&&(t=o.resolve(process.cwd(),t)),e){let e;try{e=i.readlinkSync(n)}catch(e){if("EINVAL"===e.code||"UNKNOWN"===e.code)return i.symlinkSync(t,n);throw e}if(s.dereference&&(e=o.resolve(process.cwd(),e)),r.isSrcSubdir(t,e))throw new Error(`Cannot copy '${t}' to a subdirectory of itself, '${e}'.`);if(r.isSrcSubdir(e,t))throw new Error(`Cannot overwrite '${e}' with '${t}'.`);return function(e,a){return i.unlinkSync(a),i.symlinkSync(e,a)}(t,n)}return i.symlinkSync(t,n)}(e,a,n,s);if(t.isSocket())throw new Error(`Cannot copy a socket file: ${a}`);if(t.isFIFO())throw new Error(`Cannot copy a FIFO pipe: ${a}`);throw new Error(`Unknown file: ${a}`)}function p(e,a,n,o){return i.copyFileSync(a,n),o.preserveTimestamps&&function(e,a,n){(function(e){return!(128&e)})(e)&&function(e,a){l(e,128|a)}(n,e),function(e,a){const n=i.statSync(e);t(a,n.atime,n.mtime)}(a,n)}(e.mode,a,n),l(n,e.mode)}function l(e,a){return i.chmodSync(e,a)}function u(e,a,n){i.readdirSync(e).forEach((i=>function(e,a,n,i){const s=o.join(a,e),t=o.join(n,e);if(i.filter&&!i.filter(s,t))return;const{destStat:p}=r.checkPathsSync(s,t,"copy",i);return c(p,s,t,i)}(i,e,a,n)))}e.exports=function(e,a,n){"function"==typeof n&&(n={filter:n}),(n=n||{}).clobber=!("clobber"in n)||!!n.clobber,n.overwrite="overwrite"in n?!!n.overwrite:n.clobber,n.preserveTimestamps&&"ia32"===process.arch&&process.emitWarning("Using the preserveTimestamps option in 32-bit node is not recommended;\n\n\tsee https://github.com/jprichardson/node-fs-extra/issues/269","Warning","fs-extra-WARN0002");const{srcStat:t,destStat:p}=r.checkPathsSync(e,a,"copy",n);if(r.checkParentPathsSync(e,t,a,"copy"),n.filter&&!n.filter(e,a))return;const l=o.dirname(a);return i.existsSync(l)||s(l),c(p,e,a,n)}},1457:(e,a,n)=>{"use strict";const i=n(1892),o=n(6928),{mkdirs:s}=n(2551),{pathExists:t}=n(5239),{utimesMillis:r}=n(5180),c=n(6525);async function p(e,a,n){return!n.filter||n.filter(e,a)}async function l(e,a,n,s){const t=s.dereference?i.stat:i.lstat,r=await t(a);if(r.isDirectory())return async function(e,a,n,s,t){a||await i.mkdir(s);const r=await i.readdir(n);await Promise.all(r.map((async e=>{const a=o.join(n,e),i=o.join(s,e);if(!await p(a,i,t))return;const{destStat:r}=await c.checkPaths(a,i,"copy",t);return l(r,a,i,t)}))),a||await i.chmod(s,e.mode)}(r,e,a,n,s);if(r.isFile()||r.isCharacterDevice()||r.isBlockDevice())return async function(e,a,n,o,s){if(!a)return u(e,n,o,s);if(s.overwrite)return await i.unlink(o),u(e,n,o,s);if(s.errorOnExist)throw new Error(`'${o}' already exists`)}(r,e,a,n,s);if(r.isSymbolicLink())return async function(e,a,n,s){let t=await i.readlink(a);if(s.dereference&&(t=o.resolve(process.cwd(),t)),!e)return i.symlink(t,n);let r=null;try{r=await i.readlink(n)}catch(e){if("EINVAL"===e.code||"UNKNOWN"===e.code)return i.symlink(t,n);throw e}if(s.dereference&&(r=o.resolve(process.cwd(),r)),c.isSrcSubdir(t,r))throw new Error(`Cannot copy '${t}' to a subdirectory of itself, '${r}'.`);if(c.isSrcSubdir(r,t))throw new Error(`Cannot overwrite '${r}' with '${t}'.`);return await i.unlink(n),i.symlink(t,n)}(e,a,n,s);if(r.isSocket())throw new Error(`Cannot copy a socket file: ${a}`);if(r.isFIFO())throw new Error(`Cannot copy a FIFO pipe: ${a}`);throw new Error(`Unknown file: ${a}`)}async function u(e,a,n,o){if(await i.copyFile(a,n),o.preserveTimestamps){128&e.mode||await function(e,a){return i.chmod(e,128|a)}(n,e.mode);const o=await i.stat(a);await r(n,o.atime,o.mtime)}return i.chmod(n,e.mode)}e.exports=async function(e,a,n={}){"function"==typeof n&&(n={filter:n}),n.clobber=!("clobber"in n)||!!n.clobber,n.overwrite="overwrite"in n?!!n.overwrite:n.clobber,n.preserveTimestamps&&"ia32"===process.arch&&process.emitWarning("Using the preserveTimestamps option in 32-bit node is not recommended;\n\n\tsee https://github.com/jprichardson/node-fs-extra/issues/269","Warning","fs-extra-WARN0001");const{srcStat:i,destStat:r}=await c.checkPaths(e,a,"copy",n);if(await c.checkParentPaths(e,i,a,"copy"),!await p(e,a,n))return;const u=o.dirname(a);await t(u)||await s(u),await l(r,e,a,n)}},1494:(e,a,n)=>{"use strict";const i=n(1171).fromPromise;e.exports={copy:i(n(1457)),copySync:n(1217)}},9328:(e,a,n)=>{"use strict";const i=n(1171).fromPromise,o=n(1892),s=n(6928),t=n(2551),r=n(7411),c=i((async function(e){let a;try{a=await o.readdir(e)}catch{return t.mkdirs(e)}return Promise.all(a.map((a=>r.remove(s.join(e,a)))))}));function p(e){let a;try{a=o.readdirSync(e)}catch{return t.mkdirsSync(e)}a.forEach((a=>{a=s.join(e,a),r.removeSync(a)}))}e.exports={emptyDirSync:p,emptydirSync:p,emptyDir:c,emptydir:c}},7163:(e,a,n)=>{"use strict";const i=n(1171).fromPromise,o=n(6928),s=n(1892),t=n(2551);e.exports={createFile:i((async function(e){let a;try{a=await s.stat(e)}catch{}if(a&&a.isFile())return;const n=o.dirname(e);let i=null;try{i=await s.stat(n)}catch(a){if("ENOENT"===a.code)return await t.mkdirs(n),void await s.writeFile(e,"");throw a}i.isDirectory()?await s.writeFile(e,""):await s.readdir(n)})),createFileSync:function(e){let a;try{a=s.statSync(e)}catch{}if(a&&a.isFile())return;const n=o.dirname(e);try{s.statSync(n).isDirectory()||s.readdirSync(n)}catch(e){if(!e||"ENOENT"!==e.code)throw e;t.mkdirsSync(n)}s.writeFileSync(e,"")}}},3689:(e,a,n)=>{"use strict";const{createFile:i,createFileSync:o}=n(7163),{createLink:s,createLinkSync:t}=n(9237),{createSymlink:r,createSymlinkSync:c}=n(9894);e.exports={createFile:i,createFileSync:o,ensureFile:i,ensureFileSync:o,createLink:s,createLinkSync:t,ensureLink:s,ensureLinkSync:t,createSymlink:r,createSymlinkSync:c,ensureSymlink:r,ensureSymlinkSync:c}},9237:(e,a,n)=>{"use strict";const i=n(1171).fromPromise,o=n(6928),s=n(1892),t=n(2551),{pathExists:r}=n(5239),{areIdentical:c}=n(6525);e.exports={createLink:i((async function(e,a){let n,i;try{n=await s.lstat(a)}catch{}try{i=await s.lstat(e)}catch(e){throw e.message=e.message.replace("lstat","ensureLink"),e}if(n&&c(i,n))return;const p=o.dirname(a);await r(p)||await t.mkdirs(p),await s.link(e,a)})),createLinkSync:function(e,a){let n;try{n=s.lstatSync(a)}catch{}try{const a=s.lstatSync(e);if(n&&c(a,n))return}catch(e){throw e.message=e.message.replace("lstat","ensureLink"),e}const i=o.dirname(a);return s.existsSync(i)||t.mkdirsSync(i),s.linkSync(e,a)}}},987:(e,a,n)=>{"use strict";const i=n(6928),o=n(1892),{pathExists:s}=n(5239),t=n(1171).fromPromise;e.exports={symlinkPaths:t((async function(e,a){if(i.isAbsolute(e)){try{await o.lstat(e)}catch(e){throw e.message=e.message.replace("lstat","ensureSymlink"),e}return{toCwd:e,toDst:e}}const n=i.dirname(a),t=i.join(n,e);if(await s(t))return{toCwd:t,toDst:e};try{await o.lstat(e)}catch(e){throw e.message=e.message.replace("lstat","ensureSymlink"),e}return{toCwd:e,toDst:i.relative(n,e)}})),symlinkPathsSync:function(e,a){if(i.isAbsolute(e)){if(!o.existsSync(e))throw new Error("absolute srcpath does not exist");return{toCwd:e,toDst:e}}const n=i.dirname(a),s=i.join(n,e);if(o.existsSync(s))return{toCwd:s,toDst:e};if(!o.existsSync(e))throw new Error("relative srcpath does not exist");return{toCwd:e,toDst:i.relative(n,e)}}}},127:(e,a,n)=>{"use strict";const i=n(1892),o=n(1171).fromPromise;e.exports={symlinkType:o((async function(e,a){if(a)return a;let n;try{n=await i.lstat(e)}catch{return"file"}return n&&n.isDirectory()?"dir":"file"})),symlinkTypeSync:function(e,a){if(a)return a;let n;try{n=i.lstatSync(e)}catch{return"file"}return n&&n.isDirectory()?"dir":"file"}}},9894:(e,a,n)=>{"use strict";const i=n(1171).fromPromise,o=n(6928),s=n(1892),{mkdirs:t,mkdirsSync:r}=n(2551),{symlinkPaths:c,symlinkPathsSync:p}=n(987),{symlinkType:l,symlinkTypeSync:u}=n(127),{pathExists:m}=n(5239),{areIdentical:d}=n(6525);e.exports={createSymlink:i((async function(e,a,n){let i;try{i=await s.lstat(a)}catch{}if(i&&i.isSymbolicLink()){const[n,i]=await Promise.all([s.stat(e),s.stat(a)]);if(d(n,i))return}const r=await c(e,a);e=r.toDst;const p=await l(r.toCwd,n),u=o.dirname(a);return await m(u)||await t(u),s.symlink(e,a,p)})),createSymlinkSync:function(e,a,n){let i;try{i=s.lstatSync(a)}catch{}if(i&&i.isSymbolicLink()){const n=s.statSync(e),i=s.statSync(a);if(d(n,i))return}const t=p(e,a);e=t.toDst,n=u(t.toCwd,n);const c=o.dirname(a);return s.existsSync(c)||r(c),s.symlinkSync(e,a,n)}}},1892:(e,a,n)=>{"use strict";const i=n(1171).fromCallback,o=n(9851),s=["access","appendFile","chmod","chown","close","copyFile","fchmod","fchown","fdatasync","fstat","fsync","ftruncate","futimes","lchmod","lchown","link","lstat","mkdir","mkdtemp","open","opendir","readdir","readFile","readlink","realpath","rename","rm","rmdir","stat","symlink","truncate","unlink","utimes","writeFile"].filter((e=>"function"==typeof o[e]));Object.assign(a,o),s.forEach((e=>{a[e]=i(o[e])})),a.exists=function(e,a){return"function"==typeof a?o.exists(e,a):new Promise((a=>o.exists(e,a)))},a.read=function(e,a,n,i,s,t){return"function"==typeof t?o.read(e,a,n,i,s,t):new Promise(((t,r)=>{o.read(e,a,n,i,s,((e,a,n)=>{if(e)return r(e);t({bytesRead:a,buffer:n})}))}))},a.write=function(e,a,...n){return"function"==typeof n[n.length-1]?o.write(e,a,...n):new Promise(((i,s)=>{o.write(e,a,...n,((e,a,n)=>{if(e)return s(e);i({bytesWritten:a,buffer:n})}))}))},a.readv=function(e,a,...n){return"function"==typeof n[n.length-1]?o.readv(e,a,...n):new Promise(((i,s)=>{o.readv(e,a,...n,((e,a,n)=>{if(e)return s(e);i({bytesRead:a,buffers:n})}))}))},a.writev=function(e,a,...n){return"function"==typeof n[n.length-1]?o.writev(e,a,...n):new Promise(((i,s)=>{o.writev(e,a,...n,((e,a,n)=>{if(e)return s(e);i({bytesWritten:a,buffers:n})}))}))},"function"==typeof o.realpath.native?a.realpath.native=i(o.realpath.native):process.emitWarning("fs.realpath.native is not a function. Is fs being monkey-patched?","Warning","fs-extra-WARN0003")},6474:(e,a,n)=>{"use strict";e.exports={...n(1892),...n(1494),...n(9328),...n(3689),...n(9873),...n(2551),...n(8030),...n(7787),...n(5239),...n(7411)}},9873:(e,a,n)=>{"use strict";const i=n(1171).fromPromise,o=n(6917);o.outputJson=i(n(9843)),o.outputJsonSync=n(579),o.outputJSON=o.outputJson,o.outputJSONSync=o.outputJsonSync,o.writeJSON=o.writeJson,o.writeJSONSync=o.writeJsonSync,o.readJSON=o.readJson,o.readJSONSync=o.readJsonSync,e.exports=o},6917:(e,a,n)=>{"use strict";const i=n(6259);e.exports={readJson:i.readFile,readJsonSync:i.readFileSync,writeJson:i.writeFile,writeJsonSync:i.writeFileSync}},579:(e,a,n)=>{"use strict";const{stringify:i}=n(5646),{outputFileSync:o}=n(7787);e.exports=function(e,a,n){const s=i(a,n);o(e,s,n)}},9843:(e,a,n)=>{"use strict";const{stringify:i}=n(5646),{outputFile:o}=n(7787);e.exports=async function(e,a,n={}){const s=i(a,n);await o(e,s,n)}},2551:(e,a,n)=>{"use strict";const i=n(1171).fromPromise,{makeDir:o,makeDirSync:s}=n(8415),t=i(o);e.exports={mkdirs:t,mkdirsSync:s,mkdirp:t,mkdirpSync:s,ensureDir:t,ensureDirSync:s}},8415:(e,a,n)=>{"use strict";const i=n(1892),{checkPath:o}=n(3874),s=e=>"number"==typeof e?e:{mode:511,...e}.mode;e.exports.makeDir=async(e,a)=>(o(e),i.mkdir(e,{mode:s(a),recursive:!0})),e.exports.makeDirSync=(e,a)=>(o(e),i.mkdirSync(e,{mode:s(a),recursive:!0}))},3874:(e,a,n)=>{"use strict";const i=n(6928);e.exports.checkPath=function(e){if("win32"===process.platform&&/[<>:"|?*]/.test(e.replace(i.parse(e).root,""))){const a=new Error(`Path contains invalid characters: ${e}`);throw a.code="EINVAL",a}}},8030:(e,a,n)=>{"use strict";const i=n(1171).fromPromise;e.exports={move:i(n(2153)),moveSync:n(3785)}},3785:(e,a,n)=>{"use strict";const i=n(9851),o=n(6928),s=n(1494).copySync,t=n(7411).removeSync,r=n(2551).mkdirpSync,c=n(6525);function p(e,a,n){try{i.renameSync(e,a)}catch(i){if("EXDEV"!==i.code)throw i;return function(e,a,n){return s(e,a,{overwrite:n,errorOnExist:!0,preserveTimestamps:!0}),t(e)}(e,a,n)}}e.exports=function(e,a,n){const s=(n=n||{}).overwrite||n.clobber||!1,{srcStat:l,isChangingCase:u=!1}=c.checkPathsSync(e,a,"move",n);return c.checkParentPathsSync(e,l,a,"move"),function(e){const a=o.dirname(e);return o.parse(a).root===a}(a)||r(o.dirname(a)),function(e,a,n,o){if(o)return p(e,a,n);if(n)return t(a),p(e,a,n);if(i.existsSync(a))throw new Error("dest already exists.");return p(e,a,n)}(e,a,s,u)}},2153:(e,a,n)=>{"use strict";const i=n(1892),o=n(6928),{copy:s}=n(1494),{remove:t}=n(7411),{mkdirp:r}=n(2551),{pathExists:c}=n(5239),p=n(6525);e.exports=async function(e,a,n={}){const l=n.overwrite||n.clobber||!1,{srcStat:u,isChangingCase:m=!1}=await p.checkPaths(e,a,"move",n);await p.checkParentPaths(e,u,a,"move");const d=o.dirname(a);return o.parse(d).root!==d&&await r(d),async function(e,a,n,o){if(!o)if(n)await t(a);else if(await c(a))throw new Error("dest already exists.");try{await i.rename(e,a)}catch(i){if("EXDEV"!==i.code)throw i;await async function(e,a,n){const i={overwrite:n,errorOnExist:!0,preserveTimestamps:!0};return await s(e,a,i),t(e)}(e,a,n)}}(e,a,l,m)}},7787:(e,a,n)=>{"use strict";const i=n(1171).fromPromise,o=n(1892),s=n(6928),t=n(2551),r=n(5239).pathExists;e.exports={outputFile:i((async function(e,a,n="utf-8"){const i=s.dirname(e);return await r(i)||await t.mkdirs(i),o.writeFile(e,a,n)})),outputFileSync:function(e,...a){const n=s.dirname(e);o.existsSync(n)||t.mkdirsSync(n),o.writeFileSync(e,...a)}}},5239:(e,a,n)=>{"use strict";const i=n(1171).fromPromise,o=n(1892);e.exports={pathExists:i((function(e){return o.access(e).then((()=>!0)).catch((()=>!1))})),pathExistsSync:o.existsSync}},7411:(e,a,n)=>{"use strict";const i=n(9851),o=n(1171).fromCallback;e.exports={remove:o((function(e,a){i.rm(e,{recursive:!0,force:!0},a)})),removeSync:function(e){i.rmSync(e,{recursive:!0,force:!0})}}},6525:(e,a,n)=>{"use strict";const i=n(1892),o=n(6928),s=n(1171).fromPromise;function t(e,a){return a.ino&&a.dev&&a.ino===e.ino&&a.dev===e.dev}function r(e,a){const n=o.resolve(e).split(o.sep).filter((e=>e)),i=o.resolve(a).split(o.sep).filter((e=>e));return n.every(((e,a)=>i[a]===e))}function c(e,a,n){return`Cannot ${n} '${e}' to a subdirectory of itself, '${a}'.`}e.exports={checkPaths:s((async function(e,a,n,s){const{srcStat:p,destStat:l}=await function(e,a,n){const o=n.dereference?e=>i.stat(e,{bigint:!0}):e=>i.lstat(e,{bigint:!0});return Promise.all([o(e),o(a).catch((e=>{if("ENOENT"===e.code)return null;throw e}))]).then((([e,a])=>({srcStat:e,destStat:a})))}(e,a,s);if(l){if(t(p,l)){const i=o.basename(e),s=o.basename(a);if("move"===n&&i!==s&&i.toLowerCase()===s.toLowerCase())return{srcStat:p,destStat:l,isChangingCase:!0};throw new Error("Source and destination must not be the same.")}if(p.isDirectory()&&!l.isDirectory())throw new Error(`Cannot overwrite non-directory '${a}' with directory '${e}'.`);if(!p.isDirectory()&&l.isDirectory())throw new Error(`Cannot overwrite directory '${a}' with non-directory '${e}'.`)}if(p.isDirectory()&&r(e,a))throw new Error(c(e,a,n));return{srcStat:p,destStat:l}})),checkPathsSync:function(e,a,n,s){const{srcStat:p,destStat:l}=function(e,a,n){let o;const s=n.dereference?e=>i.statSync(e,{bigint:!0}):e=>i.lstatSync(e,{bigint:!0}),t=s(e);try{o=s(a)}catch(e){if("ENOENT"===e.code)return{srcStat:t,destStat:null};throw e}return{srcStat:t,destStat:o}}(e,a,s);if(l){if(t(p,l)){const i=o.basename(e),s=o.basename(a);if("move"===n&&i!==s&&i.toLowerCase()===s.toLowerCase())return{srcStat:p,destStat:l,isChangingCase:!0};throw new Error("Source and destination must not be the same.")}if(p.isDirectory()&&!l.isDirectory())throw new Error(`Cannot overwrite non-directory '${a}' with directory '${e}'.`);if(!p.isDirectory()&&l.isDirectory())throw new Error(`Cannot overwrite directory '${a}' with non-directory '${e}'.`)}if(p.isDirectory()&&r(e,a))throw new Error(c(e,a,n));return{srcStat:p,destStat:l}},checkParentPaths:s((async function e(a,n,s,r){const p=o.resolve(o.dirname(a)),l=o.resolve(o.dirname(s));if(l===p||l===o.parse(l).root)return;let u;try{u=await i.stat(l,{bigint:!0})}catch(e){if("ENOENT"===e.code)return;throw e}if(t(n,u))throw new Error(c(a,s,r));return e(a,n,l,r)})),checkParentPathsSync:function e(a,n,s,r){const p=o.resolve(o.dirname(a)),l=o.resolve(o.dirname(s));if(l===p||l===o.parse(l).root)return;let u;try{u=i.statSync(l,{bigint:!0})}catch(e){if("ENOENT"===e.code)return;throw e}if(t(n,u))throw new Error(c(a,s,r));return e(a,n,l,r)},isSrcSubdir:r,areIdentical:t}},5180:(e,a,n)=>{"use strict";const i=n(1892),o=n(1171).fromPromise;e.exports={utimesMillis:o((async function(e,a,n){const o=await i.open(e,"r+");let s=null;try{await i.futimes(o,a,n)}finally{try{await i.close(o)}catch(e){s=e}}if(s)throw s})),utimesMillisSync:function(e,a,n){const o=i.openSync(e,"r+");return i.futimesSync(o,a,n),i.closeSync(o)}}},287:e=>{"use strict";e.exports=function(e){if(null===e||"object"!=typeof e)return e;if(e instanceof Object)var n={__proto__:a(e)};else n=Object.create(null);return Object.getOwnPropertyNames(e).forEach((function(a){Object.defineProperty(n,a,Object.getOwnPropertyDescriptor(e,a))})),n};var a=Object.getPrototypeOf||function(e){return e.__proto__}},9851:(e,a,n)=>{var i,o,s=n(9896),t=n(2070),r=n(6071),c=n(287),p=n(9023);function l(e,a){Object.defineProperty(e,i,{get:function(){return a}})}"function"==typeof Symbol&&"function"==typeof Symbol.for?(i=Symbol.for("graceful-fs.queue"),o=Symbol.for("graceful-fs.previous")):(i="___graceful-fs.queue",o="___graceful-fs.previous");var u,m=function(){};if(p.debuglog?m=p.debuglog("gfs4"):/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&(m=function(){var e=p.format.apply(p,arguments);e="GFS4: "+e.split(/\n/).join("\nGFS4: "),console.error(e)}),!s[i]){var d=global[i]||[];l(s,d),s.close=function(e){function a(a,n){return e.call(s,a,(function(e){e||h(),"function"==typeof n&&n.apply(this,arguments)}))}return Object.defineProperty(a,o,{value:e}),a}(s.close),s.closeSync=function(e){function a(a){e.apply(s,arguments),h()}return Object.defineProperty(a,o,{value:e}),a}(s.closeSync),/\bgfs4\b/i.test(process.env.NODE_DEBUG||"")&&process.on("exit",(function(){m(s[i]),n(2613).equal(s[i].length,0)}))}function f(e){t(e),e.gracefulify=f,e.createReadStream=function(a,n){return new e.ReadStream(a,n)},e.createWriteStream=function(a,n){return new e.WriteStream(a,n)};var a=e.readFile;e.readFile=function(e,n,i){return"function"==typeof n&&(i=n,n=null),function e(n,i,o,s){return a(n,i,(function(a){!a||"EMFILE"!==a.code&&"ENFILE"!==a.code?"function"==typeof o&&o.apply(this,arguments):x([e,[n,i,o],a,s||Date.now(),Date.now()])}))}(e,n,i)};var n=e.writeFile;e.writeFile=function(e,a,i,o){return"function"==typeof i&&(o=i,i=null),function e(a,i,o,s,t){return n(a,i,o,(function(n){!n||"EMFILE"!==n.code&&"ENFILE"!==n.code?"function"==typeof s&&s.apply(this,arguments):x([e,[a,i,o,s],n,t||Date.now(),Date.now()])}))}(e,a,i,o)};var i=e.appendFile;i&&(e.appendFile=function(e,a,n,o){return"function"==typeof n&&(o=n,n=null),function e(a,n,o,s,t){return i(a,n,o,(function(i){!i||"EMFILE"!==i.code&&"ENFILE"!==i.code?"function"==typeof s&&s.apply(this,arguments):x([e,[a,n,o,s],i,t||Date.now(),Date.now()])}))}(e,a,n,o)});var o=e.copyFile;o&&(e.copyFile=function(e,a,n,i){return"function"==typeof n&&(i=n,n=0),function e(a,n,i,s,t){return o(a,n,i,(function(o){!o||"EMFILE"!==o.code&&"ENFILE"!==o.code?"function"==typeof s&&s.apply(this,arguments):x([e,[a,n,i,s],o,t||Date.now(),Date.now()])}))}(e,a,n,i)});var s=e.readdir;e.readdir=function(e,a,n){"function"==typeof a&&(n=a,a=null);var i=c.test(process.version)?function(e,a,n,i){return s(e,o(e,a,n,i))}:function(e,a,n,i){return s(e,a,o(e,a,n,i))};return i(e,a,n);function o(e,a,n,o){return function(s,t){!s||"EMFILE"!==s.code&&"ENFILE"!==s.code?(t&&t.sort&&t.sort(),"function"==typeof n&&n.call(this,s,t)):x([i,[e,a,n],s,o||Date.now(),Date.now()])}}};var c=/^v[0-5]\./;if("v0.8"===process.version.substr(0,4)){var p=r(e);h=p.ReadStream,v=p.WriteStream}var l=e.ReadStream;l&&(h.prototype=Object.create(l.prototype),h.prototype.open=function(){var e=this;g(e.path,e.flags,e.mode,(function(a,n){a?(e.autoClose&&e.destroy(),e.emit("error",a)):(e.fd=n,e.emit("open",n),e.read())}))});var u=e.WriteStream;u&&(v.prototype=Object.create(u.prototype),v.prototype.open=function(){var e=this;g(e.path,e.flags,e.mode,(function(a,n){a?(e.destroy(),e.emit("error",a)):(e.fd=n,e.emit("open",n))}))}),Object.defineProperty(e,"ReadStream",{get:function(){return h},set:function(e){h=e},enumerable:!0,configurable:!0}),Object.defineProperty(e,"WriteStream",{get:function(){return v},set:function(e){v=e},enumerable:!0,configurable:!0});var m=h;Object.defineProperty(e,"FileReadStream",{get:function(){return m},set:function(e){m=e},enumerable:!0,configurable:!0});var d=v;function h(e,a){return this instanceof h?(l.apply(this,arguments),this):h.apply(Object.create(h.prototype),arguments)}function v(e,a){return this instanceof v?(u.apply(this,arguments),this):v.apply(Object.create(v.prototype),arguments)}Object.defineProperty(e,"FileWriteStream",{get:function(){return d},set:function(e){d=e},enumerable:!0,configurable:!0});var b=e.open;function g(e,a,n,i){return"function"==typeof n&&(i=n,n=null),function e(a,n,i,o,s){return b(a,n,i,(function(t,r){!t||"EMFILE"!==t.code&&"ENFILE"!==t.code?"function"==typeof o&&o.apply(this,arguments):x([e,[a,n,i,o],t,s||Date.now(),Date.now()])}))}(e,a,n,i)}return e.open=g,e}function x(e){m("ENQUEUE",e[0].name,e[1]),s[i].push(e),v()}function h(){for(var e=Date.now(),a=0;a<s[i].length;++a)s[i][a].length>2&&(s[i][a][3]=e,s[i][a][4]=e);v()}function v(){if(clearTimeout(u),u=void 0,0!==s[i].length){var e=s[i].shift(),a=e[0],n=e[1],o=e[2],t=e[3],r=e[4];if(void 0===t)m("RETRY",a.name,n),a.apply(null,n);else if(Date.now()-t>=6e4){m("TIMEOUT",a.name,n);var c=n.pop();"function"==typeof c&&c.call(null,o)}else{var p=Date.now()-r,l=Math.max(r-t,1);p>=Math.min(1.2*l,100)?(m("RETRY",a.name,n),a.apply(null,n.concat([t]))):s[i].push(e)}void 0===u&&(u=setTimeout(v,0))}}global[i]||l(global,s[i]),e.exports=f(c(s)),process.env.TEST_GRACEFUL_FS_GLOBAL_PATCH&&!s.__patched&&(e.exports=f(s),s.__patched=!0)},6071:(e,a,n)=>{var i=n(2203).Stream;e.exports=function(e){return{ReadStream:function a(n,o){if(!(this instanceof a))return new a(n,o);i.call(this);var s=this;this.path=n,this.fd=null,this.readable=!0,this.paused=!1,this.flags="r",this.mode=438,this.bufferSize=65536,o=o||{};for(var t=Object.keys(o),r=0,c=t.length;r<c;r++){var p=t[r];this[p]=o[p]}if(this.encoding&&this.setEncoding(this.encoding),void 0!==this.start){if("number"!=typeof this.start)throw TypeError("start must be a Number");if(void 0===this.end)this.end=1/0;else if("number"!=typeof this.end)throw TypeError("end must be a Number");if(this.start>this.end)throw new Error("start must be <= end");this.pos=this.start}null===this.fd?e.open(this.path,this.flags,this.mode,(function(e,a){if(e)return s.emit("error",e),void(s.readable=!1);s.fd=a,s.emit("open",a),s._read()})):process.nextTick((function(){s._read()}))},WriteStream:function a(n,o){if(!(this instanceof a))return new a(n,o);i.call(this),this.path=n,this.fd=null,this.writable=!0,this.flags="w",this.encoding="binary",this.mode=438,this.bytesWritten=0,o=o||{};for(var s=Object.keys(o),t=0,r=s.length;t<r;t++){var c=s[t];this[c]=o[c]}if(void 0!==this.start){if("number"!=typeof this.start)throw TypeError("start must be a Number");if(this.start<0)throw new Error("start must be >= zero");this.pos=this.start}this.busy=!1,this._queue=[],null===this.fd&&(this._open=e.open,this._queue.push([this._open,this.path,this.flags,this.mode,void 0]),this.flush())}}}},2070:(e,a,n)=>{var i=n(9140),o=process.cwd,s=null,t=process.env.GRACEFUL_FS_PLATFORM||process.platform;process.cwd=function(){return s||(s=o.call(process)),s};try{process.cwd()}catch(e){}if("function"==typeof process.chdir){var r=process.chdir;process.chdir=function(e){s=null,r.call(process,e)},Object.setPrototypeOf&&Object.setPrototypeOf(process.chdir,r)}e.exports=function(e){function a(a){return a?function(n,i,o){return a.call(e,n,i,(function(e){p(e)&&(e=null),o&&o.apply(this,arguments)}))}:a}function n(a){return a?function(n,i){try{return a.call(e,n,i)}catch(e){if(!p(e))throw e}}:a}function o(a){return a?function(n,i,o,s){return a.call(e,n,i,o,(function(e){p(e)&&(e=null),s&&s.apply(this,arguments)}))}:a}function s(a){return a?function(n,i,o){try{return a.call(e,n,i,o)}catch(e){if(!p(e))throw e}}:a}function r(a){return a?function(n,i,o){function s(e,a){a&&(a.uid<0&&(a.uid+=4294967296),a.gid<0&&(a.gid+=4294967296)),o&&o.apply(this,arguments)}return"function"==typeof i&&(o=i,i=null),i?a.call(e,n,i,s):a.call(e,n,s)}:a}function c(a){return a?function(n,i){var o=i?a.call(e,n,i):a.call(e,n);return o&&(o.uid<0&&(o.uid+=4294967296),o.gid<0&&(o.gid+=4294967296)),o}:a}function p(e){return!e||"ENOSYS"===e.code||!(process.getuid&&0===process.getuid()||"EINVAL"!==e.code&&"EPERM"!==e.code)}var l;i.hasOwnProperty("O_SYMLINK")&&process.version.match(/^v0\.6\.[0-2]|^v0\.5\./)&&function(e){e.lchmod=function(a,n,o){e.open(a,i.O_WRONLY|i.O_SYMLINK,n,(function(a,i){a?o&&o(a):e.fchmod(i,n,(function(a){e.close(i,(function(e){o&&o(a||e)}))}))}))},e.lchmodSync=function(a,n){var o,s=e.openSync(a,i.O_WRONLY|i.O_SYMLINK,n),t=!0;try{o=e.fchmodSync(s,n),t=!1}finally{if(t)try{e.closeSync(s)}catch(e){}else e.closeSync(s)}return o}}(e),e.lutimes||function(e){i.hasOwnProperty("O_SYMLINK")&&e.futimes?(e.lutimes=function(a,n,o,s){e.open(a,i.O_SYMLINK,(function(a,i){a?s&&s(a):e.futimes(i,n,o,(function(a){e.close(i,(function(e){s&&s(a||e)}))}))}))},e.lutimesSync=function(a,n,o){var s,t=e.openSync(a,i.O_SYMLINK),r=!0;try{s=e.futimesSync(t,n,o),r=!1}finally{if(r)try{e.closeSync(t)}catch(e){}else e.closeSync(t)}return s}):e.futimes&&(e.lutimes=function(e,a,n,i){i&&process.nextTick(i)},e.lutimesSync=function(){})}(e),e.chown=o(e.chown),e.fchown=o(e.fchown),e.lchown=o(e.lchown),e.chmod=a(e.chmod),e.fchmod=a(e.fchmod),e.lchmod=a(e.lchmod),e.chownSync=s(e.chownSync),e.fchownSync=s(e.fchownSync),e.lchownSync=s(e.lchownSync),e.chmodSync=n(e.chmodSync),e.fchmodSync=n(e.fchmodSync),e.lchmodSync=n(e.lchmodSync),e.stat=r(e.stat),e.fstat=r(e.fstat),e.lstat=r(e.lstat),e.statSync=c(e.statSync),e.fstatSync=c(e.fstatSync),e.lstatSync=c(e.lstatSync),e.chmod&&!e.lchmod&&(e.lchmod=function(e,a,n){n&&process.nextTick(n)},e.lchmodSync=function(){}),e.chown&&!e.lchown&&(e.lchown=function(e,a,n,i){i&&process.nextTick(i)},e.lchownSync=function(){}),"win32"===t&&(e.rename="function"!=typeof e.rename?e.rename:function(a){function n(n,i,o){var s=Date.now(),t=0;a(n,i,(function r(c){if(c&&("EACCES"===c.code||"EPERM"===c.code||"EBUSY"===c.code)&&Date.now()-s<6e4)return setTimeout((function(){e.stat(i,(function(e,s){e&&"ENOENT"===e.code?a(n,i,r):o(c)}))}),t),void(t<100&&(t+=10));o&&o(c)}))}return Object.setPrototypeOf&&Object.setPrototypeOf(n,a),n}(e.rename)),e.read="function"!=typeof e.read?e.read:function(a){function n(n,i,o,s,t,r){var c;if(r&&"function"==typeof r){var p=0;c=function(l,u,m){if(l&&"EAGAIN"===l.code&&p<10)return p++,a.call(e,n,i,o,s,t,c);r.apply(this,arguments)}}return a.call(e,n,i,o,s,t,c)}return Object.setPrototypeOf&&Object.setPrototypeOf(n,a),n}(e.read),e.readSync="function"!=typeof e.readSync?e.readSync:(l=e.readSync,function(a,n,i,o,s){for(var t=0;;)try{return l.call(e,a,n,i,o,s)}catch(e){if("EAGAIN"===e.code&&t<10){t++;continue}throw e}})}},7702:e=>{"use strict";e.exports=(e,a=process.argv)=>{const n=e.startsWith("-")?"":1===e.length?"-":"--",i=a.indexOf(n+e),o=a.indexOf("--");return-1!==i&&(-1===o||i<o)}},6190:e=>{function a(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}e.exports=function(e){return null!=e&&(a(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&a(e.slice(0,0))}(e)||!!e._isBuffer)}},6259:(e,a,n)=>{let i;try{i=n(9851)}catch(e){i=n(9896)}const o=n(1171),{stringify:s,stripBom:t}=n(5646),r={readFile:o.fromPromise((async function(e,a={}){"string"==typeof a&&(a={encoding:a});const n=a.fs||i,s=!("throws"in a)||a.throws;let r,c=await o.fromCallback(n.readFile)(e,a);c=t(c);try{r=JSON.parse(c,a?a.reviver:null)}catch(a){if(s)throw a.message=`${e}: ${a.message}`,a;return null}return r})),readFileSync:function(e,a={}){"string"==typeof a&&(a={encoding:a});const n=a.fs||i,o=!("throws"in a)||a.throws;try{let i=n.readFileSync(e,a);return i=t(i),JSON.parse(i,a.reviver)}catch(a){if(o)throw a.message=`${e}: ${a.message}`,a;return null}},writeFile:o.fromPromise((async function(e,a,n={}){const t=n.fs||i,r=s(a,n);await o.fromCallback(t.writeFile)(e,r,n)})),writeFileSync:function(e,a,n={}){const o=n.fs||i,t=s(a,n);return o.writeFileSync(e,t,n)}};e.exports=r},5646:e=>{e.exports={stringify:function(e,{EOL:a="\n",finalEOL:n=!0,replacer:i=null,spaces:o}={}){const s=n?a:"";return JSON.stringify(e,i,o).replace(/\n/g,a)+s},stripBom:function(e){return Buffer.isBuffer(e)&&(e=e.toString("utf8")),e.replace(/^\uFEFF/,"")}}},5643:(e,a,n)=>{var i,o,s,t,r;i=n(6480),o=n(7012).utf8,s=n(6190),t=n(7012).bin,(r=function(e,a){e.constructor==String?e=a&&"binary"===a.encoding?t.stringToBytes(e):o.stringToBytes(e):s(e)?e=Array.prototype.slice.call(e,0):Array.isArray(e)||e.constructor===Uint8Array||(e=e.toString());for(var n=i.bytesToWords(e),c=8*e.length,p=1732584193,l=-271733879,u=-1732584194,m=271733878,d=0;d<n.length;d++)n[d]=16711935&(n[d]<<8|n[d]>>>24)|4278255360&(n[d]<<24|n[d]>>>8);n[c>>>5]|=128<<c%32,n[14+(c+64>>>9<<4)]=c;var f=r._ff,x=r._gg,h=r._hh,v=r._ii;for(d=0;d<n.length;d+=16){var b=p,g=l,y=u,w=m;p=f(p,l,u,m,n[d+0],7,-680876936),m=f(m,p,l,u,n[d+1],12,-389564586),u=f(u,m,p,l,n[d+2],17,606105819),l=f(l,u,m,p,n[d+3],22,-1044525330),p=f(p,l,u,m,n[d+4],7,-176418897),m=f(m,p,l,u,n[d+5],12,1200080426),u=f(u,m,p,l,n[d+6],17,-1473231341),l=f(l,u,m,p,n[d+7],22,-45705983),p=f(p,l,u,m,n[d+8],7,1770035416),m=f(m,p,l,u,n[d+9],12,-1958414417),u=f(u,m,p,l,n[d+10],17,-42063),l=f(l,u,m,p,n[d+11],22,-1990404162),p=f(p,l,u,m,n[d+12],7,1804603682),m=f(m,p,l,u,n[d+13],12,-40341101),u=f(u,m,p,l,n[d+14],17,-1502002290),p=x(p,l=f(l,u,m,p,n[d+15],22,1236535329),u,m,n[d+1],5,-165796510),m=x(m,p,l,u,n[d+6],9,-1069501632),u=x(u,m,p,l,n[d+11],14,643717713),l=x(l,u,m,p,n[d+0],20,-373897302),p=x(p,l,u,m,n[d+5],5,-701558691),m=x(m,p,l,u,n[d+10],9,38016083),u=x(u,m,p,l,n[d+15],14,-660478335),l=x(l,u,m,p,n[d+4],20,-405537848),p=x(p,l,u,m,n[d+9],5,568446438),m=x(m,p,l,u,n[d+14],9,-1019803690),u=x(u,m,p,l,n[d+3],14,-187363961),l=x(l,u,m,p,n[d+8],20,1163531501),p=x(p,l,u,m,n[d+13],5,-1444681467),m=x(m,p,l,u,n[d+2],9,-51403784),u=x(u,m,p,l,n[d+7],14,1735328473),p=h(p,l=x(l,u,m,p,n[d+12],20,-1926607734),u,m,n[d+5],4,-378558),m=h(m,p,l,u,n[d+8],11,-2022574463),u=h(u,m,p,l,n[d+11],16,1839030562),l=h(l,u,m,p,n[d+14],23,-35309556),p=h(p,l,u,m,n[d+1],4,-1530992060),m=h(m,p,l,u,n[d+4],11,1272893353),u=h(u,m,p,l,n[d+7],16,-155497632),l=h(l,u,m,p,n[d+10],23,-1094730640),p=h(p,l,u,m,n[d+13],4,681279174),m=h(m,p,l,u,n[d+0],11,-358537222),u=h(u,m,p,l,n[d+3],16,-722521979),l=h(l,u,m,p,n[d+6],23,76029189),p=h(p,l,u,m,n[d+9],4,-640364487),m=h(m,p,l,u,n[d+12],11,-421815835),u=h(u,m,p,l,n[d+15],16,530742520),p=v(p,l=h(l,u,m,p,n[d+2],23,-995338651),u,m,n[d+0],6,-198630844),m=v(m,p,l,u,n[d+7],10,1126891415),u=v(u,m,p,l,n[d+14],15,-1416354905),l=v(l,u,m,p,n[d+5],21,-57434055),p=v(p,l,u,m,n[d+12],6,1700485571),m=v(m,p,l,u,n[d+3],10,-1894986606),u=v(u,m,p,l,n[d+10],15,-1051523),l=v(l,u,m,p,n[d+1],21,-2054922799),p=v(p,l,u,m,n[d+8],6,1873313359),m=v(m,p,l,u,n[d+15],10,-30611744),u=v(u,m,p,l,n[d+6],15,-1560198380),l=v(l,u,m,p,n[d+13],21,1309151649),p=v(p,l,u,m,n[d+4],6,-145523070),m=v(m,p,l,u,n[d+11],10,-1120210379),u=v(u,m,p,l,n[d+2],15,718787259),l=v(l,u,m,p,n[d+9],21,-343485551),p=p+b>>>0,l=l+g>>>0,u=u+y>>>0,m=m+w>>>0}return i.endian([p,l,u,m])})._ff=function(e,a,n,i,o,s,t){var r=e+(a&n|~a&i)+(o>>>0)+t;return(r<<s|r>>>32-s)+a},r._gg=function(e,a,n,i,o,s,t){var r=e+(a&i|n&~i)+(o>>>0)+t;return(r<<s|r>>>32-s)+a},r._hh=function(e,a,n,i,o,s,t){var r=e+(a^n^i)+(o>>>0)+t;return(r<<s|r>>>32-s)+a},r._ii=function(e,a,n,i,o,s,t){var r=e+(n^(a|~i))+(o>>>0)+t;return(r<<s|r>>>32-s)+a},r._blocksize=16,r._digestsize=16,e.exports=function(e,a){if(null==e)throw new Error("Illegal argument "+e);var n=i.wordsToBytes(r(e,a));return a&&a.asBytes?n:a&&a.asString?t.bytesToString(n):i.bytesToHex(n)}},6116:(e,a,n)=>{e.exports=n(7080)},453:(e,a,n)=>{"use strict";var i,o,s,t=n(6116),r=n(6928).extname,c=/^\s*([^;\s]*)(?:;|\s|$)/,p=/^text\//i;function l(e){if(!e||"string"!=typeof e)return!1;var a=c.exec(e),n=a&&t[a[1].toLowerCase()];return n&&n.charset?n.charset:!(!a||!p.test(a[1]))&&"UTF-8"}a.charset=l,a.charsets={lookup:l},a.contentType=function(e){if(!e||"string"!=typeof e)return!1;var n=-1===e.indexOf("/")?a.lookup(e):e;if(!n)return!1;if(-1===n.indexOf("charset")){var i=a.charset(n);i&&(n+="; charset="+i.toLowerCase())}return n},a.extension=function(e){if(!e||"string"!=typeof e)return!1;var n=c.exec(e),i=n&&a.extensions[n[1].toLowerCase()];return!(!i||!i.length)&&i[0]},a.extensions=Object.create(null),a.lookup=function(e){if(!e||"string"!=typeof e)return!1;var n=r("x."+e).toLowerCase().substr(1);return n&&a.types[n]||!1},a.types=Object.create(null),i=a.extensions,o=a.types,s=["nginx","apache",void 0,"iana"],Object.keys(t).forEach((function(e){var a=t[e],n=a.extensions;if(n&&n.length){i[e]=n;for(var r=0;r<n.length;r++){var c=n[r];if(o[c]){var p=s.indexOf(t[o[c]].source),l=s.indexOf(a.source);if("application/octet-stream"!==o[c]&&(p>l||p===l&&"application/"===o[c].substr(0,12)))continue}o[c]=e}}}))},9173:e=>{var a=1e3,n=60*a,i=60*n,o=24*i,s=7*o;function t(e,a,n,i){var o=a>=1.5*n;return Math.round(e/n)+" "+i+(o?"s":"")}e.exports=function(e,r){r=r||{};var c,p,l=typeof e;if("string"===l&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"weeks":case"week":case"w":return r*s;case"days":case"day":case"d":return r*o;case"hours":case"hour":case"hrs":case"hr":case"h":return r*i;case"minutes":case"minute":case"mins":case"min":case"m":return r*n;case"seconds":case"second":case"secs":case"sec":case"s":return r*a;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}(e);if("number"===l&&isFinite(e))return r.long?(c=e,(p=Math.abs(c))>=o?t(c,p,o,"day"):p>=i?t(c,p,i,"hour"):p>=n?t(c,p,n,"minute"):p>=a?t(c,p,a,"second"):c+" ms"):function(e){var s=Math.abs(e);return s>=o?Math.round(e/o)+"d":s>=i?Math.round(e/i)+"h":s>=n?Math.round(e/n)+"m":s>=a?Math.round(e/a)+"s":e+"ms"}(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},5574:(e,a,n)=>{"use strict";var i=n(7016).parse,o={ftp:21,gopher:70,http:80,https:443,ws:80,wss:443},s=String.prototype.endsWith||function(e){return e.length<=this.length&&-1!==this.indexOf(e,this.length-e.length)};function t(e){return process.env[e.toLowerCase()]||process.env[e.toUpperCase()]||""}a.T=function(e){var a="string"==typeof e?i(e):e||{},n=a.protocol,r=a.host,c=a.port;if("string"!=typeof r||!r||"string"!=typeof n)return"";if(n=n.split(":",1)[0],!function(e,a){var n=(t("npm_config_no_proxy")||t("no_proxy")).toLowerCase();return!n||"*"!==n&&n.split(/[,\s]/).every((function(n){if(!n)return!0;var i=n.match(/^(.+):(\d+)$/),o=i?i[1]:n,t=i?parseInt(i[2]):0;return!(!t||t===a)||(/^[.*]/.test(o)?("*"===o.charAt(0)&&(o=o.slice(1)),!s.call(e,o)):e!==o)}))}(r=r.replace(/:\d*$/,""),c=parseInt(c)||o[n]||0))return"";var p=t("npm_config_"+n+"_proxy")||t(n+"_proxy")||t("npm_config_proxy")||t("all_proxy");return p&&-1===p.indexOf("://")&&(p=n+"://"+p),p}},4741:(e,a,n)=>{"use strict";const i=n(857),o=n(2018),s=n(7702),{env:t}=process;let r;function c(e){return 0!==e&&{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function p(e,a){if(0===r)return 0;if(s("color=16m")||s("color=full")||s("color=truecolor"))return 3;if(s("color=256"))return 2;if(e&&!a&&void 0===r)return 0;const n=r||0;if("dumb"===t.TERM)return n;if("win32"===process.platform){const e=i.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in t)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","GITHUB_ACTIONS","BUILDKITE"].some((e=>e in t))||"codeship"===t.CI_NAME?1:n;if("TEAMCITY_VERSION"in t)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(t.TEAMCITY_VERSION)?1:0;if("truecolor"===t.COLORTERM)return 3;if("TERM_PROGRAM"in t){const e=parseInt((t.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(t.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(t.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(t.TERM)||"COLORTERM"in t?1:n}s("no-color")||s("no-colors")||s("color=false")||s("color=never")?r=0:(s("color")||s("colors")||s("color=true")||s("color=always"))&&(r=1),"FORCE_COLOR"in t&&(r="true"===t.FORCE_COLOR?1:"false"===t.FORCE_COLOR?0:0===t.FORCE_COLOR.length?1:Math.min(parseInt(t.FORCE_COLOR,10),3)),e.exports={supportsColor:function(e){return c(p(e,e&&e.isTTY))},stdout:c(p(!0,o.isatty(1))),stderr:c(p(!0,o.isatty(2)))}},1171:(e,a)=>{"use strict";a.fromCallback=function(e){return Object.defineProperty((function(...a){if("function"!=typeof a[a.length-1])return new Promise(((n,i)=>{a.push(((e,a)=>null!=e?i(e):n(a))),e.apply(this,a)}));e.apply(this,a)}),"name",{value:e.name})},a.fromPromise=function(e){return Object.defineProperty((function(...a){const n=a[a.length-1];if("function"!=typeof n)return e.apply(this,a);a.pop(),e.apply(this,a).then((e=>n(null,e)),n)}),"name",{value:e.name})}},2613:e=>{"use strict";e.exports=require("assert")},9140:e=>{"use strict";e.exports=require("constants")},9896:e=>{"use strict";e.exports=require("fs")},8611:e=>{"use strict";e.exports=require("http")},5692:e=>{"use strict";e.exports=require("https")},857:e=>{"use strict";e.exports=require("os")},6928:e=>{"use strict";e.exports=require("path")},2203:e=>{"use strict";e.exports=require("stream")},2018:e=>{"use strict";e.exports=require("tty")},7016:e=>{"use strict";e.exports=require("url")},9023:e=>{"use strict";e.exports=require("util")},7080:e=>{"use strict";e.exports=JSON.parse('{"application/1d-interleaved-parityfec":{"source":"iana"},"application/3gpdash-qoe-report+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/3gpp-ims+xml":{"source":"iana","compressible":true},"application/3gpphal+json":{"source":"iana","compressible":true},"application/3gpphalforms+json":{"source":"iana","compressible":true},"application/a2l":{"source":"iana"},"application/ace+cbor":{"source":"iana"},"application/activemessage":{"source":"iana"},"application/activity+json":{"source":"iana","compressible":true},"application/alto-costmap+json":{"source":"iana","compressible":true},"application/alto-costmapfilter+json":{"source":"iana","compressible":true},"application/alto-directory+json":{"source":"iana","compressible":true},"application/alto-endpointcost+json":{"source":"iana","compressible":true},"application/alto-endpointcostparams+json":{"source":"iana","compressible":true},"application/alto-endpointprop+json":{"source":"iana","compressible":true},"application/alto-endpointpropparams+json":{"source":"iana","compressible":true},"application/alto-error+json":{"source":"iana","compressible":true},"application/alto-networkmap+json":{"source":"iana","compressible":true},"application/alto-networkmapfilter+json":{"source":"iana","compressible":true},"application/alto-updatestreamcontrol+json":{"source":"iana","compressible":true},"application/alto-updatestreamparams+json":{"source":"iana","compressible":true},"application/aml":{"source":"iana"},"application/andrew-inset":{"source":"iana","extensions":["ez"]},"application/applefile":{"source":"iana"},"application/applixware":{"source":"apache","extensions":["aw"]},"application/at+jwt":{"source":"iana"},"application/atf":{"source":"iana"},"application/atfx":{"source":"iana"},"application/atom+xml":{"source":"iana","compressible":true,"extensions":["atom"]},"application/atomcat+xml":{"source":"iana","compressible":true,"extensions":["atomcat"]},"application/atomdeleted+xml":{"source":"iana","compressible":true,"extensions":["atomdeleted"]},"application/atomicmail":{"source":"iana"},"application/atomsvc+xml":{"source":"iana","compressible":true,"extensions":["atomsvc"]},"application/atsc-dwd+xml":{"source":"iana","compressible":true,"extensions":["dwd"]},"application/atsc-dynamic-event-message":{"source":"iana"},"application/atsc-held+xml":{"source":"iana","compressible":true,"extensions":["held"]},"application/atsc-rdt+json":{"source":"iana","compressible":true},"application/atsc-rsat+xml":{"source":"iana","compressible":true,"extensions":["rsat"]},"application/atxml":{"source":"iana"},"application/auth-policy+xml":{"source":"iana","compressible":true},"application/bacnet-xdd+zip":{"source":"iana","compressible":false},"application/batch-smtp":{"source":"iana"},"application/bdoc":{"compressible":false,"extensions":["bdoc"]},"application/beep+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/calendar+json":{"source":"iana","compressible":true},"application/calendar+xml":{"source":"iana","compressible":true,"extensions":["xcs"]},"application/call-completion":{"source":"iana"},"application/cals-1840":{"source":"iana"},"application/captive+json":{"source":"iana","compressible":true},"application/cbor":{"source":"iana"},"application/cbor-seq":{"source":"iana"},"application/cccex":{"source":"iana"},"application/ccmp+xml":{"source":"iana","compressible":true},"application/ccxml+xml":{"source":"iana","compressible":true,"extensions":["ccxml"]},"application/cdfx+xml":{"source":"iana","compressible":true,"extensions":["cdfx"]},"application/cdmi-capability":{"source":"iana","extensions":["cdmia"]},"application/cdmi-container":{"source":"iana","extensions":["cdmic"]},"application/cdmi-domain":{"source":"iana","extensions":["cdmid"]},"application/cdmi-object":{"source":"iana","extensions":["cdmio"]},"application/cdmi-queue":{"source":"iana","extensions":["cdmiq"]},"application/cdni":{"source":"iana"},"application/cea":{"source":"iana"},"application/cea-2018+xml":{"source":"iana","compressible":true},"application/cellml+xml":{"source":"iana","compressible":true},"application/cfw":{"source":"iana"},"application/city+json":{"source":"iana","compressible":true},"application/clr":{"source":"iana"},"application/clue+xml":{"source":"iana","compressible":true},"application/clue_info+xml":{"source":"iana","compressible":true},"application/cms":{"source":"iana"},"application/cnrp+xml":{"source":"iana","compressible":true},"application/coap-group+json":{"source":"iana","compressible":true},"application/coap-payload":{"source":"iana"},"application/commonground":{"source":"iana"},"application/conference-info+xml":{"source":"iana","compressible":true},"application/cose":{"source":"iana"},"application/cose-key":{"source":"iana"},"application/cose-key-set":{"source":"iana"},"application/cpl+xml":{"source":"iana","compressible":true,"extensions":["cpl"]},"application/csrattrs":{"source":"iana"},"application/csta+xml":{"source":"iana","compressible":true},"application/cstadata+xml":{"source":"iana","compressible":true},"application/csvm+json":{"source":"iana","compressible":true},"application/cu-seeme":{"source":"apache","extensions":["cu"]},"application/cwt":{"source":"iana"},"application/cybercash":{"source":"iana"},"application/dart":{"compressible":true},"application/dash+xml":{"source":"iana","compressible":true,"extensions":["mpd"]},"application/dash-patch+xml":{"source":"iana","compressible":true,"extensions":["mpp"]},"application/dashdelta":{"source":"iana"},"application/davmount+xml":{"source":"iana","compressible":true,"extensions":["davmount"]},"application/dca-rft":{"source":"iana"},"application/dcd":{"source":"iana"},"application/dec-dx":{"source":"iana"},"application/dialog-info+xml":{"source":"iana","compressible":true},"application/dicom":{"source":"iana"},"application/dicom+json":{"source":"iana","compressible":true},"application/dicom+xml":{"source":"iana","compressible":true},"application/dii":{"source":"iana"},"application/dit":{"source":"iana"},"application/dns":{"source":"iana"},"application/dns+json":{"source":"iana","compressible":true},"application/dns-message":{"source":"iana"},"application/docbook+xml":{"source":"apache","compressible":true,"extensions":["dbk"]},"application/dots+cbor":{"source":"iana"},"application/dskpp+xml":{"source":"iana","compressible":true},"application/dssc+der":{"source":"iana","extensions":["dssc"]},"application/dssc+xml":{"source":"iana","compressible":true,"extensions":["xdssc"]},"application/dvcs":{"source":"iana"},"application/ecmascript":{"source":"iana","compressible":true,"extensions":["es","ecma"]},"application/edi-consent":{"source":"iana"},"application/edi-x12":{"source":"iana","compressible":false},"application/edifact":{"source":"iana","compressible":false},"application/efi":{"source":"iana"},"application/elm+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/elm+xml":{"source":"iana","compressible":true},"application/emergencycalldata.cap+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/emergencycalldata.comment+xml":{"source":"iana","compressible":true},"application/emergencycalldata.control+xml":{"source":"iana","compressible":true},"application/emergencycalldata.deviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.ecall.msd":{"source":"iana"},"application/emergencycalldata.providerinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.serviceinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.subscriberinfo+xml":{"source":"iana","compressible":true},"application/emergencycalldata.veds+xml":{"source":"iana","compressible":true},"application/emma+xml":{"source":"iana","compressible":true,"extensions":["emma"]},"application/emotionml+xml":{"source":"iana","compressible":true,"extensions":["emotionml"]},"application/encaprtp":{"source":"iana"},"application/epp+xml":{"source":"iana","compressible":true},"application/epub+zip":{"source":"iana","compressible":false,"extensions":["epub"]},"application/eshop":{"source":"iana"},"application/exi":{"source":"iana","extensions":["exi"]},"application/expect-ct-report+json":{"source":"iana","compressible":true},"application/express":{"source":"iana","extensions":["exp"]},"application/fastinfoset":{"source":"iana"},"application/fastsoap":{"source":"iana"},"application/fdt+xml":{"source":"iana","compressible":true,"extensions":["fdt"]},"application/fhir+json":{"source":"iana","charset":"UTF-8","compressible":true},"application/fhir+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/fido.trusted-apps+json":{"compressible":true},"application/fits":{"source":"iana"},"application/flexfec":{"source":"iana"},"application/font-sfnt":{"source":"iana"},"application/font-tdpfr":{"source":"iana","extensions":["pfr"]},"application/font-woff":{"source":"iana","compressible":false},"application/framework-attributes+xml":{"source":"iana","compressible":true},"application/geo+json":{"source":"iana","compressible":true,"extensions":["geojson"]},"application/geo+json-seq":{"source":"iana"},"application/geopackage+sqlite3":{"source":"iana"},"application/geoxacml+xml":{"source":"iana","compressible":true},"application/gltf-buffer":{"source":"iana"},"application/gml+xml":{"source":"iana","compressible":true,"extensions":["gml"]},"application/gpx+xml":{"source":"apache","compressible":true,"extensions":["gpx"]},"application/gxf":{"source":"apache","extensions":["gxf"]},"application/gzip":{"source":"iana","compressible":false,"extensions":["gz"]},"application/h224":{"source":"iana"},"application/held+xml":{"source":"iana","compressible":true},"application/hjson":{"extensions":["hjson"]},"application/http":{"source":"iana"},"application/hyperstudio":{"source":"iana","extensions":["stk"]},"application/ibe-key-request+xml":{"source":"iana","compressible":true},"application/ibe-pkg-reply+xml":{"source":"iana","compressible":true},"application/ibe-pp-data":{"source":"iana"},"application/iges":{"source":"iana"},"application/im-iscomposing+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/index":{"source":"iana"},"application/index.cmd":{"source":"iana"},"application/index.obj":{"source":"iana"},"application/index.response":{"source":"iana"},"application/index.vnd":{"source":"iana"},"application/inkml+xml":{"source":"iana","compressible":true,"extensions":["ink","inkml"]},"application/iotp":{"source":"iana"},"application/ipfix":{"source":"iana","extensions":["ipfix"]},"application/ipp":{"source":"iana"},"application/isup":{"source":"iana"},"application/its+xml":{"source":"iana","compressible":true,"extensions":["its"]},"application/java-archive":{"source":"apache","compressible":false,"extensions":["jar","war","ear"]},"application/java-serialized-object":{"source":"apache","compressible":false,"extensions":["ser"]},"application/java-vm":{"source":"apache","compressible":false,"extensions":["class"]},"application/javascript":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["js","mjs"]},"application/jf2feed+json":{"source":"iana","compressible":true},"application/jose":{"source":"iana"},"application/jose+json":{"source":"iana","compressible":true},"application/jrd+json":{"source":"iana","compressible":true},"application/jscalendar+json":{"source":"iana","compressible":true},"application/json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["json","map"]},"application/json-patch+json":{"source":"iana","compressible":true},"application/json-seq":{"source":"iana"},"application/json5":{"extensions":["json5"]},"application/jsonml+json":{"source":"apache","compressible":true,"extensions":["jsonml"]},"application/jwk+json":{"source":"iana","compressible":true},"application/jwk-set+json":{"source":"iana","compressible":true},"application/jwt":{"source":"iana"},"application/kpml-request+xml":{"source":"iana","compressible":true},"application/kpml-response+xml":{"source":"iana","compressible":true},"application/ld+json":{"source":"iana","compressible":true,"extensions":["jsonld"]},"application/lgr+xml":{"source":"iana","compressible":true,"extensions":["lgr"]},"application/link-format":{"source":"iana"},"application/load-control+xml":{"source":"iana","compressible":true},"application/lost+xml":{"source":"iana","compressible":true,"extensions":["lostxml"]},"application/lostsync+xml":{"source":"iana","compressible":true},"application/lpf+zip":{"source":"iana","compressible":false},"application/lxf":{"source":"iana"},"application/mac-binhex40":{"source":"iana","extensions":["hqx"]},"application/mac-compactpro":{"source":"apache","extensions":["cpt"]},"application/macwriteii":{"source":"iana"},"application/mads+xml":{"source":"iana","compressible":true,"extensions":["mads"]},"application/manifest+json":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["webmanifest"]},"application/marc":{"source":"iana","extensions":["mrc"]},"application/marcxml+xml":{"source":"iana","compressible":true,"extensions":["mrcx"]},"application/mathematica":{"source":"iana","extensions":["ma","nb","mb"]},"application/mathml+xml":{"source":"iana","compressible":true,"extensions":["mathml"]},"application/mathml-content+xml":{"source":"iana","compressible":true},"application/mathml-presentation+xml":{"source":"iana","compressible":true},"application/mbms-associated-procedure-description+xml":{"source":"iana","compressible":true},"application/mbms-deregister+xml":{"source":"iana","compressible":true},"application/mbms-envelope+xml":{"source":"iana","compressible":true},"application/mbms-msk+xml":{"source":"iana","compressible":true},"application/mbms-msk-response+xml":{"source":"iana","compressible":true},"application/mbms-protection-description+xml":{"source":"iana","compressible":true},"application/mbms-reception-report+xml":{"source":"iana","compressible":true},"application/mbms-register+xml":{"source":"iana","compressible":true},"application/mbms-register-response+xml":{"source":"iana","compressible":true},"application/mbms-schedule+xml":{"source":"iana","compressible":true},"application/mbms-user-service-description+xml":{"source":"iana","compressible":true},"application/mbox":{"source":"iana","extensions":["mbox"]},"application/media-policy-dataset+xml":{"source":"iana","compressible":true,"extensions":["mpf"]},"application/media_control+xml":{"source":"iana","compressible":true},"application/mediaservercontrol+xml":{"source":"iana","compressible":true,"extensions":["mscml"]},"application/merge-patch+json":{"source":"iana","compressible":true},"application/metalink+xml":{"source":"apache","compressible":true,"extensions":["metalink"]},"application/metalink4+xml":{"source":"iana","compressible":true,"extensions":["meta4"]},"application/mets+xml":{"source":"iana","compressible":true,"extensions":["mets"]},"application/mf4":{"source":"iana"},"application/mikey":{"source":"iana"},"application/mipc":{"source":"iana"},"application/missing-blocks+cbor-seq":{"source":"iana"},"application/mmt-aei+xml":{"source":"iana","compressible":true,"extensions":["maei"]},"application/mmt-usd+xml":{"source":"iana","compressible":true,"extensions":["musd"]},"application/mods+xml":{"source":"iana","compressible":true,"extensions":["mods"]},"application/moss-keys":{"source":"iana"},"application/moss-signature":{"source":"iana"},"application/mosskey-data":{"source":"iana"},"application/mosskey-request":{"source":"iana"},"application/mp21":{"source":"iana","extensions":["m21","mp21"]},"application/mp4":{"source":"iana","extensions":["mp4s","m4p"]},"application/mpeg4-generic":{"source":"iana"},"application/mpeg4-iod":{"source":"iana"},"application/mpeg4-iod-xmt":{"source":"iana"},"application/mrb-consumer+xml":{"source":"iana","compressible":true},"application/mrb-publish+xml":{"source":"iana","compressible":true},"application/msc-ivr+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msc-mixer+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/msword":{"source":"iana","compressible":false,"extensions":["doc","dot"]},"application/mud+json":{"source":"iana","compressible":true},"application/multipart-core":{"source":"iana"},"application/mxf":{"source":"iana","extensions":["mxf"]},"application/n-quads":{"source":"iana","extensions":["nq"]},"application/n-triples":{"source":"iana","extensions":["nt"]},"application/nasdata":{"source":"iana"},"application/news-checkgroups":{"source":"iana","charset":"US-ASCII"},"application/news-groupinfo":{"source":"iana","charset":"US-ASCII"},"application/news-transmission":{"source":"iana"},"application/nlsml+xml":{"source":"iana","compressible":true},"application/node":{"source":"iana","extensions":["cjs"]},"application/nss":{"source":"iana"},"application/oauth-authz-req+jwt":{"source":"iana"},"application/oblivious-dns-message":{"source":"iana"},"application/ocsp-request":{"source":"iana"},"application/ocsp-response":{"source":"iana"},"application/octet-stream":{"source":"iana","compressible":false,"extensions":["bin","dms","lrf","mar","so","dist","distz","pkg","bpk","dump","elc","deploy","exe","dll","deb","dmg","iso","img","msi","msp","msm","buffer"]},"application/oda":{"source":"iana","extensions":["oda"]},"application/odm+xml":{"source":"iana","compressible":true},"application/odx":{"source":"iana"},"application/oebps-package+xml":{"source":"iana","compressible":true,"extensions":["opf"]},"application/ogg":{"source":"iana","compressible":false,"extensions":["ogx"]},"application/omdoc+xml":{"source":"apache","compressible":true,"extensions":["omdoc"]},"application/onenote":{"source":"apache","extensions":["onetoc","onetoc2","onetmp","onepkg"]},"application/opc-nodeset+xml":{"source":"iana","compressible":true},"application/oscore":{"source":"iana"},"application/oxps":{"source":"iana","extensions":["oxps"]},"application/p21":{"source":"iana"},"application/p21+zip":{"source":"iana","compressible":false},"application/p2p-overlay+xml":{"source":"iana","compressible":true,"extensions":["relo"]},"application/parityfec":{"source":"iana"},"application/passport":{"source":"iana"},"application/patch-ops-error+xml":{"source":"iana","compressible":true,"extensions":["xer"]},"application/pdf":{"source":"iana","compressible":false,"extensions":["pdf"]},"application/pdx":{"source":"iana"},"application/pem-certificate-chain":{"source":"iana"},"application/pgp-encrypted":{"source":"iana","compressible":false,"extensions":["pgp"]},"application/pgp-keys":{"source":"iana","extensions":["asc"]},"application/pgp-signature":{"source":"iana","extensions":["asc","sig"]},"application/pics-rules":{"source":"apache","extensions":["prf"]},"application/pidf+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pidf-diff+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/pkcs10":{"source":"iana","extensions":["p10"]},"application/pkcs12":{"source":"iana"},"application/pkcs7-mime":{"source":"iana","extensions":["p7m","p7c"]},"application/pkcs7-signature":{"source":"iana","extensions":["p7s"]},"application/pkcs8":{"source":"iana","extensions":["p8"]},"application/pkcs8-encrypted":{"source":"iana"},"application/pkix-attr-cert":{"source":"iana","extensions":["ac"]},"application/pkix-cert":{"source":"iana","extensions":["cer"]},"application/pkix-crl":{"source":"iana","extensions":["crl"]},"application/pkix-pkipath":{"source":"iana","extensions":["pkipath"]},"application/pkixcmp":{"source":"iana","extensions":["pki"]},"application/pls+xml":{"source":"iana","compressible":true,"extensions":["pls"]},"application/poc-settings+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/postscript":{"source":"iana","compressible":true,"extensions":["ai","eps","ps"]},"application/ppsp-tracker+json":{"source":"iana","compressible":true},"application/problem+json":{"source":"iana","compressible":true},"application/problem+xml":{"source":"iana","compressible":true},"application/provenance+xml":{"source":"iana","compressible":true,"extensions":["provx"]},"application/prs.alvestrand.titrax-sheet":{"source":"iana"},"application/prs.cww":{"source":"iana","extensions":["cww"]},"application/prs.cyn":{"source":"iana","charset":"7-BIT"},"application/prs.hpub+zip":{"source":"iana","compressible":false},"application/prs.nprend":{"source":"iana"},"application/prs.plucker":{"source":"iana"},"application/prs.rdf-xml-crypt":{"source":"iana"},"application/prs.xsf+xml":{"source":"iana","compressible":true},"application/pskc+xml":{"source":"iana","compressible":true,"extensions":["pskcxml"]},"application/pvd+json":{"source":"iana","compressible":true},"application/qsig":{"source":"iana"},"application/raml+yaml":{"compressible":true,"extensions":["raml"]},"application/raptorfec":{"source":"iana"},"application/rdap+json":{"source":"iana","compressible":true},"application/rdf+xml":{"source":"iana","compressible":true,"extensions":["rdf","owl"]},"application/reginfo+xml":{"source":"iana","compressible":true,"extensions":["rif"]},"application/relax-ng-compact-syntax":{"source":"iana","extensions":["rnc"]},"application/remote-printing":{"source":"iana"},"application/reputon+json":{"source":"iana","compressible":true},"application/resource-lists+xml":{"source":"iana","compressible":true,"extensions":["rl"]},"application/resource-lists-diff+xml":{"source":"iana","compressible":true,"extensions":["rld"]},"application/rfc+xml":{"source":"iana","compressible":true},"application/riscos":{"source":"iana"},"application/rlmi+xml":{"source":"iana","compressible":true},"application/rls-services+xml":{"source":"iana","compressible":true,"extensions":["rs"]},"application/route-apd+xml":{"source":"iana","compressible":true,"extensions":["rapd"]},"application/route-s-tsid+xml":{"source":"iana","compressible":true,"extensions":["sls"]},"application/route-usd+xml":{"source":"iana","compressible":true,"extensions":["rusd"]},"application/rpki-ghostbusters":{"source":"iana","extensions":["gbr"]},"application/rpki-manifest":{"source":"iana","extensions":["mft"]},"application/rpki-publication":{"source":"iana"},"application/rpki-roa":{"source":"iana","extensions":["roa"]},"application/rpki-updown":{"source":"iana"},"application/rsd+xml":{"source":"apache","compressible":true,"extensions":["rsd"]},"application/rss+xml":{"source":"apache","compressible":true,"extensions":["rss"]},"application/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"application/rtploopback":{"source":"iana"},"application/rtx":{"source":"iana"},"application/samlassertion+xml":{"source":"iana","compressible":true},"application/samlmetadata+xml":{"source":"iana","compressible":true},"application/sarif+json":{"source":"iana","compressible":true},"application/sarif-external-properties+json":{"source":"iana","compressible":true},"application/sbe":{"source":"iana"},"application/sbml+xml":{"source":"iana","compressible":true,"extensions":["sbml"]},"application/scaip+xml":{"source":"iana","compressible":true},"application/scim+json":{"source":"iana","compressible":true},"application/scvp-cv-request":{"source":"iana","extensions":["scq"]},"application/scvp-cv-response":{"source":"iana","extensions":["scs"]},"application/scvp-vp-request":{"source":"iana","extensions":["spq"]},"application/scvp-vp-response":{"source":"iana","extensions":["spp"]},"application/sdp":{"source":"iana","extensions":["sdp"]},"application/secevent+jwt":{"source":"iana"},"application/senml+cbor":{"source":"iana"},"application/senml+json":{"source":"iana","compressible":true},"application/senml+xml":{"source":"iana","compressible":true,"extensions":["senmlx"]},"application/senml-etch+cbor":{"source":"iana"},"application/senml-etch+json":{"source":"iana","compressible":true},"application/senml-exi":{"source":"iana"},"application/sensml+cbor":{"source":"iana"},"application/sensml+json":{"source":"iana","compressible":true},"application/sensml+xml":{"source":"iana","compressible":true,"extensions":["sensmlx"]},"application/sensml-exi":{"source":"iana"},"application/sep+xml":{"source":"iana","compressible":true},"application/sep-exi":{"source":"iana"},"application/session-info":{"source":"iana"},"application/set-payment":{"source":"iana"},"application/set-payment-initiation":{"source":"iana","extensions":["setpay"]},"application/set-registration":{"source":"iana"},"application/set-registration-initiation":{"source":"iana","extensions":["setreg"]},"application/sgml":{"source":"iana"},"application/sgml-open-catalog":{"source":"iana"},"application/shf+xml":{"source":"iana","compressible":true,"extensions":["shf"]},"application/sieve":{"source":"iana","extensions":["siv","sieve"]},"application/simple-filter+xml":{"source":"iana","compressible":true},"application/simple-message-summary":{"source":"iana"},"application/simplesymbolcontainer":{"source":"iana"},"application/sipc":{"source":"iana"},"application/slate":{"source":"iana"},"application/smil":{"source":"iana"},"application/smil+xml":{"source":"iana","compressible":true,"extensions":["smi","smil"]},"application/smpte336m":{"source":"iana"},"application/soap+fastinfoset":{"source":"iana"},"application/soap+xml":{"source":"iana","compressible":true},"application/sparql-query":{"source":"iana","extensions":["rq"]},"application/sparql-results+xml":{"source":"iana","compressible":true,"extensions":["srx"]},"application/spdx+json":{"source":"iana","compressible":true},"application/spirits-event+xml":{"source":"iana","compressible":true},"application/sql":{"source":"iana"},"application/srgs":{"source":"iana","extensions":["gram"]},"application/srgs+xml":{"source":"iana","compressible":true,"extensions":["grxml"]},"application/sru+xml":{"source":"iana","compressible":true,"extensions":["sru"]},"application/ssdl+xml":{"source":"apache","compressible":true,"extensions":["ssdl"]},"application/ssml+xml":{"source":"iana","compressible":true,"extensions":["ssml"]},"application/stix+json":{"source":"iana","compressible":true},"application/swid+xml":{"source":"iana","compressible":true,"extensions":["swidtag"]},"application/tamp-apex-update":{"source":"iana"},"application/tamp-apex-update-confirm":{"source":"iana"},"application/tamp-community-update":{"source":"iana"},"application/tamp-community-update-confirm":{"source":"iana"},"application/tamp-error":{"source":"iana"},"application/tamp-sequence-adjust":{"source":"iana"},"application/tamp-sequence-adjust-confirm":{"source":"iana"},"application/tamp-status-query":{"source":"iana"},"application/tamp-status-response":{"source":"iana"},"application/tamp-update":{"source":"iana"},"application/tamp-update-confirm":{"source":"iana"},"application/tar":{"compressible":true},"application/taxii+json":{"source":"iana","compressible":true},"application/td+json":{"source":"iana","compressible":true},"application/tei+xml":{"source":"iana","compressible":true,"extensions":["tei","teicorpus"]},"application/tetra_isi":{"source":"iana"},"application/thraud+xml":{"source":"iana","compressible":true,"extensions":["tfi"]},"application/timestamp-query":{"source":"iana"},"application/timestamp-reply":{"source":"iana"},"application/timestamped-data":{"source":"iana","extensions":["tsd"]},"application/tlsrpt+gzip":{"source":"iana"},"application/tlsrpt+json":{"source":"iana","compressible":true},"application/tnauthlist":{"source":"iana"},"application/token-introspection+jwt":{"source":"iana"},"application/toml":{"compressible":true,"extensions":["toml"]},"application/trickle-ice-sdpfrag":{"source":"iana"},"application/trig":{"source":"iana","extensions":["trig"]},"application/ttml+xml":{"source":"iana","compressible":true,"extensions":["ttml"]},"application/tve-trigger":{"source":"iana"},"application/tzif":{"source":"iana"},"application/tzif-leap":{"source":"iana"},"application/ubjson":{"compressible":false,"extensions":["ubj"]},"application/ulpfec":{"source":"iana"},"application/urc-grpsheet+xml":{"source":"iana","compressible":true},"application/urc-ressheet+xml":{"source":"iana","compressible":true,"extensions":["rsheet"]},"application/urc-targetdesc+xml":{"source":"iana","compressible":true,"extensions":["td"]},"application/urc-uisocketdesc+xml":{"source":"iana","compressible":true},"application/vcard+json":{"source":"iana","compressible":true},"application/vcard+xml":{"source":"iana","compressible":true},"application/vemmi":{"source":"iana"},"application/vividence.scriptfile":{"source":"apache"},"application/vnd.1000minds.decision-model+xml":{"source":"iana","compressible":true,"extensions":["1km"]},"application/vnd.3gpp-prose+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-prose-pc3ch+xml":{"source":"iana","compressible":true},"application/vnd.3gpp-v2x-local-service-information":{"source":"iana"},"application/vnd.3gpp.5gnas":{"source":"iana"},"application/vnd.3gpp.access-transfer-events+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.bsf+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gmop+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.gtpc":{"source":"iana"},"application/vnd.3gpp.interworking-data":{"source":"iana"},"application/vnd.3gpp.lpp":{"source":"iana"},"application/vnd.3gpp.mc-signalling-ear":{"source":"iana"},"application/vnd.3gpp.mcdata-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-payload":{"source":"iana"},"application/vnd.3gpp.mcdata-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-signalling":{"source":"iana"},"application/vnd.3gpp.mcdata-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcdata-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-floor-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-signed+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-ue-init-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcptt-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-command+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-affiliation-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-location-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-mbms-usage-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-service-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-transmission-request+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-ue-config+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mcvideo-user-profile+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.mid-call+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ngap":{"source":"iana"},"application/vnd.3gpp.pfcp":{"source":"iana"},"application/vnd.3gpp.pic-bw-large":{"source":"iana","extensions":["plb"]},"application/vnd.3gpp.pic-bw-small":{"source":"iana","extensions":["psb"]},"application/vnd.3gpp.pic-bw-var":{"source":"iana","extensions":["pvb"]},"application/vnd.3gpp.s1ap":{"source":"iana"},"application/vnd.3gpp.sms":{"source":"iana"},"application/vnd.3gpp.sms+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-ext+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.srvcc-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.state-and-event-info+xml":{"source":"iana","compressible":true},"application/vnd.3gpp.ussd+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.bcmcsinfo+xml":{"source":"iana","compressible":true},"application/vnd.3gpp2.sms":{"source":"iana"},"application/vnd.3gpp2.tcap":{"source":"iana","extensions":["tcap"]},"application/vnd.3lightssoftware.imagescal":{"source":"iana"},"application/vnd.3m.post-it-notes":{"source":"iana","extensions":["pwn"]},"application/vnd.accpac.simply.aso":{"source":"iana","extensions":["aso"]},"application/vnd.accpac.simply.imp":{"source":"iana","extensions":["imp"]},"application/vnd.acucobol":{"source":"iana","extensions":["acu"]},"application/vnd.acucorp":{"source":"iana","extensions":["atc","acutc"]},"application/vnd.adobe.air-application-installer-package+zip":{"source":"apache","compressible":false,"extensions":["air"]},"application/vnd.adobe.flash.movie":{"source":"iana"},"application/vnd.adobe.formscentral.fcdt":{"source":"iana","extensions":["fcdt"]},"application/vnd.adobe.fxp":{"source":"iana","extensions":["fxp","fxpl"]},"application/vnd.adobe.partial-upload":{"source":"iana"},"application/vnd.adobe.xdp+xml":{"source":"iana","compressible":true,"extensions":["xdp"]},"application/vnd.adobe.xfdf":{"source":"iana","extensions":["xfdf"]},"application/vnd.aether.imp":{"source":"iana"},"application/vnd.afpc.afplinedata":{"source":"iana"},"application/vnd.afpc.afplinedata-pagedef":{"source":"iana"},"application/vnd.afpc.cmoca-cmresource":{"source":"iana"},"application/vnd.afpc.foca-charset":{"source":"iana"},"application/vnd.afpc.foca-codedfont":{"source":"iana"},"application/vnd.afpc.foca-codepage":{"source":"iana"},"application/vnd.afpc.modca":{"source":"iana"},"application/vnd.afpc.modca-cmtable":{"source":"iana"},"application/vnd.afpc.modca-formdef":{"source":"iana"},"application/vnd.afpc.modca-mediummap":{"source":"iana"},"application/vnd.afpc.modca-objectcontainer":{"source":"iana"},"application/vnd.afpc.modca-overlay":{"source":"iana"},"application/vnd.afpc.modca-pagesegment":{"source":"iana"},"application/vnd.age":{"source":"iana","extensions":["age"]},"application/vnd.ah-barcode":{"source":"iana"},"application/vnd.ahead.space":{"source":"iana","extensions":["ahead"]},"application/vnd.airzip.filesecure.azf":{"source":"iana","extensions":["azf"]},"application/vnd.airzip.filesecure.azs":{"source":"iana","extensions":["azs"]},"application/vnd.amadeus+json":{"source":"iana","compressible":true},"application/vnd.amazon.ebook":{"source":"apache","extensions":["azw"]},"application/vnd.amazon.mobi8-ebook":{"source":"iana"},"application/vnd.americandynamics.acc":{"source":"iana","extensions":["acc"]},"application/vnd.amiga.ami":{"source":"iana","extensions":["ami"]},"application/vnd.amundsen.maze+xml":{"source":"iana","compressible":true},"application/vnd.android.ota":{"source":"iana"},"application/vnd.android.package-archive":{"source":"apache","compressible":false,"extensions":["apk"]},"application/vnd.anki":{"source":"iana"},"application/vnd.anser-web-certificate-issue-initiation":{"source":"iana","extensions":["cii"]},"application/vnd.anser-web-funds-transfer-initiation":{"source":"apache","extensions":["fti"]},"application/vnd.antix.game-component":{"source":"iana","extensions":["atx"]},"application/vnd.apache.arrow.file":{"source":"iana"},"application/vnd.apache.arrow.stream":{"source":"iana"},"application/vnd.apache.thrift.binary":{"source":"iana"},"application/vnd.apache.thrift.compact":{"source":"iana"},"application/vnd.apache.thrift.json":{"source":"iana"},"application/vnd.api+json":{"source":"iana","compressible":true},"application/vnd.aplextor.warrp+json":{"source":"iana","compressible":true},"application/vnd.apothekende.reservation+json":{"source":"iana","compressible":true},"application/vnd.apple.installer+xml":{"source":"iana","compressible":true,"extensions":["mpkg"]},"application/vnd.apple.keynote":{"source":"iana","extensions":["key"]},"application/vnd.apple.mpegurl":{"source":"iana","extensions":["m3u8"]},"application/vnd.apple.numbers":{"source":"iana","extensions":["numbers"]},"application/vnd.apple.pages":{"source":"iana","extensions":["pages"]},"application/vnd.apple.pkpass":{"compressible":false,"extensions":["pkpass"]},"application/vnd.arastra.swi":{"source":"iana"},"application/vnd.aristanetworks.swi":{"source":"iana","extensions":["swi"]},"application/vnd.artisan+json":{"source":"iana","compressible":true},"application/vnd.artsquare":{"source":"iana"},"application/vnd.astraea-software.iota":{"source":"iana","extensions":["iota"]},"application/vnd.audiograph":{"source":"iana","extensions":["aep"]},"application/vnd.autopackage":{"source":"iana"},"application/vnd.avalon+json":{"source":"iana","compressible":true},"application/vnd.avistar+xml":{"source":"iana","compressible":true},"application/vnd.balsamiq.bmml+xml":{"source":"iana","compressible":true,"extensions":["bmml"]},"application/vnd.balsamiq.bmpr":{"source":"iana"},"application/vnd.banana-accounting":{"source":"iana"},"application/vnd.bbf.usp.error":{"source":"iana"},"application/vnd.bbf.usp.msg":{"source":"iana"},"application/vnd.bbf.usp.msg+json":{"source":"iana","compressible":true},"application/vnd.bekitzur-stech+json":{"source":"iana","compressible":true},"application/vnd.bint.med-content":{"source":"iana"},"application/vnd.biopax.rdf+xml":{"source":"iana","compressible":true},"application/vnd.blink-idb-value-wrapper":{"source":"iana"},"application/vnd.blueice.multipass":{"source":"iana","extensions":["mpm"]},"application/vnd.bluetooth.ep.oob":{"source":"iana"},"application/vnd.bluetooth.le.oob":{"source":"iana"},"application/vnd.bmi":{"source":"iana","extensions":["bmi"]},"application/vnd.bpf":{"source":"iana"},"application/vnd.bpf3":{"source":"iana"},"application/vnd.businessobjects":{"source":"iana","extensions":["rep"]},"application/vnd.byu.uapi+json":{"source":"iana","compressible":true},"application/vnd.cab-jscript":{"source":"iana"},"application/vnd.canon-cpdl":{"source":"iana"},"application/vnd.canon-lips":{"source":"iana"},"application/vnd.capasystems-pg+json":{"source":"iana","compressible":true},"application/vnd.cendio.thinlinc.clientconf":{"source":"iana"},"application/vnd.century-systems.tcp_stream":{"source":"iana"},"application/vnd.chemdraw+xml":{"source":"iana","compressible":true,"extensions":["cdxml"]},"application/vnd.chess-pgn":{"source":"iana"},"application/vnd.chipnuts.karaoke-mmd":{"source":"iana","extensions":["mmd"]},"application/vnd.ciedi":{"source":"iana"},"application/vnd.cinderella":{"source":"iana","extensions":["cdy"]},"application/vnd.cirpack.isdn-ext":{"source":"iana"},"application/vnd.citationstyles.style+xml":{"source":"iana","compressible":true,"extensions":["csl"]},"application/vnd.claymore":{"source":"iana","extensions":["cla"]},"application/vnd.cloanto.rp9":{"source":"iana","extensions":["rp9"]},"application/vnd.clonk.c4group":{"source":"iana","extensions":["c4g","c4d","c4f","c4p","c4u"]},"application/vnd.cluetrust.cartomobile-config":{"source":"iana","extensions":["c11amc"]},"application/vnd.cluetrust.cartomobile-config-pkg":{"source":"iana","extensions":["c11amz"]},"application/vnd.coffeescript":{"source":"iana"},"application/vnd.collabio.xodocuments.document":{"source":"iana"},"application/vnd.collabio.xodocuments.document-template":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation":{"source":"iana"},"application/vnd.collabio.xodocuments.presentation-template":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet":{"source":"iana"},"application/vnd.collabio.xodocuments.spreadsheet-template":{"source":"iana"},"application/vnd.collection+json":{"source":"iana","compressible":true},"application/vnd.collection.doc+json":{"source":"iana","compressible":true},"application/vnd.collection.next+json":{"source":"iana","compressible":true},"application/vnd.comicbook+zip":{"source":"iana","compressible":false},"application/vnd.comicbook-rar":{"source":"iana"},"application/vnd.commerce-battelle":{"source":"iana"},"application/vnd.commonspace":{"source":"iana","extensions":["csp"]},"application/vnd.contact.cmsg":{"source":"iana","extensions":["cdbcmsg"]},"application/vnd.coreos.ignition+json":{"source":"iana","compressible":true},"application/vnd.cosmocaller":{"source":"iana","extensions":["cmc"]},"application/vnd.crick.clicker":{"source":"iana","extensions":["clkx"]},"application/vnd.crick.clicker.keyboard":{"source":"iana","extensions":["clkk"]},"application/vnd.crick.clicker.palette":{"source":"iana","extensions":["clkp"]},"application/vnd.crick.clicker.template":{"source":"iana","extensions":["clkt"]},"application/vnd.crick.clicker.wordbank":{"source":"iana","extensions":["clkw"]},"application/vnd.criticaltools.wbs+xml":{"source":"iana","compressible":true,"extensions":["wbs"]},"application/vnd.cryptii.pipe+json":{"source":"iana","compressible":true},"application/vnd.crypto-shade-file":{"source":"iana"},"application/vnd.cryptomator.encrypted":{"source":"iana"},"application/vnd.cryptomator.vault":{"source":"iana"},"application/vnd.ctc-posml":{"source":"iana","extensions":["pml"]},"application/vnd.ctct.ws+xml":{"source":"iana","compressible":true},"application/vnd.cups-pdf":{"source":"iana"},"application/vnd.cups-postscript":{"source":"iana"},"application/vnd.cups-ppd":{"source":"iana","extensions":["ppd"]},"application/vnd.cups-raster":{"source":"iana"},"application/vnd.cups-raw":{"source":"iana"},"application/vnd.curl":{"source":"iana"},"application/vnd.curl.car":{"source":"apache","extensions":["car"]},"application/vnd.curl.pcurl":{"source":"apache","extensions":["pcurl"]},"application/vnd.cyan.dean.root+xml":{"source":"iana","compressible":true},"application/vnd.cybank":{"source":"iana"},"application/vnd.cyclonedx+json":{"source":"iana","compressible":true},"application/vnd.cyclonedx+xml":{"source":"iana","compressible":true},"application/vnd.d2l.coursepackage1p0+zip":{"source":"iana","compressible":false},"application/vnd.d3m-dataset":{"source":"iana"},"application/vnd.d3m-problem":{"source":"iana"},"application/vnd.dart":{"source":"iana","compressible":true,"extensions":["dart"]},"application/vnd.data-vision.rdz":{"source":"iana","extensions":["rdz"]},"application/vnd.datapackage+json":{"source":"iana","compressible":true},"application/vnd.dataresource+json":{"source":"iana","compressible":true},"application/vnd.dbf":{"source":"iana","extensions":["dbf"]},"application/vnd.debian.binary-package":{"source":"iana"},"application/vnd.dece.data":{"source":"iana","extensions":["uvf","uvvf","uvd","uvvd"]},"application/vnd.dece.ttml+xml":{"source":"iana","compressible":true,"extensions":["uvt","uvvt"]},"application/vnd.dece.unspecified":{"source":"iana","extensions":["uvx","uvvx"]},"application/vnd.dece.zip":{"source":"iana","extensions":["uvz","uvvz"]},"application/vnd.denovo.fcselayout-link":{"source":"iana","extensions":["fe_launch"]},"application/vnd.desmume.movie":{"source":"iana"},"application/vnd.dir-bi.plate-dl-nosuffix":{"source":"iana"},"application/vnd.dm.delegation+xml":{"source":"iana","compressible":true},"application/vnd.dna":{"source":"iana","extensions":["dna"]},"application/vnd.document+json":{"source":"iana","compressible":true},"application/vnd.dolby.mlp":{"source":"apache","extensions":["mlp"]},"application/vnd.dolby.mobile.1":{"source":"iana"},"application/vnd.dolby.mobile.2":{"source":"iana"},"application/vnd.doremir.scorecloud-binary-document":{"source":"iana"},"application/vnd.dpgraph":{"source":"iana","extensions":["dpg"]},"application/vnd.dreamfactory":{"source":"iana","extensions":["dfac"]},"application/vnd.drive+json":{"source":"iana","compressible":true},"application/vnd.ds-keypoint":{"source":"apache","extensions":["kpxx"]},"application/vnd.dtg.local":{"source":"iana"},"application/vnd.dtg.local.flash":{"source":"iana"},"application/vnd.dtg.local.html":{"source":"iana"},"application/vnd.dvb.ait":{"source":"iana","extensions":["ait"]},"application/vnd.dvb.dvbisl+xml":{"source":"iana","compressible":true},"application/vnd.dvb.dvbj":{"source":"iana"},"application/vnd.dvb.esgcontainer":{"source":"iana"},"application/vnd.dvb.ipdcdftnotifaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess":{"source":"iana"},"application/vnd.dvb.ipdcesgaccess2":{"source":"iana"},"application/vnd.dvb.ipdcesgpdd":{"source":"iana"},"application/vnd.dvb.ipdcroaming":{"source":"iana"},"application/vnd.dvb.iptv.alfec-base":{"source":"iana"},"application/vnd.dvb.iptv.alfec-enhancement":{"source":"iana"},"application/vnd.dvb.notif-aggregate-root+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-container+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-generic+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-msglist+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-request+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-ia-registration-response+xml":{"source":"iana","compressible":true},"application/vnd.dvb.notif-init+xml":{"source":"iana","compressible":true},"application/vnd.dvb.pfr":{"source":"iana"},"application/vnd.dvb.service":{"source":"iana","extensions":["svc"]},"application/vnd.dxr":{"source":"iana"},"application/vnd.dynageo":{"source":"iana","extensions":["geo"]},"application/vnd.dzr":{"source":"iana"},"application/vnd.easykaraoke.cdgdownload":{"source":"iana"},"application/vnd.ecdis-update":{"source":"iana"},"application/vnd.ecip.rlp":{"source":"iana"},"application/vnd.eclipse.ditto+json":{"source":"iana","compressible":true},"application/vnd.ecowin.chart":{"source":"iana","extensions":["mag"]},"application/vnd.ecowin.filerequest":{"source":"iana"},"application/vnd.ecowin.fileupdate":{"source":"iana"},"application/vnd.ecowin.series":{"source":"iana"},"application/vnd.ecowin.seriesrequest":{"source":"iana"},"application/vnd.ecowin.seriesupdate":{"source":"iana"},"application/vnd.efi.img":{"source":"iana"},"application/vnd.efi.iso":{"source":"iana"},"application/vnd.emclient.accessrequest+xml":{"source":"iana","compressible":true},"application/vnd.enliven":{"source":"iana","extensions":["nml"]},"application/vnd.enphase.envoy":{"source":"iana"},"application/vnd.eprints.data+xml":{"source":"iana","compressible":true},"application/vnd.epson.esf":{"source":"iana","extensions":["esf"]},"application/vnd.epson.msf":{"source":"iana","extensions":["msf"]},"application/vnd.epson.quickanime":{"source":"iana","extensions":["qam"]},"application/vnd.epson.salt":{"source":"iana","extensions":["slt"]},"application/vnd.epson.ssf":{"source":"iana","extensions":["ssf"]},"application/vnd.ericsson.quickcall":{"source":"iana"},"application/vnd.espass-espass+zip":{"source":"iana","compressible":false},"application/vnd.eszigno3+xml":{"source":"iana","compressible":true,"extensions":["es3","et3"]},"application/vnd.etsi.aoc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.asic-e+zip":{"source":"iana","compressible":false},"application/vnd.etsi.asic-s+zip":{"source":"iana","compressible":false},"application/vnd.etsi.cug+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvcommand+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-bc+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-cod+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsad-npvr+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvservice+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvsync+xml":{"source":"iana","compressible":true},"application/vnd.etsi.iptvueprofile+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mcid+xml":{"source":"iana","compressible":true},"application/vnd.etsi.mheg5":{"source":"iana"},"application/vnd.etsi.overload-control-policy-dataset+xml":{"source":"iana","compressible":true},"application/vnd.etsi.pstn+xml":{"source":"iana","compressible":true},"application/vnd.etsi.sci+xml":{"source":"iana","compressible":true},"application/vnd.etsi.simservs+xml":{"source":"iana","compressible":true},"application/vnd.etsi.timestamp-token":{"source":"iana"},"application/vnd.etsi.tsl+xml":{"source":"iana","compressible":true},"application/vnd.etsi.tsl.der":{"source":"iana"},"application/vnd.eu.kasparian.car+json":{"source":"iana","compressible":true},"application/vnd.eudora.data":{"source":"iana"},"application/vnd.evolv.ecig.profile":{"source":"iana"},"application/vnd.evolv.ecig.settings":{"source":"iana"},"application/vnd.evolv.ecig.theme":{"source":"iana"},"application/vnd.exstream-empower+zip":{"source":"iana","compressible":false},"application/vnd.exstream-package":{"source":"iana"},"application/vnd.ezpix-album":{"source":"iana","extensions":["ez2"]},"application/vnd.ezpix-package":{"source":"iana","extensions":["ez3"]},"application/vnd.f-secure.mobile":{"source":"iana"},"application/vnd.familysearch.gedcom+zip":{"source":"iana","compressible":false},"application/vnd.fastcopy-disk-image":{"source":"iana"},"application/vnd.fdf":{"source":"iana","extensions":["fdf"]},"application/vnd.fdsn.mseed":{"source":"iana","extensions":["mseed"]},"application/vnd.fdsn.seed":{"source":"iana","extensions":["seed","dataless"]},"application/vnd.ffsns":{"source":"iana"},"application/vnd.ficlab.flb+zip":{"source":"iana","compressible":false},"application/vnd.filmit.zfc":{"source":"iana"},"application/vnd.fints":{"source":"iana"},"application/vnd.firemonkeys.cloudcell":{"source":"iana"},"application/vnd.flographit":{"source":"iana","extensions":["gph"]},"application/vnd.fluxtime.clip":{"source":"iana","extensions":["ftc"]},"application/vnd.font-fontforge-sfd":{"source":"iana"},"application/vnd.framemaker":{"source":"iana","extensions":["fm","frame","maker","book"]},"application/vnd.frogans.fnc":{"source":"iana","extensions":["fnc"]},"application/vnd.frogans.ltf":{"source":"iana","extensions":["ltf"]},"application/vnd.fsc.weblaunch":{"source":"iana","extensions":["fsc"]},"application/vnd.fujifilm.fb.docuworks":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.binder":{"source":"iana"},"application/vnd.fujifilm.fb.docuworks.container":{"source":"iana"},"application/vnd.fujifilm.fb.jfi+xml":{"source":"iana","compressible":true},"application/vnd.fujitsu.oasys":{"source":"iana","extensions":["oas"]},"application/vnd.fujitsu.oasys2":{"source":"iana","extensions":["oa2"]},"application/vnd.fujitsu.oasys3":{"source":"iana","extensions":["oa3"]},"application/vnd.fujitsu.oasysgp":{"source":"iana","extensions":["fg5"]},"application/vnd.fujitsu.oasysprs":{"source":"iana","extensions":["bh2"]},"application/vnd.fujixerox.art-ex":{"source":"iana"},"application/vnd.fujixerox.art4":{"source":"iana"},"application/vnd.fujixerox.ddd":{"source":"iana","extensions":["ddd"]},"application/vnd.fujixerox.docuworks":{"source":"iana","extensions":["xdw"]},"application/vnd.fujixerox.docuworks.binder":{"source":"iana","extensions":["xbd"]},"application/vnd.fujixerox.docuworks.container":{"source":"iana"},"application/vnd.fujixerox.hbpl":{"source":"iana"},"application/vnd.fut-misnet":{"source":"iana"},"application/vnd.futoin+cbor":{"source":"iana"},"application/vnd.futoin+json":{"source":"iana","compressible":true},"application/vnd.fuzzysheet":{"source":"iana","extensions":["fzs"]},"application/vnd.genomatix.tuxedo":{"source":"iana","extensions":["txd"]},"application/vnd.gentics.grd+json":{"source":"iana","compressible":true},"application/vnd.geo+json":{"source":"iana","compressible":true},"application/vnd.geocube+xml":{"source":"iana","compressible":true},"application/vnd.geogebra.file":{"source":"iana","extensions":["ggb"]},"application/vnd.geogebra.slides":{"source":"iana"},"application/vnd.geogebra.tool":{"source":"iana","extensions":["ggt"]},"application/vnd.geometry-explorer":{"source":"iana","extensions":["gex","gre"]},"application/vnd.geonext":{"source":"iana","extensions":["gxt"]},"application/vnd.geoplan":{"source":"iana","extensions":["g2w"]},"application/vnd.geospace":{"source":"iana","extensions":["g3w"]},"application/vnd.gerber":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt":{"source":"iana"},"application/vnd.globalplatform.card-content-mgt-response":{"source":"iana"},"application/vnd.gmx":{"source":"iana","extensions":["gmx"]},"application/vnd.google-apps.document":{"compressible":false,"extensions":["gdoc"]},"application/vnd.google-apps.presentation":{"compressible":false,"extensions":["gslides"]},"application/vnd.google-apps.spreadsheet":{"compressible":false,"extensions":["gsheet"]},"application/vnd.google-earth.kml+xml":{"source":"iana","compressible":true,"extensions":["kml"]},"application/vnd.google-earth.kmz":{"source":"iana","compressible":false,"extensions":["kmz"]},"application/vnd.gov.sk.e-form+xml":{"source":"iana","compressible":true},"application/vnd.gov.sk.e-form+zip":{"source":"iana","compressible":false},"application/vnd.gov.sk.xmldatacontainer+xml":{"source":"iana","compressible":true},"application/vnd.grafeq":{"source":"iana","extensions":["gqf","gqs"]},"application/vnd.gridmp":{"source":"iana"},"application/vnd.groove-account":{"source":"iana","extensions":["gac"]},"application/vnd.groove-help":{"source":"iana","extensions":["ghf"]},"application/vnd.groove-identity-message":{"source":"iana","extensions":["gim"]},"application/vnd.groove-injector":{"source":"iana","extensions":["grv"]},"application/vnd.groove-tool-message":{"source":"iana","extensions":["gtm"]},"application/vnd.groove-tool-template":{"source":"iana","extensions":["tpl"]},"application/vnd.groove-vcard":{"source":"iana","extensions":["vcg"]},"application/vnd.hal+json":{"source":"iana","compressible":true},"application/vnd.hal+xml":{"source":"iana","compressible":true,"extensions":["hal"]},"application/vnd.handheld-entertainment+xml":{"source":"iana","compressible":true,"extensions":["zmm"]},"application/vnd.hbci":{"source":"iana","extensions":["hbci"]},"application/vnd.hc+json":{"source":"iana","compressible":true},"application/vnd.hcl-bireports":{"source":"iana"},"application/vnd.hdt":{"source":"iana"},"application/vnd.heroku+json":{"source":"iana","compressible":true},"application/vnd.hhe.lesson-player":{"source":"iana","extensions":["les"]},"application/vnd.hl7cda+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hl7v2+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.hp-hpgl":{"source":"iana","extensions":["hpgl"]},"application/vnd.hp-hpid":{"source":"iana","extensions":["hpid"]},"application/vnd.hp-hps":{"source":"iana","extensions":["hps"]},"application/vnd.hp-jlyt":{"source":"iana","extensions":["jlt"]},"application/vnd.hp-pcl":{"source":"iana","extensions":["pcl"]},"application/vnd.hp-pclxl":{"source":"iana","extensions":["pclxl"]},"application/vnd.httphone":{"source":"iana"},"application/vnd.hydrostatix.sof-data":{"source":"iana","extensions":["sfd-hdstx"]},"application/vnd.hyper+json":{"source":"iana","compressible":true},"application/vnd.hyper-item+json":{"source":"iana","compressible":true},"application/vnd.hyperdrive+json":{"source":"iana","compressible":true},"application/vnd.hzn-3d-crossword":{"source":"iana"},"application/vnd.ibm.afplinedata":{"source":"iana"},"application/vnd.ibm.electronic-media":{"source":"iana"},"application/vnd.ibm.minipay":{"source":"iana","extensions":["mpy"]},"application/vnd.ibm.modcap":{"source":"iana","extensions":["afp","listafp","list3820"]},"application/vnd.ibm.rights-management":{"source":"iana","extensions":["irm"]},"application/vnd.ibm.secure-container":{"source":"iana","extensions":["sc"]},"application/vnd.iccprofile":{"source":"iana","extensions":["icc","icm"]},"application/vnd.ieee.1905":{"source":"iana"},"application/vnd.igloader":{"source":"iana","extensions":["igl"]},"application/vnd.imagemeter.folder+zip":{"source":"iana","compressible":false},"application/vnd.imagemeter.image+zip":{"source":"iana","compressible":false},"application/vnd.immervision-ivp":{"source":"iana","extensions":["ivp"]},"application/vnd.immervision-ivu":{"source":"iana","extensions":["ivu"]},"application/vnd.ims.imsccv1p1":{"source":"iana"},"application/vnd.ims.imsccv1p2":{"source":"iana"},"application/vnd.ims.imsccv1p3":{"source":"iana"},"application/vnd.ims.lis.v2.result+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolconsumerprofile+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolproxy.id+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings+json":{"source":"iana","compressible":true},"application/vnd.ims.lti.v2.toolsettings.simple+json":{"source":"iana","compressible":true},"application/vnd.informedcontrol.rms+xml":{"source":"iana","compressible":true},"application/vnd.informix-visionary":{"source":"iana"},"application/vnd.infotech.project":{"source":"iana"},"application/vnd.infotech.project+xml":{"source":"iana","compressible":true},"application/vnd.innopath.wamp.notification":{"source":"iana"},"application/vnd.insors.igm":{"source":"iana","extensions":["igm"]},"application/vnd.intercon.formnet":{"source":"iana","extensions":["xpw","xpx"]},"application/vnd.intergeo":{"source":"iana","extensions":["i2g"]},"application/vnd.intertrust.digibox":{"source":"iana"},"application/vnd.intertrust.nncp":{"source":"iana"},"application/vnd.intu.qbo":{"source":"iana","extensions":["qbo"]},"application/vnd.intu.qfx":{"source":"iana","extensions":["qfx"]},"application/vnd.iptc.g2.catalogitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.conceptitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.knowledgeitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.newsmessage+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.packageitem+xml":{"source":"iana","compressible":true},"application/vnd.iptc.g2.planningitem+xml":{"source":"iana","compressible":true},"application/vnd.ipunplugged.rcprofile":{"source":"iana","extensions":["rcprofile"]},"application/vnd.irepository.package+xml":{"source":"iana","compressible":true,"extensions":["irp"]},"application/vnd.is-xpr":{"source":"iana","extensions":["xpr"]},"application/vnd.isac.fcs":{"source":"iana","extensions":["fcs"]},"application/vnd.iso11783-10+zip":{"source":"iana","compressible":false},"application/vnd.jam":{"source":"iana","extensions":["jam"]},"application/vnd.japannet-directory-service":{"source":"iana"},"application/vnd.japannet-jpnstore-wakeup":{"source":"iana"},"application/vnd.japannet-payment-wakeup":{"source":"iana"},"application/vnd.japannet-registration":{"source":"iana"},"application/vnd.japannet-registration-wakeup":{"source":"iana"},"application/vnd.japannet-setstore-wakeup":{"source":"iana"},"application/vnd.japannet-verification":{"source":"iana"},"application/vnd.japannet-verification-wakeup":{"source":"iana"},"application/vnd.jcp.javame.midlet-rms":{"source":"iana","extensions":["rms"]},"application/vnd.jisp":{"source":"iana","extensions":["jisp"]},"application/vnd.joost.joda-archive":{"source":"iana","extensions":["joda"]},"application/vnd.jsk.isdn-ngn":{"source":"iana"},"application/vnd.kahootz":{"source":"iana","extensions":["ktz","ktr"]},"application/vnd.kde.karbon":{"source":"iana","extensions":["karbon"]},"application/vnd.kde.kchart":{"source":"iana","extensions":["chrt"]},"application/vnd.kde.kformula":{"source":"iana","extensions":["kfo"]},"application/vnd.kde.kivio":{"source":"iana","extensions":["flw"]},"application/vnd.kde.kontour":{"source":"iana","extensions":["kon"]},"application/vnd.kde.kpresenter":{"source":"iana","extensions":["kpr","kpt"]},"application/vnd.kde.kspread":{"source":"iana","extensions":["ksp"]},"application/vnd.kde.kword":{"source":"iana","extensions":["kwd","kwt"]},"application/vnd.kenameaapp":{"source":"iana","extensions":["htke"]},"application/vnd.kidspiration":{"source":"iana","extensions":["kia"]},"application/vnd.kinar":{"source":"iana","extensions":["kne","knp"]},"application/vnd.koan":{"source":"iana","extensions":["skp","skd","skt","skm"]},"application/vnd.kodak-descriptor":{"source":"iana","extensions":["sse"]},"application/vnd.las":{"source":"iana"},"application/vnd.las.las+json":{"source":"iana","compressible":true},"application/vnd.las.las+xml":{"source":"iana","compressible":true,"extensions":["lasxml"]},"application/vnd.laszip":{"source":"iana"},"application/vnd.leap+json":{"source":"iana","compressible":true},"application/vnd.liberty-request+xml":{"source":"iana","compressible":true},"application/vnd.llamagraphics.life-balance.desktop":{"source":"iana","extensions":["lbd"]},"application/vnd.llamagraphics.life-balance.exchange+xml":{"source":"iana","compressible":true,"extensions":["lbe"]},"application/vnd.logipipe.circuit+zip":{"source":"iana","compressible":false},"application/vnd.loom":{"source":"iana"},"application/vnd.lotus-1-2-3":{"source":"iana","extensions":["123"]},"application/vnd.lotus-approach":{"source":"iana","extensions":["apr"]},"application/vnd.lotus-freelance":{"source":"iana","extensions":["pre"]},"application/vnd.lotus-notes":{"source":"iana","extensions":["nsf"]},"application/vnd.lotus-organizer":{"source":"iana","extensions":["org"]},"application/vnd.lotus-screencam":{"source":"iana","extensions":["scm"]},"application/vnd.lotus-wordpro":{"source":"iana","extensions":["lwp"]},"application/vnd.macports.portpkg":{"source":"iana","extensions":["portpkg"]},"application/vnd.mapbox-vector-tile":{"source":"iana","extensions":["mvt"]},"application/vnd.marlin.drm.actiontoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.conftoken+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.license+xml":{"source":"iana","compressible":true},"application/vnd.marlin.drm.mdcf":{"source":"iana"},"application/vnd.mason+json":{"source":"iana","compressible":true},"application/vnd.maxar.archive.3tz+zip":{"source":"iana","compressible":false},"application/vnd.maxmind.maxmind-db":{"source":"iana"},"application/vnd.mcd":{"source":"iana","extensions":["mcd"]},"application/vnd.medcalcdata":{"source":"iana","extensions":["mc1"]},"application/vnd.mediastation.cdkey":{"source":"iana","extensions":["cdkey"]},"application/vnd.meridian-slingshot":{"source":"iana"},"application/vnd.mfer":{"source":"iana","extensions":["mwf"]},"application/vnd.mfmp":{"source":"iana","extensions":["mfm"]},"application/vnd.micro+json":{"source":"iana","compressible":true},"application/vnd.micrografx.flo":{"source":"iana","extensions":["flo"]},"application/vnd.micrografx.igx":{"source":"iana","extensions":["igx"]},"application/vnd.microsoft.portable-executable":{"source":"iana"},"application/vnd.microsoft.windows.thumbnail-cache":{"source":"iana"},"application/vnd.miele+json":{"source":"iana","compressible":true},"application/vnd.mif":{"source":"iana","extensions":["mif"]},"application/vnd.minisoft-hp3000-save":{"source":"iana"},"application/vnd.mitsubishi.misty-guard.trustweb":{"source":"iana"},"application/vnd.mobius.daf":{"source":"iana","extensions":["daf"]},"application/vnd.mobius.dis":{"source":"iana","extensions":["dis"]},"application/vnd.mobius.mbk":{"source":"iana","extensions":["mbk"]},"application/vnd.mobius.mqy":{"source":"iana","extensions":["mqy"]},"application/vnd.mobius.msl":{"source":"iana","extensions":["msl"]},"application/vnd.mobius.plc":{"source":"iana","extensions":["plc"]},"application/vnd.mobius.txf":{"source":"iana","extensions":["txf"]},"application/vnd.mophun.application":{"source":"iana","extensions":["mpn"]},"application/vnd.mophun.certificate":{"source":"iana","extensions":["mpc"]},"application/vnd.motorola.flexsuite":{"source":"iana"},"application/vnd.motorola.flexsuite.adsi":{"source":"iana"},"application/vnd.motorola.flexsuite.fis":{"source":"iana"},"application/vnd.motorola.flexsuite.gotap":{"source":"iana"},"application/vnd.motorola.flexsuite.kmr":{"source":"iana"},"application/vnd.motorola.flexsuite.ttc":{"source":"iana"},"application/vnd.motorola.flexsuite.wem":{"source":"iana"},"application/vnd.motorola.iprm":{"source":"iana"},"application/vnd.mozilla.xul+xml":{"source":"iana","compressible":true,"extensions":["xul"]},"application/vnd.ms-3mfdocument":{"source":"iana"},"application/vnd.ms-artgalry":{"source":"iana","extensions":["cil"]},"application/vnd.ms-asf":{"source":"iana"},"application/vnd.ms-cab-compressed":{"source":"iana","extensions":["cab"]},"application/vnd.ms-color.iccprofile":{"source":"apache"},"application/vnd.ms-excel":{"source":"iana","compressible":false,"extensions":["xls","xlm","xla","xlc","xlt","xlw"]},"application/vnd.ms-excel.addin.macroenabled.12":{"source":"iana","extensions":["xlam"]},"application/vnd.ms-excel.sheet.binary.macroenabled.12":{"source":"iana","extensions":["xlsb"]},"application/vnd.ms-excel.sheet.macroenabled.12":{"source":"iana","extensions":["xlsm"]},"application/vnd.ms-excel.template.macroenabled.12":{"source":"iana","extensions":["xltm"]},"application/vnd.ms-fontobject":{"source":"iana","compressible":true,"extensions":["eot"]},"application/vnd.ms-htmlhelp":{"source":"iana","extensions":["chm"]},"application/vnd.ms-ims":{"source":"iana","extensions":["ims"]},"application/vnd.ms-lrm":{"source":"iana","extensions":["lrm"]},"application/vnd.ms-office.activex+xml":{"source":"iana","compressible":true},"application/vnd.ms-officetheme":{"source":"iana","extensions":["thmx"]},"application/vnd.ms-opentype":{"source":"apache","compressible":true},"application/vnd.ms-outlook":{"compressible":false,"extensions":["msg"]},"application/vnd.ms-package.obfuscated-opentype":{"source":"apache"},"application/vnd.ms-pki.seccat":{"source":"apache","extensions":["cat"]},"application/vnd.ms-pki.stl":{"source":"apache","extensions":["stl"]},"application/vnd.ms-playready.initiator+xml":{"source":"iana","compressible":true},"application/vnd.ms-powerpoint":{"source":"iana","compressible":false,"extensions":["ppt","pps","pot"]},"application/vnd.ms-powerpoint.addin.macroenabled.12":{"source":"iana","extensions":["ppam"]},"application/vnd.ms-powerpoint.presentation.macroenabled.12":{"source":"iana","extensions":["pptm"]},"application/vnd.ms-powerpoint.slide.macroenabled.12":{"source":"iana","extensions":["sldm"]},"application/vnd.ms-powerpoint.slideshow.macroenabled.12":{"source":"iana","extensions":["ppsm"]},"application/vnd.ms-powerpoint.template.macroenabled.12":{"source":"iana","extensions":["potm"]},"application/vnd.ms-printdevicecapabilities+xml":{"source":"iana","compressible":true},"application/vnd.ms-printing.printticket+xml":{"source":"apache","compressible":true},"application/vnd.ms-printschematicket+xml":{"source":"iana","compressible":true},"application/vnd.ms-project":{"source":"iana","extensions":["mpp","mpt"]},"application/vnd.ms-tnef":{"source":"iana"},"application/vnd.ms-windows.devicepairing":{"source":"iana"},"application/vnd.ms-windows.nwprinting.oob":{"source":"iana"},"application/vnd.ms-windows.printerpairing":{"source":"iana"},"application/vnd.ms-windows.wsd.oob":{"source":"iana"},"application/vnd.ms-wmdrm.lic-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.lic-resp":{"source":"iana"},"application/vnd.ms-wmdrm.meter-chlg-req":{"source":"iana"},"application/vnd.ms-wmdrm.meter-resp":{"source":"iana"},"application/vnd.ms-word.document.macroenabled.12":{"source":"iana","extensions":["docm"]},"application/vnd.ms-word.template.macroenabled.12":{"source":"iana","extensions":["dotm"]},"application/vnd.ms-works":{"source":"iana","extensions":["wps","wks","wcm","wdb"]},"application/vnd.ms-wpl":{"source":"iana","extensions":["wpl"]},"application/vnd.ms-xpsdocument":{"source":"iana","compressible":false,"extensions":["xps"]},"application/vnd.msa-disk-image":{"source":"iana"},"application/vnd.mseq":{"source":"iana","extensions":["mseq"]},"application/vnd.msign":{"source":"iana"},"application/vnd.multiad.creator":{"source":"iana"},"application/vnd.multiad.creator.cif":{"source":"iana"},"application/vnd.music-niff":{"source":"iana"},"application/vnd.musician":{"source":"iana","extensions":["mus"]},"application/vnd.muvee.style":{"source":"iana","extensions":["msty"]},"application/vnd.mynfc":{"source":"iana","extensions":["taglet"]},"application/vnd.nacamar.ybrid+json":{"source":"iana","compressible":true},"application/vnd.ncd.control":{"source":"iana"},"application/vnd.ncd.reference":{"source":"iana"},"application/vnd.nearst.inv+json":{"source":"iana","compressible":true},"application/vnd.nebumind.line":{"source":"iana"},"application/vnd.nervana":{"source":"iana"},"application/vnd.netfpx":{"source":"iana"},"application/vnd.neurolanguage.nlu":{"source":"iana","extensions":["nlu"]},"application/vnd.nimn":{"source":"iana"},"application/vnd.nintendo.nitro.rom":{"source":"iana"},"application/vnd.nintendo.snes.rom":{"source":"iana"},"application/vnd.nitf":{"source":"iana","extensions":["ntf","nitf"]},"application/vnd.noblenet-directory":{"source":"iana","extensions":["nnd"]},"application/vnd.noblenet-sealer":{"source":"iana","extensions":["nns"]},"application/vnd.noblenet-web":{"source":"iana","extensions":["nnw"]},"application/vnd.nokia.catalogs":{"source":"iana"},"application/vnd.nokia.conml+wbxml":{"source":"iana"},"application/vnd.nokia.conml+xml":{"source":"iana","compressible":true},"application/vnd.nokia.iptv.config+xml":{"source":"iana","compressible":true},"application/vnd.nokia.isds-radio-presets":{"source":"iana"},"application/vnd.nokia.landmark+wbxml":{"source":"iana"},"application/vnd.nokia.landmark+xml":{"source":"iana","compressible":true},"application/vnd.nokia.landmarkcollection+xml":{"source":"iana","compressible":true},"application/vnd.nokia.n-gage.ac+xml":{"source":"iana","compressible":true,"extensions":["ac"]},"application/vnd.nokia.n-gage.data":{"source":"iana","extensions":["ngdat"]},"application/vnd.nokia.n-gage.symbian.install":{"source":"iana","extensions":["n-gage"]},"application/vnd.nokia.ncd":{"source":"iana"},"application/vnd.nokia.pcd+wbxml":{"source":"iana"},"application/vnd.nokia.pcd+xml":{"source":"iana","compressible":true},"application/vnd.nokia.radio-preset":{"source":"iana","extensions":["rpst"]},"application/vnd.nokia.radio-presets":{"source":"iana","extensions":["rpss"]},"application/vnd.novadigm.edm":{"source":"iana","extensions":["edm"]},"application/vnd.novadigm.edx":{"source":"iana","extensions":["edx"]},"application/vnd.novadigm.ext":{"source":"iana","extensions":["ext"]},"application/vnd.ntt-local.content-share":{"source":"iana"},"application/vnd.ntt-local.file-transfer":{"source":"iana"},"application/vnd.ntt-local.ogw_remote-access":{"source":"iana"},"application/vnd.ntt-local.sip-ta_remote":{"source":"iana"},"application/vnd.ntt-local.sip-ta_tcp_stream":{"source":"iana"},"application/vnd.oasis.opendocument.chart":{"source":"iana","extensions":["odc"]},"application/vnd.oasis.opendocument.chart-template":{"source":"iana","extensions":["otc"]},"application/vnd.oasis.opendocument.database":{"source":"iana","extensions":["odb"]},"application/vnd.oasis.opendocument.formula":{"source":"iana","extensions":["odf"]},"application/vnd.oasis.opendocument.formula-template":{"source":"iana","extensions":["odft"]},"application/vnd.oasis.opendocument.graphics":{"source":"iana","compressible":false,"extensions":["odg"]},"application/vnd.oasis.opendocument.graphics-template":{"source":"iana","extensions":["otg"]},"application/vnd.oasis.opendocument.image":{"source":"iana","extensions":["odi"]},"application/vnd.oasis.opendocument.image-template":{"source":"iana","extensions":["oti"]},"application/vnd.oasis.opendocument.presentation":{"source":"iana","compressible":false,"extensions":["odp"]},"application/vnd.oasis.opendocument.presentation-template":{"source":"iana","extensions":["otp"]},"application/vnd.oasis.opendocument.spreadsheet":{"source":"iana","compressible":false,"extensions":["ods"]},"application/vnd.oasis.opendocument.spreadsheet-template":{"source":"iana","extensions":["ots"]},"application/vnd.oasis.opendocument.text":{"source":"iana","compressible":false,"extensions":["odt"]},"application/vnd.oasis.opendocument.text-master":{"source":"iana","extensions":["odm"]},"application/vnd.oasis.opendocument.text-template":{"source":"iana","extensions":["ott"]},"application/vnd.oasis.opendocument.text-web":{"source":"iana","extensions":["oth"]},"application/vnd.obn":{"source":"iana"},"application/vnd.ocf+cbor":{"source":"iana"},"application/vnd.oci.image.manifest.v1+json":{"source":"iana","compressible":true},"application/vnd.oftn.l10n+json":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessdownload+xml":{"source":"iana","compressible":true},"application/vnd.oipf.contentaccessstreaming+xml":{"source":"iana","compressible":true},"application/vnd.oipf.cspg-hexbinary":{"source":"iana"},"application/vnd.oipf.dae.svg+xml":{"source":"iana","compressible":true},"application/vnd.oipf.dae.xhtml+xml":{"source":"iana","compressible":true},"application/vnd.oipf.mippvcontrolmessage+xml":{"source":"iana","compressible":true},"application/vnd.oipf.pae.gem":{"source":"iana"},"application/vnd.oipf.spdiscovery+xml":{"source":"iana","compressible":true},"application/vnd.oipf.spdlist+xml":{"source":"iana","compressible":true},"application/vnd.oipf.ueprofile+xml":{"source":"iana","compressible":true},"application/vnd.oipf.userprofile+xml":{"source":"iana","compressible":true},"application/vnd.olpc-sugar":{"source":"iana","extensions":["xo"]},"application/vnd.oma-scws-config":{"source":"iana"},"application/vnd.oma-scws-http-request":{"source":"iana"},"application/vnd.oma-scws-http-response":{"source":"iana"},"application/vnd.oma.bcast.associated-procedure-parameter+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.drm-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.imd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.ltkm":{"source":"iana"},"application/vnd.oma.bcast.notification+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.provisioningtrigger":{"source":"iana"},"application/vnd.oma.bcast.sgboot":{"source":"iana"},"application/vnd.oma.bcast.sgdd+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sgdu":{"source":"iana"},"application/vnd.oma.bcast.simple-symbol-container":{"source":"iana"},"application/vnd.oma.bcast.smartcard-trigger+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.sprov+xml":{"source":"iana","compressible":true},"application/vnd.oma.bcast.stkm":{"source":"iana"},"application/vnd.oma.cab-address-book+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-feature-handler+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-pcc+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-subs-invite+xml":{"source":"iana","compressible":true},"application/vnd.oma.cab-user-prefs+xml":{"source":"iana","compressible":true},"application/vnd.oma.dcd":{"source":"iana"},"application/vnd.oma.dcdc":{"source":"iana"},"application/vnd.oma.dd2+xml":{"source":"iana","compressible":true,"extensions":["dd2"]},"application/vnd.oma.drm.risd+xml":{"source":"iana","compressible":true},"application/vnd.oma.group-usage-list+xml":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+cbor":{"source":"iana"},"application/vnd.oma.lwm2m+json":{"source":"iana","compressible":true},"application/vnd.oma.lwm2m+tlv":{"source":"iana"},"application/vnd.oma.pal+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.detailed-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.final-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.groups+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.invocation-descriptor+xml":{"source":"iana","compressible":true},"application/vnd.oma.poc.optimized-progress-report+xml":{"source":"iana","compressible":true},"application/vnd.oma.push":{"source":"iana"},"application/vnd.oma.scidm.messages+xml":{"source":"iana","compressible":true},"application/vnd.oma.xcap-directory+xml":{"source":"iana","compressible":true},"application/vnd.omads-email+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-file+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omads-folder+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.omaloc-supl-init":{"source":"iana"},"application/vnd.onepager":{"source":"iana"},"application/vnd.onepagertamp":{"source":"iana"},"application/vnd.onepagertamx":{"source":"iana"},"application/vnd.onepagertat":{"source":"iana"},"application/vnd.onepagertatp":{"source":"iana"},"application/vnd.onepagertatx":{"source":"iana"},"application/vnd.openblox.game+xml":{"source":"iana","compressible":true,"extensions":["obgx"]},"application/vnd.openblox.game-binary":{"source":"iana"},"application/vnd.openeye.oeb":{"source":"iana"},"application/vnd.openofficeorg.extension":{"source":"apache","extensions":["oxt"]},"application/vnd.openstreetmap.data+xml":{"source":"iana","compressible":true,"extensions":["osm"]},"application/vnd.opentimestamps.ots":{"source":"iana"},"application/vnd.openxmlformats-officedocument.custom-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.customxmlproperties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawing+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chart+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.chartshapes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramcolors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramdata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramlayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.drawingml.diagramstyle+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.extended-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.commentauthors+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.handoutmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesmaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.notesslide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presentation":{"source":"iana","compressible":false,"extensions":["pptx"]},"application/vnd.openxmlformats-officedocument.presentationml.presentation.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.presprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slide":{"source":"iana","extensions":["sldx"]},"application/vnd.openxmlformats-officedocument.presentationml.slide+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidelayout+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slidemaster+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideshow":{"source":"iana","extensions":["ppsx"]},"application/vnd.openxmlformats-officedocument.presentationml.slideshow.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.slideupdateinfo+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tablestyles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.tags+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.template":{"source":"iana","extensions":["potx"]},"application/vnd.openxmlformats-officedocument.presentationml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.presentationml.viewprops+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.calcchain+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.chartsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.connections+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.dialogsheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.externallink+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcachedefinition+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivotcacherecords+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.pivottable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.querytable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionheaders+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.revisionlog+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sharedstrings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet":{"source":"iana","compressible":false,"extensions":["xlsx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.sheetmetadata+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.table+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.tablesinglecells+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.template":{"source":"iana","extensions":["xltx"]},"application/vnd.openxmlformats-officedocument.spreadsheetml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.usernames+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.volatiledependencies+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.spreadsheetml.worksheet+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.theme+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.themeoverride+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.vmldrawing":{"source":"iana"},"application/vnd.openxmlformats-officedocument.wordprocessingml.comments+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document":{"source":"iana","compressible":false,"extensions":["docx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.glossary+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.endnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.fonttable+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footer+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.footnotes+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.numbering+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.settings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.styles+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.template":{"source":"iana","extensions":["dotx"]},"application/vnd.openxmlformats-officedocument.wordprocessingml.template.main+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-officedocument.wordprocessingml.websettings+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.core-properties+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.digital-signature-xmlsignature+xml":{"source":"iana","compressible":true},"application/vnd.openxmlformats-package.relationships+xml":{"source":"iana","compressible":true},"application/vnd.oracle.resource+json":{"source":"iana","compressible":true},"application/vnd.orange.indata":{"source":"iana"},"application/vnd.osa.netdeploy":{"source":"iana"},"application/vnd.osgeo.mapguide.package":{"source":"iana","extensions":["mgp"]},"application/vnd.osgi.bundle":{"source":"iana"},"application/vnd.osgi.dp":{"source":"iana","extensions":["dp"]},"application/vnd.osgi.subsystem":{"source":"iana","extensions":["esa"]},"application/vnd.otps.ct-kip+xml":{"source":"iana","compressible":true},"application/vnd.oxli.countgraph":{"source":"iana"},"application/vnd.pagerduty+json":{"source":"iana","compressible":true},"application/vnd.palm":{"source":"iana","extensions":["pdb","pqa","oprc"]},"application/vnd.panoply":{"source":"iana"},"application/vnd.paos.xml":{"source":"iana"},"application/vnd.patentdive":{"source":"iana"},"application/vnd.patientecommsdoc":{"source":"iana"},"application/vnd.pawaafile":{"source":"iana","extensions":["paw"]},"application/vnd.pcos":{"source":"iana"},"application/vnd.pg.format":{"source":"iana","extensions":["str"]},"application/vnd.pg.osasli":{"source":"iana","extensions":["ei6"]},"application/vnd.piaccess.application-licence":{"source":"iana"},"application/vnd.picsel":{"source":"iana","extensions":["efif"]},"application/vnd.pmi.widget":{"source":"iana","extensions":["wg"]},"application/vnd.poc.group-advertisement+xml":{"source":"iana","compressible":true},"application/vnd.pocketlearn":{"source":"iana","extensions":["plf"]},"application/vnd.powerbuilder6":{"source":"iana","extensions":["pbd"]},"application/vnd.powerbuilder6-s":{"source":"iana"},"application/vnd.powerbuilder7":{"source":"iana"},"application/vnd.powerbuilder7-s":{"source":"iana"},"application/vnd.powerbuilder75":{"source":"iana"},"application/vnd.powerbuilder75-s":{"source":"iana"},"application/vnd.preminet":{"source":"iana"},"application/vnd.previewsystems.box":{"source":"iana","extensions":["box"]},"application/vnd.proteus.magazine":{"source":"iana","extensions":["mgz"]},"application/vnd.psfs":{"source":"iana"},"application/vnd.publishare-delta-tree":{"source":"iana","extensions":["qps"]},"application/vnd.pvi.ptid1":{"source":"iana","extensions":["ptid"]},"application/vnd.pwg-multiplexed":{"source":"iana"},"application/vnd.pwg-xhtml-print+xml":{"source":"iana","compressible":true},"application/vnd.qualcomm.brew-app-res":{"source":"iana"},"application/vnd.quarantainenet":{"source":"iana"},"application/vnd.quark.quarkxpress":{"source":"iana","extensions":["qxd","qxt","qwd","qwt","qxl","qxb"]},"application/vnd.quobject-quoxdocument":{"source":"iana"},"application/vnd.radisys.moml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-conn+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-audit-stream+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-conf+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-base+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-detect+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-fax-sendrecv+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-group+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-speech+xml":{"source":"iana","compressible":true},"application/vnd.radisys.msml-dialog-transform+xml":{"source":"iana","compressible":true},"application/vnd.rainstor.data":{"source":"iana"},"application/vnd.rapid":{"source":"iana"},"application/vnd.rar":{"source":"iana","extensions":["rar"]},"application/vnd.realvnc.bed":{"source":"iana","extensions":["bed"]},"application/vnd.recordare.musicxml":{"source":"iana","extensions":["mxl"]},"application/vnd.recordare.musicxml+xml":{"source":"iana","compressible":true,"extensions":["musicxml"]},"application/vnd.renlearn.rlprint":{"source":"iana"},"application/vnd.resilient.logic":{"source":"iana"},"application/vnd.restful+json":{"source":"iana","compressible":true},"application/vnd.rig.cryptonote":{"source":"iana","extensions":["cryptonote"]},"application/vnd.rim.cod":{"source":"apache","extensions":["cod"]},"application/vnd.rn-realmedia":{"source":"apache","extensions":["rm"]},"application/vnd.rn-realmedia-vbr":{"source":"apache","extensions":["rmvb"]},"application/vnd.route66.link66+xml":{"source":"iana","compressible":true,"extensions":["link66"]},"application/vnd.rs-274x":{"source":"iana"},"application/vnd.ruckus.download":{"source":"iana"},"application/vnd.s3sms":{"source":"iana"},"application/vnd.sailingtracker.track":{"source":"iana","extensions":["st"]},"application/vnd.sar":{"source":"iana"},"application/vnd.sbm.cid":{"source":"iana"},"application/vnd.sbm.mid2":{"source":"iana"},"application/vnd.scribus":{"source":"iana"},"application/vnd.sealed.3df":{"source":"iana"},"application/vnd.sealed.csf":{"source":"iana"},"application/vnd.sealed.doc":{"source":"iana"},"application/vnd.sealed.eml":{"source":"iana"},"application/vnd.sealed.mht":{"source":"iana"},"application/vnd.sealed.net":{"source":"iana"},"application/vnd.sealed.ppt":{"source":"iana"},"application/vnd.sealed.tiff":{"source":"iana"},"application/vnd.sealed.xls":{"source":"iana"},"application/vnd.sealedmedia.softseal.html":{"source":"iana"},"application/vnd.sealedmedia.softseal.pdf":{"source":"iana"},"application/vnd.seemail":{"source":"iana","extensions":["see"]},"application/vnd.seis+json":{"source":"iana","compressible":true},"application/vnd.sema":{"source":"iana","extensions":["sema"]},"application/vnd.semd":{"source":"iana","extensions":["semd"]},"application/vnd.semf":{"source":"iana","extensions":["semf"]},"application/vnd.shade-save-file":{"source":"iana"},"application/vnd.shana.informed.formdata":{"source":"iana","extensions":["ifm"]},"application/vnd.shana.informed.formtemplate":{"source":"iana","extensions":["itp"]},"application/vnd.shana.informed.interchange":{"source":"iana","extensions":["iif"]},"application/vnd.shana.informed.package":{"source":"iana","extensions":["ipk"]},"application/vnd.shootproof+json":{"source":"iana","compressible":true},"application/vnd.shopkick+json":{"source":"iana","compressible":true},"application/vnd.shp":{"source":"iana"},"application/vnd.shx":{"source":"iana"},"application/vnd.sigrok.session":{"source":"iana"},"application/vnd.simtech-mindmapper":{"source":"iana","extensions":["twd","twds"]},"application/vnd.siren+json":{"source":"iana","compressible":true},"application/vnd.smaf":{"source":"iana","extensions":["mmf"]},"application/vnd.smart.notebook":{"source":"iana"},"application/vnd.smart.teacher":{"source":"iana","extensions":["teacher"]},"application/vnd.snesdev-page-table":{"source":"iana"},"application/vnd.software602.filler.form+xml":{"source":"iana","compressible":true,"extensions":["fo"]},"application/vnd.software602.filler.form-xml-zip":{"source":"iana"},"application/vnd.solent.sdkm+xml":{"source":"iana","compressible":true,"extensions":["sdkm","sdkd"]},"application/vnd.spotfire.dxp":{"source":"iana","extensions":["dxp"]},"application/vnd.spotfire.sfs":{"source":"iana","extensions":["sfs"]},"application/vnd.sqlite3":{"source":"iana"},"application/vnd.sss-cod":{"source":"iana"},"application/vnd.sss-dtf":{"source":"iana"},"application/vnd.sss-ntf":{"source":"iana"},"application/vnd.stardivision.calc":{"source":"apache","extensions":["sdc"]},"application/vnd.stardivision.draw":{"source":"apache","extensions":["sda"]},"application/vnd.stardivision.impress":{"source":"apache","extensions":["sdd"]},"application/vnd.stardivision.math":{"source":"apache","extensions":["smf"]},"application/vnd.stardivision.writer":{"source":"apache","extensions":["sdw","vor"]},"application/vnd.stardivision.writer-global":{"source":"apache","extensions":["sgl"]},"application/vnd.stepmania.package":{"source":"iana","extensions":["smzip"]},"application/vnd.stepmania.stepchart":{"source":"iana","extensions":["sm"]},"application/vnd.street-stream":{"source":"iana"},"application/vnd.sun.wadl+xml":{"source":"iana","compressible":true,"extensions":["wadl"]},"application/vnd.sun.xml.calc":{"source":"apache","extensions":["sxc"]},"application/vnd.sun.xml.calc.template":{"source":"apache","extensions":["stc"]},"application/vnd.sun.xml.draw":{"source":"apache","extensions":["sxd"]},"application/vnd.sun.xml.draw.template":{"source":"apache","extensions":["std"]},"application/vnd.sun.xml.impress":{"source":"apache","extensions":["sxi"]},"application/vnd.sun.xml.impress.template":{"source":"apache","extensions":["sti"]},"application/vnd.sun.xml.math":{"source":"apache","extensions":["sxm"]},"application/vnd.sun.xml.writer":{"source":"apache","extensions":["sxw"]},"application/vnd.sun.xml.writer.global":{"source":"apache","extensions":["sxg"]},"application/vnd.sun.xml.writer.template":{"source":"apache","extensions":["stw"]},"application/vnd.sus-calendar":{"source":"iana","extensions":["sus","susp"]},"application/vnd.svd":{"source":"iana","extensions":["svd"]},"application/vnd.swiftview-ics":{"source":"iana"},"application/vnd.sycle+xml":{"source":"iana","compressible":true},"application/vnd.syft+json":{"source":"iana","compressible":true},"application/vnd.symbian.install":{"source":"apache","extensions":["sis","sisx"]},"application/vnd.syncml+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xsm"]},"application/vnd.syncml.dm+wbxml":{"source":"iana","charset":"UTF-8","extensions":["bdm"]},"application/vnd.syncml.dm+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["xdm"]},"application/vnd.syncml.dm.notification":{"source":"iana"},"application/vnd.syncml.dmddf+wbxml":{"source":"iana"},"application/vnd.syncml.dmddf+xml":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["ddf"]},"application/vnd.syncml.dmtnds+wbxml":{"source":"iana"},"application/vnd.syncml.dmtnds+xml":{"source":"iana","charset":"UTF-8","compressible":true},"application/vnd.syncml.ds.notification":{"source":"iana"},"application/vnd.tableschema+json":{"source":"iana","compressible":true},"application/vnd.tao.intent-module-archive":{"source":"iana","extensions":["tao"]},"application/vnd.tcpdump.pcap":{"source":"iana","extensions":["pcap","cap","dmp"]},"application/vnd.think-cell.ppttc+json":{"source":"iana","compressible":true},"application/vnd.tmd.mediaflex.api+xml":{"source":"iana","compressible":true},"application/vnd.tml":{"source":"iana"},"application/vnd.tmobile-livetv":{"source":"iana","extensions":["tmo"]},"application/vnd.tri.onesource":{"source":"iana"},"application/vnd.trid.tpt":{"source":"iana","extensions":["tpt"]},"application/vnd.triscape.mxs":{"source":"iana","extensions":["mxs"]},"application/vnd.trueapp":{"source":"iana","extensions":["tra"]},"application/vnd.truedoc":{"source":"iana"},"application/vnd.ubisoft.webplayer":{"source":"iana"},"application/vnd.ufdl":{"source":"iana","extensions":["ufd","ufdl"]},"application/vnd.uiq.theme":{"source":"iana","extensions":["utz"]},"application/vnd.umajin":{"source":"iana","extensions":["umj"]},"application/vnd.unity":{"source":"iana","extensions":["unityweb"]},"application/vnd.uoml+xml":{"source":"iana","compressible":true,"extensions":["uoml"]},"application/vnd.uplanet.alert":{"source":"iana"},"application/vnd.uplanet.alert-wbxml":{"source":"iana"},"application/vnd.uplanet.bearer-choice":{"source":"iana"},"application/vnd.uplanet.bearer-choice-wbxml":{"source":"iana"},"application/vnd.uplanet.cacheop":{"source":"iana"},"application/vnd.uplanet.cacheop-wbxml":{"source":"iana"},"application/vnd.uplanet.channel":{"source":"iana"},"application/vnd.uplanet.channel-wbxml":{"source":"iana"},"application/vnd.uplanet.list":{"source":"iana"},"application/vnd.uplanet.list-wbxml":{"source":"iana"},"application/vnd.uplanet.listcmd":{"source":"iana"},"application/vnd.uplanet.listcmd-wbxml":{"source":"iana"},"application/vnd.uplanet.signal":{"source":"iana"},"application/vnd.uri-map":{"source":"iana"},"application/vnd.valve.source.material":{"source":"iana"},"application/vnd.vcx":{"source":"iana","extensions":["vcx"]},"application/vnd.vd-study":{"source":"iana"},"application/vnd.vectorworks":{"source":"iana"},"application/vnd.vel+json":{"source":"iana","compressible":true},"application/vnd.verimatrix.vcas":{"source":"iana"},"application/vnd.veritone.aion+json":{"source":"iana","compressible":true},"application/vnd.veryant.thin":{"source":"iana"},"application/vnd.ves.encrypted":{"source":"iana"},"application/vnd.vidsoft.vidconference":{"source":"iana"},"application/vnd.visio":{"source":"iana","extensions":["vsd","vst","vss","vsw"]},"application/vnd.visionary":{"source":"iana","extensions":["vis"]},"application/vnd.vividence.scriptfile":{"source":"iana"},"application/vnd.vsf":{"source":"iana","extensions":["vsf"]},"application/vnd.wap.sic":{"source":"iana"},"application/vnd.wap.slc":{"source":"iana"},"application/vnd.wap.wbxml":{"source":"iana","charset":"UTF-8","extensions":["wbxml"]},"application/vnd.wap.wmlc":{"source":"iana","extensions":["wmlc"]},"application/vnd.wap.wmlscriptc":{"source":"iana","extensions":["wmlsc"]},"application/vnd.webturbo":{"source":"iana","extensions":["wtb"]},"application/vnd.wfa.dpp":{"source":"iana"},"application/vnd.wfa.p2p":{"source":"iana"},"application/vnd.wfa.wsc":{"source":"iana"},"application/vnd.windows.devicepairing":{"source":"iana"},"application/vnd.wmc":{"source":"iana"},"application/vnd.wmf.bootstrap":{"source":"iana"},"application/vnd.wolfram.mathematica":{"source":"iana"},"application/vnd.wolfram.mathematica.package":{"source":"iana"},"application/vnd.wolfram.player":{"source":"iana","extensions":["nbp"]},"application/vnd.wordperfect":{"source":"iana","extensions":["wpd"]},"application/vnd.wqd":{"source":"iana","extensions":["wqd"]},"application/vnd.wrq-hp3000-labelled":{"source":"iana"},"application/vnd.wt.stf":{"source":"iana","extensions":["stf"]},"application/vnd.wv.csp+wbxml":{"source":"iana"},"application/vnd.wv.csp+xml":{"source":"iana","compressible":true},"application/vnd.wv.ssp+xml":{"source":"iana","compressible":true},"application/vnd.xacml+json":{"source":"iana","compressible":true},"application/vnd.xara":{"source":"iana","extensions":["xar"]},"application/vnd.xfdl":{"source":"iana","extensions":["xfdl"]},"application/vnd.xfdl.webform":{"source":"iana"},"application/vnd.xmi+xml":{"source":"iana","compressible":true},"application/vnd.xmpie.cpkg":{"source":"iana"},"application/vnd.xmpie.dpkg":{"source":"iana"},"application/vnd.xmpie.plan":{"source":"iana"},"application/vnd.xmpie.ppkg":{"source":"iana"},"application/vnd.xmpie.xlim":{"source":"iana"},"application/vnd.yamaha.hv-dic":{"source":"iana","extensions":["hvd"]},"application/vnd.yamaha.hv-script":{"source":"iana","extensions":["hvs"]},"application/vnd.yamaha.hv-voice":{"source":"iana","extensions":["hvp"]},"application/vnd.yamaha.openscoreformat":{"source":"iana","extensions":["osf"]},"application/vnd.yamaha.openscoreformat.osfpvg+xml":{"source":"iana","compressible":true,"extensions":["osfpvg"]},"application/vnd.yamaha.remote-setup":{"source":"iana"},"application/vnd.yamaha.smaf-audio":{"source":"iana","extensions":["saf"]},"application/vnd.yamaha.smaf-phrase":{"source":"iana","extensions":["spf"]},"application/vnd.yamaha.through-ngn":{"source":"iana"},"application/vnd.yamaha.tunnel-udpencap":{"source":"iana"},"application/vnd.yaoweme":{"source":"iana"},"application/vnd.yellowriver-custom-menu":{"source":"iana","extensions":["cmp"]},"application/vnd.youtube.yt":{"source":"iana"},"application/vnd.zul":{"source":"iana","extensions":["zir","zirz"]},"application/vnd.zzazz.deck+xml":{"source":"iana","compressible":true,"extensions":["zaz"]},"application/voicexml+xml":{"source":"iana","compressible":true,"extensions":["vxml"]},"application/voucher-cms+json":{"source":"iana","compressible":true},"application/vq-rtcpxr":{"source":"iana"},"application/wasm":{"source":"iana","compressible":true,"extensions":["wasm"]},"application/watcherinfo+xml":{"source":"iana","compressible":true,"extensions":["wif"]},"application/webpush-options+json":{"source":"iana","compressible":true},"application/whoispp-query":{"source":"iana"},"application/whoispp-response":{"source":"iana"},"application/widget":{"source":"iana","extensions":["wgt"]},"application/winhlp":{"source":"apache","extensions":["hlp"]},"application/wita":{"source":"iana"},"application/wordperfect5.1":{"source":"iana"},"application/wsdl+xml":{"source":"iana","compressible":true,"extensions":["wsdl"]},"application/wspolicy+xml":{"source":"iana","compressible":true,"extensions":["wspolicy"]},"application/x-7z-compressed":{"source":"apache","compressible":false,"extensions":["7z"]},"application/x-abiword":{"source":"apache","extensions":["abw"]},"application/x-ace-compressed":{"source":"apache","extensions":["ace"]},"application/x-amf":{"source":"apache"},"application/x-apple-diskimage":{"source":"apache","extensions":["dmg"]},"application/x-arj":{"compressible":false,"extensions":["arj"]},"application/x-authorware-bin":{"source":"apache","extensions":["aab","x32","u32","vox"]},"application/x-authorware-map":{"source":"apache","extensions":["aam"]},"application/x-authorware-seg":{"source":"apache","extensions":["aas"]},"application/x-bcpio":{"source":"apache","extensions":["bcpio"]},"application/x-bdoc":{"compressible":false,"extensions":["bdoc"]},"application/x-bittorrent":{"source":"apache","extensions":["torrent"]},"application/x-blorb":{"source":"apache","extensions":["blb","blorb"]},"application/x-bzip":{"source":"apache","compressible":false,"extensions":["bz"]},"application/x-bzip2":{"source":"apache","compressible":false,"extensions":["bz2","boz"]},"application/x-cbr":{"source":"apache","extensions":["cbr","cba","cbt","cbz","cb7"]},"application/x-cdlink":{"source":"apache","extensions":["vcd"]},"application/x-cfs-compressed":{"source":"apache","extensions":["cfs"]},"application/x-chat":{"source":"apache","extensions":["chat"]},"application/x-chess-pgn":{"source":"apache","extensions":["pgn"]},"application/x-chrome-extension":{"extensions":["crx"]},"application/x-cocoa":{"source":"nginx","extensions":["cco"]},"application/x-compress":{"source":"apache"},"application/x-conference":{"source":"apache","extensions":["nsc"]},"application/x-cpio":{"source":"apache","extensions":["cpio"]},"application/x-csh":{"source":"apache","extensions":["csh"]},"application/x-deb":{"compressible":false},"application/x-debian-package":{"source":"apache","extensions":["deb","udeb"]},"application/x-dgc-compressed":{"source":"apache","extensions":["dgc"]},"application/x-director":{"source":"apache","extensions":["dir","dcr","dxr","cst","cct","cxt","w3d","fgd","swa"]},"application/x-doom":{"source":"apache","extensions":["wad"]},"application/x-dtbncx+xml":{"source":"apache","compressible":true,"extensions":["ncx"]},"application/x-dtbook+xml":{"source":"apache","compressible":true,"extensions":["dtb"]},"application/x-dtbresource+xml":{"source":"apache","compressible":true,"extensions":["res"]},"application/x-dvi":{"source":"apache","compressible":false,"extensions":["dvi"]},"application/x-envoy":{"source":"apache","extensions":["evy"]},"application/x-eva":{"source":"apache","extensions":["eva"]},"application/x-font-bdf":{"source":"apache","extensions":["bdf"]},"application/x-font-dos":{"source":"apache"},"application/x-font-framemaker":{"source":"apache"},"application/x-font-ghostscript":{"source":"apache","extensions":["gsf"]},"application/x-font-libgrx":{"source":"apache"},"application/x-font-linux-psf":{"source":"apache","extensions":["psf"]},"application/x-font-pcf":{"source":"apache","extensions":["pcf"]},"application/x-font-snf":{"source":"apache","extensions":["snf"]},"application/x-font-speedo":{"source":"apache"},"application/x-font-sunos-news":{"source":"apache"},"application/x-font-type1":{"source":"apache","extensions":["pfa","pfb","pfm","afm"]},"application/x-font-vfont":{"source":"apache"},"application/x-freearc":{"source":"apache","extensions":["arc"]},"application/x-futuresplash":{"source":"apache","extensions":["spl"]},"application/x-gca-compressed":{"source":"apache","extensions":["gca"]},"application/x-glulx":{"source":"apache","extensions":["ulx"]},"application/x-gnumeric":{"source":"apache","extensions":["gnumeric"]},"application/x-gramps-xml":{"source":"apache","extensions":["gramps"]},"application/x-gtar":{"source":"apache","extensions":["gtar"]},"application/x-gzip":{"source":"apache"},"application/x-hdf":{"source":"apache","extensions":["hdf"]},"application/x-httpd-php":{"compressible":true,"extensions":["php"]},"application/x-install-instructions":{"source":"apache","extensions":["install"]},"application/x-iso9660-image":{"source":"apache","extensions":["iso"]},"application/x-iwork-keynote-sffkey":{"extensions":["key"]},"application/x-iwork-numbers-sffnumbers":{"extensions":["numbers"]},"application/x-iwork-pages-sffpages":{"extensions":["pages"]},"application/x-java-archive-diff":{"source":"nginx","extensions":["jardiff"]},"application/x-java-jnlp-file":{"source":"apache","compressible":false,"extensions":["jnlp"]},"application/x-javascript":{"compressible":true},"application/x-keepass2":{"extensions":["kdbx"]},"application/x-latex":{"source":"apache","compressible":false,"extensions":["latex"]},"application/x-lua-bytecode":{"extensions":["luac"]},"application/x-lzh-compressed":{"source":"apache","extensions":["lzh","lha"]},"application/x-makeself":{"source":"nginx","extensions":["run"]},"application/x-mie":{"source":"apache","extensions":["mie"]},"application/x-mobipocket-ebook":{"source":"apache","extensions":["prc","mobi"]},"application/x-mpegurl":{"compressible":false},"application/x-ms-application":{"source":"apache","extensions":["application"]},"application/x-ms-shortcut":{"source":"apache","extensions":["lnk"]},"application/x-ms-wmd":{"source":"apache","extensions":["wmd"]},"application/x-ms-wmz":{"source":"apache","extensions":["wmz"]},"application/x-ms-xbap":{"source":"apache","extensions":["xbap"]},"application/x-msaccess":{"source":"apache","extensions":["mdb"]},"application/x-msbinder":{"source":"apache","extensions":["obd"]},"application/x-mscardfile":{"source":"apache","extensions":["crd"]},"application/x-msclip":{"source":"apache","extensions":["clp"]},"application/x-msdos-program":{"extensions":["exe"]},"application/x-msdownload":{"source":"apache","extensions":["exe","dll","com","bat","msi"]},"application/x-msmediaview":{"source":"apache","extensions":["mvb","m13","m14"]},"application/x-msmetafile":{"source":"apache","extensions":["wmf","wmz","emf","emz"]},"application/x-msmoney":{"source":"apache","extensions":["mny"]},"application/x-mspublisher":{"source":"apache","extensions":["pub"]},"application/x-msschedule":{"source":"apache","extensions":["scd"]},"application/x-msterminal":{"source":"apache","extensions":["trm"]},"application/x-mswrite":{"source":"apache","extensions":["wri"]},"application/x-netcdf":{"source":"apache","extensions":["nc","cdf"]},"application/x-ns-proxy-autoconfig":{"compressible":true,"extensions":["pac"]},"application/x-nzb":{"source":"apache","extensions":["nzb"]},"application/x-perl":{"source":"nginx","extensions":["pl","pm"]},"application/x-pilot":{"source":"nginx","extensions":["prc","pdb"]},"application/x-pkcs12":{"source":"apache","compressible":false,"extensions":["p12","pfx"]},"application/x-pkcs7-certificates":{"source":"apache","extensions":["p7b","spc"]},"application/x-pkcs7-certreqresp":{"source":"apache","extensions":["p7r"]},"application/x-pki-message":{"source":"iana"},"application/x-rar-compressed":{"source":"apache","compressible":false,"extensions":["rar"]},"application/x-redhat-package-manager":{"source":"nginx","extensions":["rpm"]},"application/x-research-info-systems":{"source":"apache","extensions":["ris"]},"application/x-sea":{"source":"nginx","extensions":["sea"]},"application/x-sh":{"source":"apache","compressible":true,"extensions":["sh"]},"application/x-shar":{"source":"apache","extensions":["shar"]},"application/x-shockwave-flash":{"source":"apache","compressible":false,"extensions":["swf"]},"application/x-silverlight-app":{"source":"apache","extensions":["xap"]},"application/x-sql":{"source":"apache","extensions":["sql"]},"application/x-stuffit":{"source":"apache","compressible":false,"extensions":["sit"]},"application/x-stuffitx":{"source":"apache","extensions":["sitx"]},"application/x-subrip":{"source":"apache","extensions":["srt"]},"application/x-sv4cpio":{"source":"apache","extensions":["sv4cpio"]},"application/x-sv4crc":{"source":"apache","extensions":["sv4crc"]},"application/x-t3vm-image":{"source":"apache","extensions":["t3"]},"application/x-tads":{"source":"apache","extensions":["gam"]},"application/x-tar":{"source":"apache","compressible":true,"extensions":["tar"]},"application/x-tcl":{"source":"apache","extensions":["tcl","tk"]},"application/x-tex":{"source":"apache","extensions":["tex"]},"application/x-tex-tfm":{"source":"apache","extensions":["tfm"]},"application/x-texinfo":{"source":"apache","extensions":["texinfo","texi"]},"application/x-tgif":{"source":"apache","extensions":["obj"]},"application/x-ustar":{"source":"apache","extensions":["ustar"]},"application/x-virtualbox-hdd":{"compressible":true,"extensions":["hdd"]},"application/x-virtualbox-ova":{"compressible":true,"extensions":["ova"]},"application/x-virtualbox-ovf":{"compressible":true,"extensions":["ovf"]},"application/x-virtualbox-vbox":{"compressible":true,"extensions":["vbox"]},"application/x-virtualbox-vbox-extpack":{"compressible":false,"extensions":["vbox-extpack"]},"application/x-virtualbox-vdi":{"compressible":true,"extensions":["vdi"]},"application/x-virtualbox-vhd":{"compressible":true,"extensions":["vhd"]},"application/x-virtualbox-vmdk":{"compressible":true,"extensions":["vmdk"]},"application/x-wais-source":{"source":"apache","extensions":["src"]},"application/x-web-app-manifest+json":{"compressible":true,"extensions":["webapp"]},"application/x-www-form-urlencoded":{"source":"iana","compressible":true},"application/x-x509-ca-cert":{"source":"iana","extensions":["der","crt","pem"]},"application/x-x509-ca-ra-cert":{"source":"iana"},"application/x-x509-next-ca-cert":{"source":"iana"},"application/x-xfig":{"source":"apache","extensions":["fig"]},"application/x-xliff+xml":{"source":"apache","compressible":true,"extensions":["xlf"]},"application/x-xpinstall":{"source":"apache","compressible":false,"extensions":["xpi"]},"application/x-xz":{"source":"apache","extensions":["xz"]},"application/x-zmachine":{"source":"apache","extensions":["z1","z2","z3","z4","z5","z6","z7","z8"]},"application/x400-bp":{"source":"iana"},"application/xacml+xml":{"source":"iana","compressible":true},"application/xaml+xml":{"source":"apache","compressible":true,"extensions":["xaml"]},"application/xcap-att+xml":{"source":"iana","compressible":true,"extensions":["xav"]},"application/xcap-caps+xml":{"source":"iana","compressible":true,"extensions":["xca"]},"application/xcap-diff+xml":{"source":"iana","compressible":true,"extensions":["xdf"]},"application/xcap-el+xml":{"source":"iana","compressible":true,"extensions":["xel"]},"application/xcap-error+xml":{"source":"iana","compressible":true},"application/xcap-ns+xml":{"source":"iana","compressible":true,"extensions":["xns"]},"application/xcon-conference-info+xml":{"source":"iana","compressible":true},"application/xcon-conference-info-diff+xml":{"source":"iana","compressible":true},"application/xenc+xml":{"source":"iana","compressible":true,"extensions":["xenc"]},"application/xhtml+xml":{"source":"iana","compressible":true,"extensions":["xhtml","xht"]},"application/xhtml-voice+xml":{"source":"apache","compressible":true},"application/xliff+xml":{"source":"iana","compressible":true,"extensions":["xlf"]},"application/xml":{"source":"iana","compressible":true,"extensions":["xml","xsl","xsd","rng"]},"application/xml-dtd":{"source":"iana","compressible":true,"extensions":["dtd"]},"application/xml-external-parsed-entity":{"source":"iana"},"application/xml-patch+xml":{"source":"iana","compressible":true},"application/xmpp+xml":{"source":"iana","compressible":true},"application/xop+xml":{"source":"iana","compressible":true,"extensions":["xop"]},"application/xproc+xml":{"source":"apache","compressible":true,"extensions":["xpl"]},"application/xslt+xml":{"source":"iana","compressible":true,"extensions":["xsl","xslt"]},"application/xspf+xml":{"source":"apache","compressible":true,"extensions":["xspf"]},"application/xv+xml":{"source":"iana","compressible":true,"extensions":["mxml","xhvml","xvml","xvm"]},"application/yang":{"source":"iana","extensions":["yang"]},"application/yang-data+json":{"source":"iana","compressible":true},"application/yang-data+xml":{"source":"iana","compressible":true},"application/yang-patch+json":{"source":"iana","compressible":true},"application/yang-patch+xml":{"source":"iana","compressible":true},"application/yin+xml":{"source":"iana","compressible":true,"extensions":["yin"]},"application/zip":{"source":"iana","compressible":false,"extensions":["zip"]},"application/zlib":{"source":"iana"},"application/zstd":{"source":"iana"},"audio/1d-interleaved-parityfec":{"source":"iana"},"audio/32kadpcm":{"source":"iana"},"audio/3gpp":{"source":"iana","compressible":false,"extensions":["3gpp"]},"audio/3gpp2":{"source":"iana"},"audio/aac":{"source":"iana"},"audio/ac3":{"source":"iana"},"audio/adpcm":{"source":"apache","extensions":["adp"]},"audio/amr":{"source":"iana","extensions":["amr"]},"audio/amr-wb":{"source":"iana"},"audio/amr-wb+":{"source":"iana"},"audio/aptx":{"source":"iana"},"audio/asc":{"source":"iana"},"audio/atrac-advanced-lossless":{"source":"iana"},"audio/atrac-x":{"source":"iana"},"audio/atrac3":{"source":"iana"},"audio/basic":{"source":"iana","compressible":false,"extensions":["au","snd"]},"audio/bv16":{"source":"iana"},"audio/bv32":{"source":"iana"},"audio/clearmode":{"source":"iana"},"audio/cn":{"source":"iana"},"audio/dat12":{"source":"iana"},"audio/dls":{"source":"iana"},"audio/dsr-es201108":{"source":"iana"},"audio/dsr-es202050":{"source":"iana"},"audio/dsr-es202211":{"source":"iana"},"audio/dsr-es202212":{"source":"iana"},"audio/dv":{"source":"iana"},"audio/dvi4":{"source":"iana"},"audio/eac3":{"source":"iana"},"audio/encaprtp":{"source":"iana"},"audio/evrc":{"source":"iana"},"audio/evrc-qcp":{"source":"iana"},"audio/evrc0":{"source":"iana"},"audio/evrc1":{"source":"iana"},"audio/evrcb":{"source":"iana"},"audio/evrcb0":{"source":"iana"},"audio/evrcb1":{"source":"iana"},"audio/evrcnw":{"source":"iana"},"audio/evrcnw0":{"source":"iana"},"audio/evrcnw1":{"source":"iana"},"audio/evrcwb":{"source":"iana"},"audio/evrcwb0":{"source":"iana"},"audio/evrcwb1":{"source":"iana"},"audio/evs":{"source":"iana"},"audio/flexfec":{"source":"iana"},"audio/fwdred":{"source":"iana"},"audio/g711-0":{"source":"iana"},"audio/g719":{"source":"iana"},"audio/g722":{"source":"iana"},"audio/g7221":{"source":"iana"},"audio/g723":{"source":"iana"},"audio/g726-16":{"source":"iana"},"audio/g726-24":{"source":"iana"},"audio/g726-32":{"source":"iana"},"audio/g726-40":{"source":"iana"},"audio/g728":{"source":"iana"},"audio/g729":{"source":"iana"},"audio/g7291":{"source":"iana"},"audio/g729d":{"source":"iana"},"audio/g729e":{"source":"iana"},"audio/gsm":{"source":"iana"},"audio/gsm-efr":{"source":"iana"},"audio/gsm-hr-08":{"source":"iana"},"audio/ilbc":{"source":"iana"},"audio/ip-mr_v2.5":{"source":"iana"},"audio/isac":{"source":"apache"},"audio/l16":{"source":"iana"},"audio/l20":{"source":"iana"},"audio/l24":{"source":"iana","compressible":false},"audio/l8":{"source":"iana"},"audio/lpc":{"source":"iana"},"audio/melp":{"source":"iana"},"audio/melp1200":{"source":"iana"},"audio/melp2400":{"source":"iana"},"audio/melp600":{"source":"iana"},"audio/mhas":{"source":"iana"},"audio/midi":{"source":"apache","extensions":["mid","midi","kar","rmi"]},"audio/mobile-xmf":{"source":"iana","extensions":["mxmf"]},"audio/mp3":{"compressible":false,"extensions":["mp3"]},"audio/mp4":{"source":"iana","compressible":false,"extensions":["m4a","mp4a"]},"audio/mp4a-latm":{"source":"iana"},"audio/mpa":{"source":"iana"},"audio/mpa-robust":{"source":"iana"},"audio/mpeg":{"source":"iana","compressible":false,"extensions":["mpga","mp2","mp2a","mp3","m2a","m3a"]},"audio/mpeg4-generic":{"source":"iana"},"audio/musepack":{"source":"apache"},"audio/ogg":{"source":"iana","compressible":false,"extensions":["oga","ogg","spx","opus"]},"audio/opus":{"source":"iana"},"audio/parityfec":{"source":"iana"},"audio/pcma":{"source":"iana"},"audio/pcma-wb":{"source":"iana"},"audio/pcmu":{"source":"iana"},"audio/pcmu-wb":{"source":"iana"},"audio/prs.sid":{"source":"iana"},"audio/qcelp":{"source":"iana"},"audio/raptorfec":{"source":"iana"},"audio/red":{"source":"iana"},"audio/rtp-enc-aescm128":{"source":"iana"},"audio/rtp-midi":{"source":"iana"},"audio/rtploopback":{"source":"iana"},"audio/rtx":{"source":"iana"},"audio/s3m":{"source":"apache","extensions":["s3m"]},"audio/scip":{"source":"iana"},"audio/silk":{"source":"apache","extensions":["sil"]},"audio/smv":{"source":"iana"},"audio/smv-qcp":{"source":"iana"},"audio/smv0":{"source":"iana"},"audio/sofa":{"source":"iana"},"audio/sp-midi":{"source":"iana"},"audio/speex":{"source":"iana"},"audio/t140c":{"source":"iana"},"audio/t38":{"source":"iana"},"audio/telephone-event":{"source":"iana"},"audio/tetra_acelp":{"source":"iana"},"audio/tetra_acelp_bb":{"source":"iana"},"audio/tone":{"source":"iana"},"audio/tsvcis":{"source":"iana"},"audio/uemclip":{"source":"iana"},"audio/ulpfec":{"source":"iana"},"audio/usac":{"source":"iana"},"audio/vdvi":{"source":"iana"},"audio/vmr-wb":{"source":"iana"},"audio/vnd.3gpp.iufp":{"source":"iana"},"audio/vnd.4sb":{"source":"iana"},"audio/vnd.audiokoz":{"source":"iana"},"audio/vnd.celp":{"source":"iana"},"audio/vnd.cisco.nse":{"source":"iana"},"audio/vnd.cmles.radio-events":{"source":"iana"},"audio/vnd.cns.anp1":{"source":"iana"},"audio/vnd.cns.inf1":{"source":"iana"},"audio/vnd.dece.audio":{"source":"iana","extensions":["uva","uvva"]},"audio/vnd.digital-winds":{"source":"iana","extensions":["eol"]},"audio/vnd.dlna.adts":{"source":"iana"},"audio/vnd.dolby.heaac.1":{"source":"iana"},"audio/vnd.dolby.heaac.2":{"source":"iana"},"audio/vnd.dolby.mlp":{"source":"iana"},"audio/vnd.dolby.mps":{"source":"iana"},"audio/vnd.dolby.pl2":{"source":"iana"},"audio/vnd.dolby.pl2x":{"source":"iana"},"audio/vnd.dolby.pl2z":{"source":"iana"},"audio/vnd.dolby.pulse.1":{"source":"iana"},"audio/vnd.dra":{"source":"iana","extensions":["dra"]},"audio/vnd.dts":{"source":"iana","extensions":["dts"]},"audio/vnd.dts.hd":{"source":"iana","extensions":["dtshd"]},"audio/vnd.dts.uhd":{"source":"iana"},"audio/vnd.dvb.file":{"source":"iana"},"audio/vnd.everad.plj":{"source":"iana"},"audio/vnd.hns.audio":{"source":"iana"},"audio/vnd.lucent.voice":{"source":"iana","extensions":["lvp"]},"audio/vnd.ms-playready.media.pya":{"source":"iana","extensions":["pya"]},"audio/vnd.nokia.mobile-xmf":{"source":"iana"},"audio/vnd.nortel.vbk":{"source":"iana"},"audio/vnd.nuera.ecelp4800":{"source":"iana","extensions":["ecelp4800"]},"audio/vnd.nuera.ecelp7470":{"source":"iana","extensions":["ecelp7470"]},"audio/vnd.nuera.ecelp9600":{"source":"iana","extensions":["ecelp9600"]},"audio/vnd.octel.sbc":{"source":"iana"},"audio/vnd.presonus.multitrack":{"source":"iana"},"audio/vnd.qcelp":{"source":"iana"},"audio/vnd.rhetorex.32kadpcm":{"source":"iana"},"audio/vnd.rip":{"source":"iana","extensions":["rip"]},"audio/vnd.rn-realaudio":{"compressible":false},"audio/vnd.sealedmedia.softseal.mpeg":{"source":"iana"},"audio/vnd.vmx.cvsd":{"source":"iana"},"audio/vnd.wave":{"compressible":false},"audio/vorbis":{"source":"iana","compressible":false},"audio/vorbis-config":{"source":"iana"},"audio/wav":{"compressible":false,"extensions":["wav"]},"audio/wave":{"compressible":false,"extensions":["wav"]},"audio/webm":{"source":"apache","compressible":false,"extensions":["weba"]},"audio/x-aac":{"source":"apache","compressible":false,"extensions":["aac"]},"audio/x-aiff":{"source":"apache","extensions":["aif","aiff","aifc"]},"audio/x-caf":{"source":"apache","compressible":false,"extensions":["caf"]},"audio/x-flac":{"source":"apache","extensions":["flac"]},"audio/x-m4a":{"source":"nginx","extensions":["m4a"]},"audio/x-matroska":{"source":"apache","extensions":["mka"]},"audio/x-mpegurl":{"source":"apache","extensions":["m3u"]},"audio/x-ms-wax":{"source":"apache","extensions":["wax"]},"audio/x-ms-wma":{"source":"apache","extensions":["wma"]},"audio/x-pn-realaudio":{"source":"apache","extensions":["ram","ra"]},"audio/x-pn-realaudio-plugin":{"source":"apache","extensions":["rmp"]},"audio/x-realaudio":{"source":"nginx","extensions":["ra"]},"audio/x-tta":{"source":"apache"},"audio/x-wav":{"source":"apache","extensions":["wav"]},"audio/xm":{"source":"apache","extensions":["xm"]},"chemical/x-cdx":{"source":"apache","extensions":["cdx"]},"chemical/x-cif":{"source":"apache","extensions":["cif"]},"chemical/x-cmdf":{"source":"apache","extensions":["cmdf"]},"chemical/x-cml":{"source":"apache","extensions":["cml"]},"chemical/x-csml":{"source":"apache","extensions":["csml"]},"chemical/x-pdb":{"source":"apache"},"chemical/x-xyz":{"source":"apache","extensions":["xyz"]},"font/collection":{"source":"iana","extensions":["ttc"]},"font/otf":{"source":"iana","compressible":true,"extensions":["otf"]},"font/sfnt":{"source":"iana"},"font/ttf":{"source":"iana","compressible":true,"extensions":["ttf"]},"font/woff":{"source":"iana","extensions":["woff"]},"font/woff2":{"source":"iana","extensions":["woff2"]},"image/aces":{"source":"iana","extensions":["exr"]},"image/apng":{"compressible":false,"extensions":["apng"]},"image/avci":{"source":"iana","extensions":["avci"]},"image/avcs":{"source":"iana","extensions":["avcs"]},"image/avif":{"source":"iana","compressible":false,"extensions":["avif"]},"image/bmp":{"source":"iana","compressible":true,"extensions":["bmp"]},"image/cgm":{"source":"iana","extensions":["cgm"]},"image/dicom-rle":{"source":"iana","extensions":["drle"]},"image/emf":{"source":"iana","extensions":["emf"]},"image/fits":{"source":"iana","extensions":["fits"]},"image/g3fax":{"source":"iana","extensions":["g3"]},"image/gif":{"source":"iana","compressible":false,"extensions":["gif"]},"image/heic":{"source":"iana","extensions":["heic"]},"image/heic-sequence":{"source":"iana","extensions":["heics"]},"image/heif":{"source":"iana","extensions":["heif"]},"image/heif-sequence":{"source":"iana","extensions":["heifs"]},"image/hej2k":{"source":"iana","extensions":["hej2"]},"image/hsj2":{"source":"iana","extensions":["hsj2"]},"image/ief":{"source":"iana","extensions":["ief"]},"image/jls":{"source":"iana","extensions":["jls"]},"image/jp2":{"source":"iana","compressible":false,"extensions":["jp2","jpg2"]},"image/jpeg":{"source":"iana","compressible":false,"extensions":["jpeg","jpg","jpe"]},"image/jph":{"source":"iana","extensions":["jph"]},"image/jphc":{"source":"iana","extensions":["jhc"]},"image/jpm":{"source":"iana","compressible":false,"extensions":["jpm"]},"image/jpx":{"source":"iana","compressible":false,"extensions":["jpx","jpf"]},"image/jxr":{"source":"iana","extensions":["jxr"]},"image/jxra":{"source":"iana","extensions":["jxra"]},"image/jxrs":{"source":"iana","extensions":["jxrs"]},"image/jxs":{"source":"iana","extensions":["jxs"]},"image/jxsc":{"source":"iana","extensions":["jxsc"]},"image/jxsi":{"source":"iana","extensions":["jxsi"]},"image/jxss":{"source":"iana","extensions":["jxss"]},"image/ktx":{"source":"iana","extensions":["ktx"]},"image/ktx2":{"source":"iana","extensions":["ktx2"]},"image/naplps":{"source":"iana"},"image/pjpeg":{"compressible":false},"image/png":{"source":"iana","compressible":false,"extensions":["png"]},"image/prs.btif":{"source":"iana","extensions":["btif"]},"image/prs.pti":{"source":"iana","extensions":["pti"]},"image/pwg-raster":{"source":"iana"},"image/sgi":{"source":"apache","extensions":["sgi"]},"image/svg+xml":{"source":"iana","compressible":true,"extensions":["svg","svgz"]},"image/t38":{"source":"iana","extensions":["t38"]},"image/tiff":{"source":"iana","compressible":false,"extensions":["tif","tiff"]},"image/tiff-fx":{"source":"iana","extensions":["tfx"]},"image/vnd.adobe.photoshop":{"source":"iana","compressible":true,"extensions":["psd"]},"image/vnd.airzip.accelerator.azv":{"source":"iana","extensions":["azv"]},"image/vnd.cns.inf2":{"source":"iana"},"image/vnd.dece.graphic":{"source":"iana","extensions":["uvi","uvvi","uvg","uvvg"]},"image/vnd.djvu":{"source":"iana","extensions":["djvu","djv"]},"image/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"image/vnd.dwg":{"source":"iana","extensions":["dwg"]},"image/vnd.dxf":{"source":"iana","extensions":["dxf"]},"image/vnd.fastbidsheet":{"source":"iana","extensions":["fbs"]},"image/vnd.fpx":{"source":"iana","extensions":["fpx"]},"image/vnd.fst":{"source":"iana","extensions":["fst"]},"image/vnd.fujixerox.edmics-mmr":{"source":"iana","extensions":["mmr"]},"image/vnd.fujixerox.edmics-rlc":{"source":"iana","extensions":["rlc"]},"image/vnd.globalgraphics.pgb":{"source":"iana"},"image/vnd.microsoft.icon":{"source":"iana","compressible":true,"extensions":["ico"]},"image/vnd.mix":{"source":"iana"},"image/vnd.mozilla.apng":{"source":"iana"},"image/vnd.ms-dds":{"compressible":true,"extensions":["dds"]},"image/vnd.ms-modi":{"source":"iana","extensions":["mdi"]},"image/vnd.ms-photo":{"source":"apache","extensions":["wdp"]},"image/vnd.net-fpx":{"source":"iana","extensions":["npx"]},"image/vnd.pco.b16":{"source":"iana","extensions":["b16"]},"image/vnd.radiance":{"source":"iana"},"image/vnd.sealed.png":{"source":"iana"},"image/vnd.sealedmedia.softseal.gif":{"source":"iana"},"image/vnd.sealedmedia.softseal.jpg":{"source":"iana"},"image/vnd.svf":{"source":"iana"},"image/vnd.tencent.tap":{"source":"iana","extensions":["tap"]},"image/vnd.valve.source.texture":{"source":"iana","extensions":["vtf"]},"image/vnd.wap.wbmp":{"source":"iana","extensions":["wbmp"]},"image/vnd.xiff":{"source":"iana","extensions":["xif"]},"image/vnd.zbrush.pcx":{"source":"iana","extensions":["pcx"]},"image/webp":{"source":"apache","extensions":["webp"]},"image/wmf":{"source":"iana","extensions":["wmf"]},"image/x-3ds":{"source":"apache","extensions":["3ds"]},"image/x-cmu-raster":{"source":"apache","extensions":["ras"]},"image/x-cmx":{"source":"apache","extensions":["cmx"]},"image/x-freehand":{"source":"apache","extensions":["fh","fhc","fh4","fh5","fh7"]},"image/x-icon":{"source":"apache","compressible":true,"extensions":["ico"]},"image/x-jng":{"source":"nginx","extensions":["jng"]},"image/x-mrsid-image":{"source":"apache","extensions":["sid"]},"image/x-ms-bmp":{"source":"nginx","compressible":true,"extensions":["bmp"]},"image/x-pcx":{"source":"apache","extensions":["pcx"]},"image/x-pict":{"source":"apache","extensions":["pic","pct"]},"image/x-portable-anymap":{"source":"apache","extensions":["pnm"]},"image/x-portable-bitmap":{"source":"apache","extensions":["pbm"]},"image/x-portable-graymap":{"source":"apache","extensions":["pgm"]},"image/x-portable-pixmap":{"source":"apache","extensions":["ppm"]},"image/x-rgb":{"source":"apache","extensions":["rgb"]},"image/x-tga":{"source":"apache","extensions":["tga"]},"image/x-xbitmap":{"source":"apache","extensions":["xbm"]},"image/x-xcf":{"compressible":false},"image/x-xpixmap":{"source":"apache","extensions":["xpm"]},"image/x-xwindowdump":{"source":"apache","extensions":["xwd"]},"message/cpim":{"source":"iana"},"message/delivery-status":{"source":"iana"},"message/disposition-notification":{"source":"iana","extensions":["disposition-notification"]},"message/external-body":{"source":"iana"},"message/feedback-report":{"source":"iana"},"message/global":{"source":"iana","extensions":["u8msg"]},"message/global-delivery-status":{"source":"iana","extensions":["u8dsn"]},"message/global-disposition-notification":{"source":"iana","extensions":["u8mdn"]},"message/global-headers":{"source":"iana","extensions":["u8hdr"]},"message/http":{"source":"iana","compressible":false},"message/imdn+xml":{"source":"iana","compressible":true},"message/news":{"source":"iana"},"message/partial":{"source":"iana","compressible":false},"message/rfc822":{"source":"iana","compressible":true,"extensions":["eml","mime"]},"message/s-http":{"source":"iana"},"message/sip":{"source":"iana"},"message/sipfrag":{"source":"iana"},"message/tracking-status":{"source":"iana"},"message/vnd.si.simp":{"source":"iana"},"message/vnd.wfa.wsc":{"source":"iana","extensions":["wsc"]},"model/3mf":{"source":"iana","extensions":["3mf"]},"model/e57":{"source":"iana"},"model/gltf+json":{"source":"iana","compressible":true,"extensions":["gltf"]},"model/gltf-binary":{"source":"iana","compressible":true,"extensions":["glb"]},"model/iges":{"source":"iana","compressible":false,"extensions":["igs","iges"]},"model/mesh":{"source":"iana","compressible":false,"extensions":["msh","mesh","silo"]},"model/mtl":{"source":"iana","extensions":["mtl"]},"model/obj":{"source":"iana","extensions":["obj"]},"model/step":{"source":"iana"},"model/step+xml":{"source":"iana","compressible":true,"extensions":["stpx"]},"model/step+zip":{"source":"iana","compressible":false,"extensions":["stpz"]},"model/step-xml+zip":{"source":"iana","compressible":false,"extensions":["stpxz"]},"model/stl":{"source":"iana","extensions":["stl"]},"model/vnd.collada+xml":{"source":"iana","compressible":true,"extensions":["dae"]},"model/vnd.dwf":{"source":"iana","extensions":["dwf"]},"model/vnd.flatland.3dml":{"source":"iana"},"model/vnd.gdl":{"source":"iana","extensions":["gdl"]},"model/vnd.gs-gdl":{"source":"apache"},"model/vnd.gs.gdl":{"source":"iana"},"model/vnd.gtw":{"source":"iana","extensions":["gtw"]},"model/vnd.moml+xml":{"source":"iana","compressible":true},"model/vnd.mts":{"source":"iana","extensions":["mts"]},"model/vnd.opengex":{"source":"iana","extensions":["ogex"]},"model/vnd.parasolid.transmit.binary":{"source":"iana","extensions":["x_b"]},"model/vnd.parasolid.transmit.text":{"source":"iana","extensions":["x_t"]},"model/vnd.pytha.pyox":{"source":"iana"},"model/vnd.rosette.annotated-data-model":{"source":"iana"},"model/vnd.sap.vds":{"source":"iana","extensions":["vds"]},"model/vnd.usdz+zip":{"source":"iana","compressible":false,"extensions":["usdz"]},"model/vnd.valve.source.compiled-map":{"source":"iana","extensions":["bsp"]},"model/vnd.vtu":{"source":"iana","extensions":["vtu"]},"model/vrml":{"source":"iana","compressible":false,"extensions":["wrl","vrml"]},"model/x3d+binary":{"source":"apache","compressible":false,"extensions":["x3db","x3dbz"]},"model/x3d+fastinfoset":{"source":"iana","extensions":["x3db"]},"model/x3d+vrml":{"source":"apache","compressible":false,"extensions":["x3dv","x3dvz"]},"model/x3d+xml":{"source":"iana","compressible":true,"extensions":["x3d","x3dz"]},"model/x3d-vrml":{"source":"iana","extensions":["x3dv"]},"multipart/alternative":{"source":"iana","compressible":false},"multipart/appledouble":{"source":"iana"},"multipart/byteranges":{"source":"iana"},"multipart/digest":{"source":"iana"},"multipart/encrypted":{"source":"iana","compressible":false},"multipart/form-data":{"source":"iana","compressible":false},"multipart/header-set":{"source":"iana"},"multipart/mixed":{"source":"iana"},"multipart/multilingual":{"source":"iana"},"multipart/parallel":{"source":"iana"},"multipart/related":{"source":"iana","compressible":false},"multipart/report":{"source":"iana"},"multipart/signed":{"source":"iana","compressible":false},"multipart/vnd.bint.med-plus":{"source":"iana"},"multipart/voice-message":{"source":"iana"},"multipart/x-mixed-replace":{"source":"iana"},"text/1d-interleaved-parityfec":{"source":"iana"},"text/cache-manifest":{"source":"iana","compressible":true,"extensions":["appcache","manifest"]},"text/calendar":{"source":"iana","extensions":["ics","ifb"]},"text/calender":{"compressible":true},"text/cmd":{"compressible":true},"text/coffeescript":{"extensions":["coffee","litcoffee"]},"text/cql":{"source":"iana"},"text/cql-expression":{"source":"iana"},"text/cql-identifier":{"source":"iana"},"text/css":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["css"]},"text/csv":{"source":"iana","compressible":true,"extensions":["csv"]},"text/csv-schema":{"source":"iana"},"text/directory":{"source":"iana"},"text/dns":{"source":"iana"},"text/ecmascript":{"source":"iana"},"text/encaprtp":{"source":"iana"},"text/enriched":{"source":"iana"},"text/fhirpath":{"source":"iana"},"text/flexfec":{"source":"iana"},"text/fwdred":{"source":"iana"},"text/gff3":{"source":"iana"},"text/grammar-ref-list":{"source":"iana"},"text/html":{"source":"iana","compressible":true,"extensions":["html","htm","shtml"]},"text/jade":{"extensions":["jade"]},"text/javascript":{"source":"iana","compressible":true},"text/jcr-cnd":{"source":"iana"},"text/jsx":{"compressible":true,"extensions":["jsx"]},"text/less":{"compressible":true,"extensions":["less"]},"text/markdown":{"source":"iana","compressible":true,"extensions":["markdown","md"]},"text/mathml":{"source":"nginx","extensions":["mml"]},"text/mdx":{"compressible":true,"extensions":["mdx"]},"text/mizar":{"source":"iana"},"text/n3":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["n3"]},"text/parameters":{"source":"iana","charset":"UTF-8"},"text/parityfec":{"source":"iana"},"text/plain":{"source":"iana","compressible":true,"extensions":["txt","text","conf","def","list","log","in","ini"]},"text/provenance-notation":{"source":"iana","charset":"UTF-8"},"text/prs.fallenstein.rst":{"source":"iana"},"text/prs.lines.tag":{"source":"iana","extensions":["dsc"]},"text/prs.prop.logic":{"source":"iana"},"text/raptorfec":{"source":"iana"},"text/red":{"source":"iana"},"text/rfc822-headers":{"source":"iana"},"text/richtext":{"source":"iana","compressible":true,"extensions":["rtx"]},"text/rtf":{"source":"iana","compressible":true,"extensions":["rtf"]},"text/rtp-enc-aescm128":{"source":"iana"},"text/rtploopback":{"source":"iana"},"text/rtx":{"source":"iana"},"text/sgml":{"source":"iana","extensions":["sgml","sgm"]},"text/shaclc":{"source":"iana"},"text/shex":{"source":"iana","extensions":["shex"]},"text/slim":{"extensions":["slim","slm"]},"text/spdx":{"source":"iana","extensions":["spdx"]},"text/strings":{"source":"iana"},"text/stylus":{"extensions":["stylus","styl"]},"text/t140":{"source":"iana"},"text/tab-separated-values":{"source":"iana","compressible":true,"extensions":["tsv"]},"text/troff":{"source":"iana","extensions":["t","tr","roff","man","me","ms"]},"text/turtle":{"source":"iana","charset":"UTF-8","extensions":["ttl"]},"text/ulpfec":{"source":"iana"},"text/uri-list":{"source":"iana","compressible":true,"extensions":["uri","uris","urls"]},"text/vcard":{"source":"iana","compressible":true,"extensions":["vcard"]},"text/vnd.a":{"source":"iana"},"text/vnd.abc":{"source":"iana"},"text/vnd.ascii-art":{"source":"iana"},"text/vnd.curl":{"source":"iana","extensions":["curl"]},"text/vnd.curl.dcurl":{"source":"apache","extensions":["dcurl"]},"text/vnd.curl.mcurl":{"source":"apache","extensions":["mcurl"]},"text/vnd.curl.scurl":{"source":"apache","extensions":["scurl"]},"text/vnd.debian.copyright":{"source":"iana","charset":"UTF-8"},"text/vnd.dmclientscript":{"source":"iana"},"text/vnd.dvb.subtitle":{"source":"iana","extensions":["sub"]},"text/vnd.esmertec.theme-descriptor":{"source":"iana","charset":"UTF-8"},"text/vnd.familysearch.gedcom":{"source":"iana","extensions":["ged"]},"text/vnd.ficlab.flt":{"source":"iana"},"text/vnd.fly":{"source":"iana","extensions":["fly"]},"text/vnd.fmi.flexstor":{"source":"iana","extensions":["flx"]},"text/vnd.gml":{"source":"iana"},"text/vnd.graphviz":{"source":"iana","extensions":["gv"]},"text/vnd.hans":{"source":"iana"},"text/vnd.hgl":{"source":"iana"},"text/vnd.in3d.3dml":{"source":"iana","extensions":["3dml"]},"text/vnd.in3d.spot":{"source":"iana","extensions":["spot"]},"text/vnd.iptc.newsml":{"source":"iana"},"text/vnd.iptc.nitf":{"source":"iana"},"text/vnd.latex-z":{"source":"iana"},"text/vnd.motorola.reflex":{"source":"iana"},"text/vnd.ms-mediapackage":{"source":"iana"},"text/vnd.net2phone.commcenter.command":{"source":"iana"},"text/vnd.radisys.msml-basic-layout":{"source":"iana"},"text/vnd.senx.warpscript":{"source":"iana"},"text/vnd.si.uricatalogue":{"source":"iana"},"text/vnd.sosi":{"source":"iana"},"text/vnd.sun.j2me.app-descriptor":{"source":"iana","charset":"UTF-8","extensions":["jad"]},"text/vnd.trolltech.linguist":{"source":"iana","charset":"UTF-8"},"text/vnd.wap.si":{"source":"iana"},"text/vnd.wap.sl":{"source":"iana"},"text/vnd.wap.wml":{"source":"iana","extensions":["wml"]},"text/vnd.wap.wmlscript":{"source":"iana","extensions":["wmls"]},"text/vtt":{"source":"iana","charset":"UTF-8","compressible":true,"extensions":["vtt"]},"text/x-asm":{"source":"apache","extensions":["s","asm"]},"text/x-c":{"source":"apache","extensions":["c","cc","cxx","cpp","h","hh","dic"]},"text/x-component":{"source":"nginx","extensions":["htc"]},"text/x-fortran":{"source":"apache","extensions":["f","for","f77","f90"]},"text/x-gwt-rpc":{"compressible":true},"text/x-handlebars-template":{"extensions":["hbs"]},"text/x-java-source":{"source":"apache","extensions":["java"]},"text/x-jquery-tmpl":{"compressible":true},"text/x-lua":{"extensions":["lua"]},"text/x-markdown":{"compressible":true,"extensions":["mkd"]},"text/x-nfo":{"source":"apache","extensions":["nfo"]},"text/x-opml":{"source":"apache","extensions":["opml"]},"text/x-org":{"compressible":true,"extensions":["org"]},"text/x-pascal":{"source":"apache","extensions":["p","pas"]},"text/x-processing":{"compressible":true,"extensions":["pde"]},"text/x-sass":{"extensions":["sass"]},"text/x-scss":{"extensions":["scss"]},"text/x-setext":{"source":"apache","extensions":["etx"]},"text/x-sfv":{"source":"apache","extensions":["sfv"]},"text/x-suse-ymp":{"compressible":true,"extensions":["ymp"]},"text/x-uuencode":{"source":"apache","extensions":["uu"]},"text/x-vcalendar":{"source":"apache","extensions":["vcs"]},"text/x-vcard":{"source":"apache","extensions":["vcf"]},"text/xml":{"source":"iana","compressible":true,"extensions":["xml"]},"text/xml-external-parsed-entity":{"source":"iana"},"text/yaml":{"compressible":true,"extensions":["yaml","yml"]},"video/1d-interleaved-parityfec":{"source":"iana"},"video/3gpp":{"source":"iana","extensions":["3gp","3gpp"]},"video/3gpp-tt":{"source":"iana"},"video/3gpp2":{"source":"iana","extensions":["3g2"]},"video/av1":{"source":"iana"},"video/bmpeg":{"source":"iana"},"video/bt656":{"source":"iana"},"video/celb":{"source":"iana"},"video/dv":{"source":"iana"},"video/encaprtp":{"source":"iana"},"video/ffv1":{"source":"iana"},"video/flexfec":{"source":"iana"},"video/h261":{"source":"iana","extensions":["h261"]},"video/h263":{"source":"iana","extensions":["h263"]},"video/h263-1998":{"source":"iana"},"video/h263-2000":{"source":"iana"},"video/h264":{"source":"iana","extensions":["h264"]},"video/h264-rcdo":{"source":"iana"},"video/h264-svc":{"source":"iana"},"video/h265":{"source":"iana"},"video/iso.segment":{"source":"iana","extensions":["m4s"]},"video/jpeg":{"source":"iana","extensions":["jpgv"]},"video/jpeg2000":{"source":"iana"},"video/jpm":{"source":"apache","extensions":["jpm","jpgm"]},"video/jxsv":{"source":"iana"},"video/mj2":{"source":"iana","extensions":["mj2","mjp2"]},"video/mp1s":{"source":"iana"},"video/mp2p":{"source":"iana"},"video/mp2t":{"source":"iana","extensions":["ts"]},"video/mp4":{"source":"iana","compressible":false,"extensions":["mp4","mp4v","mpg4"]},"video/mp4v-es":{"source":"iana"},"video/mpeg":{"source":"iana","compressible":false,"extensions":["mpeg","mpg","mpe","m1v","m2v"]},"video/mpeg4-generic":{"source":"iana"},"video/mpv":{"source":"iana"},"video/nv":{"source":"iana"},"video/ogg":{"source":"iana","compressible":false,"extensions":["ogv"]},"video/parityfec":{"source":"iana"},"video/pointer":{"source":"iana"},"video/quicktime":{"source":"iana","compressible":false,"extensions":["qt","mov"]},"video/raptorfec":{"source":"iana"},"video/raw":{"source":"iana"},"video/rtp-enc-aescm128":{"source":"iana"},"video/rtploopback":{"source":"iana"},"video/rtx":{"source":"iana"},"video/scip":{"source":"iana"},"video/smpte291":{"source":"iana"},"video/smpte292m":{"source":"iana"},"video/ulpfec":{"source":"iana"},"video/vc1":{"source":"iana"},"video/vc2":{"source":"iana"},"video/vnd.cctv":{"source":"iana"},"video/vnd.dece.hd":{"source":"iana","extensions":["uvh","uvvh"]},"video/vnd.dece.mobile":{"source":"iana","extensions":["uvm","uvvm"]},"video/vnd.dece.mp4":{"source":"iana"},"video/vnd.dece.pd":{"source":"iana","extensions":["uvp","uvvp"]},"video/vnd.dece.sd":{"source":"iana","extensions":["uvs","uvvs"]},"video/vnd.dece.video":{"source":"iana","extensions":["uvv","uvvv"]},"video/vnd.directv.mpeg":{"source":"iana"},"video/vnd.directv.mpeg-tts":{"source":"iana"},"video/vnd.dlna.mpeg-tts":{"source":"iana"},"video/vnd.dvb.file":{"source":"iana","extensions":["dvb"]},"video/vnd.fvt":{"source":"iana","extensions":["fvt"]},"video/vnd.hns.video":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.1dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-1010":{"source":"iana"},"video/vnd.iptvforum.2dparityfec-2005":{"source":"iana"},"video/vnd.iptvforum.ttsavc":{"source":"iana"},"video/vnd.iptvforum.ttsmpeg2":{"source":"iana"},"video/vnd.motorola.video":{"source":"iana"},"video/vnd.motorola.videop":{"source":"iana"},"video/vnd.mpegurl":{"source":"iana","extensions":["mxu","m4u"]},"video/vnd.ms-playready.media.pyv":{"source":"iana","extensions":["pyv"]},"video/vnd.nokia.interleaved-multimedia":{"source":"iana"},"video/vnd.nokia.mp4vr":{"source":"iana"},"video/vnd.nokia.videovoip":{"source":"iana"},"video/vnd.objectvideo":{"source":"iana"},"video/vnd.radgamettools.bink":{"source":"iana"},"video/vnd.radgamettools.smacker":{"source":"iana"},"video/vnd.sealed.mpeg1":{"source":"iana"},"video/vnd.sealed.mpeg4":{"source":"iana"},"video/vnd.sealed.swf":{"source":"iana"},"video/vnd.sealedmedia.softseal.mov":{"source":"iana"},"video/vnd.uvvu.mp4":{"source":"iana","extensions":["uvu","uvvu"]},"video/vnd.vivo":{"source":"iana","extensions":["viv"]},"video/vnd.youtube.yt":{"source":"iana"},"video/vp8":{"source":"iana"},"video/vp9":{"source":"iana"},"video/webm":{"source":"apache","compressible":false,"extensions":["webm"]},"video/x-f4v":{"source":"apache","extensions":["f4v"]},"video/x-fli":{"source":"apache","extensions":["fli"]},"video/x-flv":{"source":"apache","compressible":false,"extensions":["flv"]},"video/x-m4v":{"source":"apache","extensions":["m4v"]},"video/x-matroska":{"source":"apache","compressible":false,"extensions":["mkv","mk3d","mks"]},"video/x-mng":{"source":"apache","extensions":["mng"]},"video/x-ms-asf":{"source":"apache","extensions":["asf","asx"]},"video/x-ms-vob":{"source":"apache","extensions":["vob"]},"video/x-ms-wm":{"source":"apache","extensions":["wm"]},"video/x-ms-wmv":{"source":"apache","compressible":false,"extensions":["wmv"]},"video/x-ms-wmx":{"source":"apache","extensions":["wmx"]},"video/x-ms-wvx":{"source":"apache","extensions":["wvx"]},"video/x-msvideo":{"source":"apache","extensions":["avi"]},"video/x-sgi-movie":{"source":"apache","extensions":["movie"]},"video/x-smv":{"source":"apache","extensions":["smv"]},"x-conference/x-cooltalk":{"source":"apache","extensions":["ice"]},"x-shader/x-fragment":{"compressible":true},"x-shader/x-vertex":{"compressible":true}}')}},a={};function n(i){var o=a[i];if(void 0!==o)return o.exports;var s=a[i]={exports:{}};return e[i](s,s.exports,n),s.exports}n.n=e=>{var a=e&&e.__esModule?()=>e.default:()=>e;return n.d(a,{a}),a},n.d=(e,a)=>{for(var i in a)n.o(a,i)&&!n.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:a[i]})},n.o=(e,a)=>Object.prototype.hasOwnProperty.call(e,a),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};function a(e,a){return function(){return e.apply(a,arguments)}}n.r(e),n.d(e,{hasBrowserEnv:()=>ne,hasStandardBrowserEnv:()=>ie,hasStandardBrowserWebWorkerEnv:()=>se});const{toString:i}=Object.prototype,{getPrototypeOf:o}=Object,s=(t=Object.create(null),e=>{const a=i.call(e);return t[a]||(t[a]=a.slice(8,-1).toLowerCase())});var t;const r=e=>(e=e.toLowerCase(),a=>s(a)===e),c=e=>a=>typeof a===e,{isArray:p}=Array,l=c("undefined"),u=r("ArrayBuffer"),m=c("string"),d=c("function"),f=c("number"),x=e=>null!==e&&"object"==typeof e,h=e=>{if("object"!==s(e))return!1;const a=o(e);return!(null!==a&&a!==Object.prototype&&null!==Object.getPrototypeOf(a)||Symbol.toStringTag in e||Symbol.iterator in e)},v=r("Date"),b=r("File"),g=r("Blob"),y=r("FileList"),w=r("URLSearchParams");function k(e,a,{allOwnKeys:n=!1}={}){if(null==e)return;let i,o;if("object"!=typeof e&&(e=[e]),p(e))for(i=0,o=e.length;i<o;i++)a.call(null,e[i],i,e);else{const o=n?Object.getOwnPropertyNames(e):Object.keys(e),s=o.length;let t;for(i=0;i<s;i++)t=o[i],a.call(null,e[t],t,e)}}function S(e,a){a=a.toLowerCase();const n=Object.keys(e);let i,o=n.length;for(;o-- >0;)if(i=n[o],a===i.toLowerCase())return i;return null}const j="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:global,_=e=>!l(e)&&e!==j,E=(O="undefined"!=typeof Uint8Array&&o(Uint8Array),e=>O&&e instanceof O);var O;const C=r("HTMLFormElement"),T=(({hasOwnProperty:e})=>(a,n)=>e.call(a,n))(Object.prototype),R=r("RegExp"),F=(e,a)=>{const n=Object.getOwnPropertyDescriptors(e),i={};k(n,((n,o)=>{let s;!1!==(s=a(n,o,e))&&(i[o]=s||n)})),Object.defineProperties(e,i)},L="abcdefghijklmnopqrstuvwxyz",A="0123456789",z={DIGIT:A,ALPHA:L,ALPHA_DIGIT:L+L.toUpperCase()+A},N=r("AsyncFunction"),P={isArray:p,isArrayBuffer:u,isBuffer:function(e){return null!==e&&!l(e)&&null!==e.constructor&&!l(e.constructor)&&d(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let a;return e&&("function"==typeof FormData&&e instanceof FormData||d(e.append)&&("formdata"===(a=s(e))||"object"===a&&d(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let a;return a="undefined"!=typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&u(e.buffer),a},isString:m,isNumber:f,isBoolean:e=>!0===e||!1===e,isObject:x,isPlainObject:h,isUndefined:l,isDate:v,isFile:b,isBlob:g,isRegExp:R,isFunction:d,isStream:e=>x(e)&&d(e.pipe),isURLSearchParams:w,isTypedArray:E,isFileList:y,forEach:k,merge:function e(){const{caseless:a}=_(this)&&this||{},n={},i=(i,o)=>{const s=a&&S(n,o)||o;h(n[s])&&h(i)?n[s]=e(n[s],i):h(i)?n[s]=e({},i):p(i)?n[s]=i.slice():n[s]=i};for(let e=0,a=arguments.length;e<a;e++)arguments[e]&&k(arguments[e],i);return n},extend:(e,n,i,{allOwnKeys:o}={})=>(k(n,((n,o)=>{i&&d(n)?e[o]=a(n,i):e[o]=n}),{allOwnKeys:o}),e),trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,a,n,i)=>{e.prototype=Object.create(a.prototype,i),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:a.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,a,n,i)=>{let s,t,r;const c={};if(a=a||{},null==e)return a;do{for(s=Object.getOwnPropertyNames(e),t=s.length;t-- >0;)r=s[t],i&&!i(r,e,a)||c[r]||(a[r]=e[r],c[r]=!0);e=!1!==n&&o(e)}while(e&&(!n||n(e,a))&&e!==Object.prototype);return a},kindOf:s,kindOfTest:r,endsWith:(e,a,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=a.length;const i=e.indexOf(a,n);return-1!==i&&i===n},toArray:e=>{if(!e)return null;if(p(e))return e;let a=e.length;if(!f(a))return null;const n=new Array(a);for(;a-- >0;)n[a]=e[a];return n},forEachEntry:(e,a)=>{const n=(e&&e[Symbol.iterator]).call(e);let i;for(;(i=n.next())&&!i.done;){const n=i.value;a.call(e,n[0],n[1])}},matchAll:(e,a)=>{let n;const i=[];for(;null!==(n=e.exec(a));)i.push(n);return i},isHTMLForm:C,hasOwnProperty:T,hasOwnProp:T,reduceDescriptors:F,freezeMethods:e=>{F(e,((a,n)=>{if(d(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const i=e[n];d(i)&&(a.enumerable=!1,"writable"in a?a.writable=!1:a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,a)=>{const n={},i=e=>{e.forEach((e=>{n[e]=!0}))};return p(e)?i(e):i(String(e).split(a)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,a,n){return a.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,a)=>(e=+e,Number.isFinite(e)?e:a),findKey:S,global:j,isContextDefined:_,ALPHABET:z,generateString:(e=16,a=z.ALPHA_DIGIT)=>{let n="";const{length:i}=a;for(;e--;)n+=a[Math.random()*i|0];return n},isSpecCompliantForm:function(e){return!!(e&&d(e.append)&&"FormData"===e[Symbol.toStringTag]&&e[Symbol.iterator])},toJSONObject:e=>{const a=new Array(10),n=(e,i)=>{if(x(e)){if(a.indexOf(e)>=0)return;if(!("toJSON"in e)){a[i]=e;const o=p(e)?[]:{};return k(e,((e,a)=>{const s=n(e,i+1);!l(s)&&(o[a]=s)})),a[i]=void 0,o}}return e};return n(e,0)},isAsyncFn:N,isThenable:e=>e&&(x(e)||d(e))&&d(e.then)&&d(e.catch)};function B(e,a,n,i,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",a&&(this.code=a),n&&(this.config=n),i&&(this.request=i),o&&(this.response=o)}P.inherits(B,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:P.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const D=B.prototype,q={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{q[e]={value:e}})),Object.defineProperties(B,q),Object.defineProperty(D,"isAxiosError",{value:!0}),B.from=(e,a,n,i,o,s)=>{const t=Object.create(D);return P.toFlatObject(e,t,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),B.call(t,e.message,a,n,i,o),t.cause=e,t.name=e.name,s&&Object.assign(t,s),t};const U=B,I=n(4511);function M(e){return P.isPlainObject(e)||P.isArray(e)}function $(e){return P.endsWith(e,"[]")?e.slice(0,-2):e}function W(e,a,n){return e?e.concat(a).map((function(e,a){return e=$(e),!n&&a?"["+e+"]":e})).join(n?".":""):a}const H=P.toFlatObject(P,{},null,(function(e){return/^is[A-Z]/.test(e)})),J=function(e,a,n){if(!P.isObject(e))throw new TypeError("target must be an object");a=a||new(I||FormData),n=P.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,a){return!P.isUndefined(a[e])}));const i=n.metaTokens,o=n.visitor||p,s=n.dots,t=n.indexes,r=(n.Blob||"undefined"!=typeof Blob&&Blob)&&P.isSpecCompliantForm(a);if(!P.isFunction(o))throw new TypeError("visitor must be a function");function c(e){if(null===e)return"";if(P.isDate(e))return e.toISOString();if(!r&&P.isBlob(e))throw new U("Blob is not supported. Use a Buffer instead.");return P.isArrayBuffer(e)||P.isTypedArray(e)?r&&"function"==typeof Blob?new Blob([e]):Buffer.from(e):e}function p(e,n,o){let r=e;if(e&&!o&&"object"==typeof e)if(P.endsWith(n,"{}"))n=i?n:n.slice(0,-2),e=JSON.stringify(e);else if(P.isArray(e)&&function(e){return P.isArray(e)&&!e.some(M)}(e)||(P.isFileList(e)||P.endsWith(n,"[]"))&&(r=P.toArray(e)))return n=$(n),r.forEach((function(e,i){!P.isUndefined(e)&&null!==e&&a.append(!0===t?W([n],i,s):null===t?n:n+"[]",c(e))})),!1;return!!M(e)||(a.append(W(o,n,s),c(e)),!1)}const l=[],u=Object.assign(H,{defaultVisitor:p,convertValue:c,isVisitable:M});if(!P.isObject(e))throw new TypeError("data must be an object");return function e(n,i){if(!P.isUndefined(n)){if(-1!==l.indexOf(n))throw Error("Circular reference detected in "+i.join("."));l.push(n),P.forEach(n,(function(n,s){!0===(!(P.isUndefined(n)||null===n)&&o.call(a,n,P.isString(s)?s.trim():s,i,u))&&e(n,i?i.concat(s):[s])})),l.pop()}}(e),a};function G(e){const a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return a[e]}))}function V(e,a){this._pairs=[],e&&J(e,this,a)}const K=V.prototype;K.append=function(e,a){this._pairs.push([e,a])},K.toString=function(e){const a=e?function(a){return e.call(this,a,G)}:G;return this._pairs.map((function(e){return a(e[0])+"="+a(e[1])}),"").join("&")};const Y=V;function X(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Q(e,a,n){if(!a)return e;const i=n&&n.encode||X,o=n&&n.serialize;let s;if(s=o?o(a,n):P.isURLSearchParams(a)?a.toString():new Y(a,n).toString(i),s){const a=e.indexOf("#");-1!==a&&(e=e.slice(0,a)),e+=(-1===e.indexOf("?")?"?":"&")+s}return e}const Z=class{constructor(){this.handlers=[]}use(e,a,n){return this.handlers.push({fulfilled:e,rejected:a,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){P.forEach(this.handlers,(function(a){null!==a&&e(a)}))}},ee={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},ae={isNode:!0,classes:{URLSearchParams:n(7016).URLSearchParams,FormData:I,Blob:"undefined"!=typeof Blob&&Blob||null},protocols:["http","https","file","data"]},ne="undefined"!=typeof window&&"undefined"!=typeof document,ie=(oe="undefined"!=typeof navigator&&navigator.product,ne&&["ReactNative","NativeScript","NS"].indexOf(oe)<0);var oe;const se="undefined"!=typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"==typeof self.importScripts,te={...e,...ae},re=function(e){function a(e,n,i,o){let s=e[o++];if("__proto__"===s)return!0;const t=Number.isFinite(+s),r=o>=e.length;return s=!s&&P.isArray(i)?i.length:s,r?(P.hasOwnProp(i,s)?i[s]=[i[s],n]:i[s]=n,!t):(i[s]&&P.isObject(i[s])||(i[s]=[]),a(e,n,i[s],o)&&P.isArray(i[s])&&(i[s]=function(e){const a={},n=Object.keys(e);let i;const o=n.length;let s;for(i=0;i<o;i++)s=n[i],a[s]=e[s];return a}(i[s])),!t)}if(P.isFormData(e)&&P.isFunction(e.entries)){const n={};return P.forEachEntry(e,((e,i)=>{a(function(e){return P.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),i,n,0)})),n}return null},ce={transitional:ee,adapter:["xhr","http"],transformRequest:[function(e,a){const n=a.getContentType()||"",i=n.indexOf("application/json")>-1,o=P.isObject(e);if(o&&P.isHTMLForm(e)&&(e=new FormData(e)),P.isFormData(e))return i?JSON.stringify(re(e)):e;if(P.isArrayBuffer(e)||P.isBuffer(e)||P.isStream(e)||P.isFile(e)||P.isBlob(e))return e;if(P.isArrayBufferView(e))return e.buffer;if(P.isURLSearchParams(e))return a.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let s;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,a){return J(e,new te.classes.URLSearchParams,Object.assign({visitor:function(e,a,n,i){return te.isNode&&P.isBuffer(e)?(this.append(a,e.toString("base64")),!1):i.defaultVisitor.apply(this,arguments)}},a))}(e,this.formSerializer).toString();if((s=P.isFileList(e))||n.indexOf("multipart/form-data")>-1){const a=this.env&&this.env.FormData;return J(s?{"files[]":e}:e,a&&new a,this.formSerializer)}}return o||i?(a.setContentType("application/json",!1),function(e,a,n){if(P.isString(e))try{return(0,JSON.parse)(e),P.trim(e)}catch(e){if("SyntaxError"!==e.name)throw e}return(0,JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const a=this.transitional||ce.transitional,n=a&&a.forcedJSONParsing,i="json"===this.responseType;if(e&&P.isString(e)&&(n&&!this.responseType||i)){const n=!(a&&a.silentJSONParsing)&&i;try{return JSON.parse(e)}catch(e){if(n){if("SyntaxError"===e.name)throw U.from(e,U.ERR_BAD_RESPONSE,this,null,this.response);throw e}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:te.classes.FormData,Blob:te.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};P.forEach(["delete","get","head","post","put","patch"],(e=>{ce.headers[e]={}}));const pe=ce,le=P.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ue=Symbol("internals");function me(e){return e&&String(e).trim().toLowerCase()}function de(e){return!1===e||null==e?e:P.isArray(e)?e.map(de):String(e)}function fe(e,a,n,i,o){return P.isFunction(i)?i.call(this,a,n):(o&&(a=n),P.isString(a)?P.isString(i)?-1!==a.indexOf(i):P.isRegExp(i)?i.test(a):void 0:void 0)}class xe{constructor(e){e&&this.set(e)}set(e,a,n){const i=this;function o(e,a,n){const o=me(a);if(!o)throw new Error("header name must be a non-empty string");const s=P.findKey(i,o);(!s||void 0===i[s]||!0===n||void 0===n&&!1!==i[s])&&(i[s||a]=de(e))}const s=(e,a)=>P.forEach(e,((e,n)=>o(e,n,a)));return P.isPlainObject(e)||e instanceof this.constructor?s(e,a):P.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim())?s((e=>{const a={};let n,i,o;return e&&e.split("\n").forEach((function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),i=e.substring(o+1).trim(),!n||a[n]&&le[n]||("set-cookie"===n?a[n]?a[n].push(i):a[n]=[i]:a[n]=a[n]?a[n]+", "+i:i)})),a})(e),a):null!=e&&o(a,e,n),this}get(e,a){if(e=me(e)){const n=P.findKey(this,e);if(n){const e=this[n];if(!a)return e;if(!0===a)return function(e){const a=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let i;for(;i=n.exec(e);)a[i[1]]=i[2];return a}(e);if(P.isFunction(a))return a.call(this,e,n);if(P.isRegExp(a))return a.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,a){if(e=me(e)){const n=P.findKey(this,e);return!(!n||void 0===this[n]||a&&!fe(0,this[n],n,a))}return!1}delete(e,a){const n=this;let i=!1;function o(e){if(e=me(e)){const o=P.findKey(n,e);!o||a&&!fe(0,n[o],o,a)||(delete n[o],i=!0)}}return P.isArray(e)?e.forEach(o):o(e),i}clear(e){const a=Object.keys(this);let n=a.length,i=!1;for(;n--;){const o=a[n];e&&!fe(0,this[o],o,e,!0)||(delete this[o],i=!0)}return i}normalize(e){const a=this,n={};return P.forEach(this,((i,o)=>{const s=P.findKey(n,o);if(s)return a[s]=de(i),void delete a[o];const t=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,a,n)=>a.toUpperCase()+n))}(o):String(o).trim();t!==o&&delete a[o],a[t]=de(i),n[t]=!0})),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){const a=Object.create(null);return P.forEach(this,((n,i)=>{null!=n&&!1!==n&&(a[i]=e&&P.isArray(n)?n.join(", "):n)})),a}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((([e,a])=>e+": "+a)).join("\n")}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...a){const n=new this(e);return a.forEach((e=>n.set(e))),n}static accessor(e){const a=(this[ue]=this[ue]={accessors:{}}).accessors,n=this.prototype;function i(e){const i=me(e);a[i]||(function(e,a){const n=P.toCamelCase(" "+a);["get","set","has"].forEach((i=>{Object.defineProperty(e,i+n,{value:function(e,n,o){return this[i].call(this,a,e,n,o)},configurable:!0})}))}(n,e),a[i]=!0)}return P.isArray(e)?e.forEach(i):i(e),this}}xe.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),P.reduceDescriptors(xe.prototype,(({value:e},a)=>{let n=a[0].toUpperCase()+a.slice(1);return{get:()=>e,set(e){this[n]=e}}})),P.freezeMethods(xe);const he=xe;function ve(e,a){const n=this||pe,i=a||n,o=he.from(i.headers);let s=i.data;return P.forEach(e,(function(e){s=e.call(n,s,o.normalize(),a?a.status:void 0)})),o.normalize(),s}function be(e){return!(!e||!e.__CANCEL__)}function ge(e,a,n){U.call(this,null==e?"canceled":e,U.ERR_CANCELED,a,n),this.name="CanceledError"}P.inherits(ge,U,{__CANCEL__:!0});const ye=ge;function we(e,a,n){const i=n.config.validateStatus;n.status&&i&&!i(n.status)?a(new U("Request failed with status code "+n.status,[U.ERR_BAD_REQUEST,U.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}function ke(e,a){return e&&!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(a)?function(e,a){return a?e.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):e}(e,a):a}var Se=n(5574),je=n(8611),_e=n(5692),Ee=n(9023),Oe=n(8725);const Ce=require("zlib"),Te="1.6.7";function Re(e){const a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return a&&a[1]||""}const Fe=/^(?:([^;]+);)?(?:[^;]+;)?(base64|),([\s\S]*)$/;var Le=n(2203);const Ae=function(e,a){e=e||10;const n=new Array(e),i=new Array(e);let o,s=0,t=0;return a=void 0!==a?a:1e3,function(r){const c=Date.now(),p=i[t];o||(o=c),n[s]=r,i[s]=c;let l=t,u=0;for(;l!==s;)u+=n[l++],l%=e;if(s=(s+1)%e,s===t&&(t=(t+1)%e),c-o<a)return;const m=p&&c-p;return m?Math.round(1e3*u/m):void 0}},ze=Symbol("internals");class Ne extends Le.Transform{constructor(e){e=P.toFlatObject(e,{maxRate:0,chunkSize:65536,minChunkSize:100,timeWindow:500,ticksRate:2,samplesCount:15},null,((e,a)=>!P.isUndefined(a[e]))),super({readableHighWaterMark:e.chunkSize});const a=this,n=this[ze]={length:e.length,timeWindow:e.timeWindow,ticksRate:e.ticksRate,chunkSize:e.chunkSize,maxRate:e.maxRate,minChunkSize:e.minChunkSize,bytesSeen:0,isCaptured:!1,notifiedBytesLoaded:0,ts:Date.now(),bytes:0,onReadCallback:null},i=Ae(n.ticksRate*e.samplesCount,n.timeWindow);this.on("newListener",(e=>{"progress"===e&&(n.isCaptured||(n.isCaptured=!0))}));let o=0;n.updateProgress=function(e,a){let n=0;const i=1e3/a;let o=null;return function(a,s){const t=Date.now();if(a||t-n>i)return o&&(clearTimeout(o),o=null),n=t,e.apply(null,s);o||(o=setTimeout((()=>(o=null,n=Date.now(),e.apply(null,s))),i-(t-n)))}}((function(){const e=n.length,s=n.bytesSeen,t=s-o;if(!t||a.destroyed)return;const r=i(t);o=s,process.nextTick((()=>{a.emit("progress",{loaded:s,total:e,progress:e?s/e:void 0,bytes:t,rate:r||void 0,estimated:r&&e&&s<=e?(e-s)/r:void 0})}))}),n.ticksRate);const s=()=>{n.updateProgress(!0)};this.once("end",s),this.once("error",s)}_read(e){const a=this[ze];return a.onReadCallback&&a.onReadCallback(),super._read(e)}_transform(e,a,n){const i=this,o=this[ze],s=o.maxRate,t=this.readableHighWaterMark,r=o.timeWindow,c=s/(1e3/r),p=!1!==o.minChunkSize?Math.max(o.minChunkSize,.01*c):0,l=(e,a)=>{const n=Buffer.byteLength(e);let l,u=null,m=t,d=0;if(s){const e=Date.now();(!o.ts||(d=e-o.ts)>=r)&&(o.ts=e,l=c-o.bytes,o.bytes=l<0?-l:0,d=0),l=c-o.bytes}if(s){if(l<=0)return setTimeout((()=>{a(null,e)}),r-d);l<m&&(m=l)}m&&n>m&&n-m>p&&(u=e.subarray(m),e=e.subarray(0,m)),function(e,a){const n=Buffer.byteLength(e);o.bytesSeen+=n,o.bytes+=n,o.isCaptured&&o.updateProgress(),i.push(e)?process.nextTick(a):o.onReadCallback=()=>{o.onReadCallback=null,process.nextTick(a)}}(e,u?()=>{process.nextTick(a,null,u)}:a)};l(e,(function e(a,i){if(a)return n(a);i?l(i,e):n(null)}))}setLength(e){return this[ze].length=+e,this}}const Pe=Ne,Be=require("events"),{asyncIterator:De}=Symbol,qe=async function*(e){e.stream?yield*e.stream():e.arrayBuffer?yield await e.arrayBuffer():e[De]?yield*e[De]():yield e},Ue=P.ALPHABET.ALPHA_DIGIT+"-_",Ie=new Ee.TextEncoder,Me="\r\n",$e=Ie.encode(Me);class We{constructor(e,a){const{escapeName:n}=this.constructor,i=P.isString(a);let o=`Content-Disposition: form-data; name="${n(e)}"${!i&&a.name?`; filename="${n(a.name)}"`:""}${Me}`;i?a=Ie.encode(String(a).replace(/\r?\n|\r\n?/g,Me)):o+=`Content-Type: ${a.type||"application/octet-stream"}${Me}`,this.headers=Ie.encode(o+Me),this.contentLength=i?a.byteLength:a.size,this.size=this.headers.byteLength+this.contentLength+2,this.name=e,this.value=a}async*encode(){yield this.headers;const{value:e}=this;P.isTypedArray(e)?yield e:yield*qe(e),yield $e}static escapeName(e){return String(e).replace(/[\r\n"]/g,(e=>({"\r":"%0D","\n":"%0A",'"':"%22"}[e])))}}class He extends Le.Transform{__transform(e,a,n){this.push(e),n()}_transform(e,a,n){if(0!==e.length&&(this._transform=this.__transform,120!==e[0])){const e=Buffer.alloc(2);e[0]=120,e[1]=156,this.push(e,a)}this.__transform(e,a,n)}}const Je=He,Ge=(e,a)=>P.isAsyncFn(e)?function(...n){const i=n.pop();e.apply(this,n).then((e=>{try{a?i(null,...a(e)):i(null,e)}catch(e){i(e)}}),i)}:e,Ve={flush:Ce.constants.Z_SYNC_FLUSH,finishFlush:Ce.constants.Z_SYNC_FLUSH},Ke={flush:Ce.constants.BROTLI_OPERATION_FLUSH,finishFlush:Ce.constants.BROTLI_OPERATION_FLUSH},Ye=P.isFunction(Ce.createBrotliDecompress),{http:Xe,https:Qe}=Oe,Ze=/https:?/,ea=te.protocols.map((e=>e+":"));function aa(e,a){e.beforeRedirects.proxy&&e.beforeRedirects.proxy(e),e.beforeRedirects.config&&e.beforeRedirects.config(e,a)}function na(e,a,n){let i=a;if(!i&&!1!==i){const e=(0,Se.T)(n);e&&(i=new URL(e))}if(i){if(i.username&&(i.auth=(i.username||"")+":"+(i.password||"")),i.auth){(i.auth.username||i.auth.password)&&(i.auth=(i.auth.username||"")+":"+(i.auth.password||""));const a=Buffer.from(i.auth,"utf8").toString("base64");e.headers["Proxy-Authorization"]="Basic "+a}e.headers.host=e.hostname+(e.port?":"+e.port:"");const a=i.hostname||i.host;e.hostname=a,e.host=a,e.port=i.port,e.path=n,i.protocol&&(e.protocol=i.protocol.includes(":")?i.protocol:`${i.protocol}:`)}e.beforeRedirects.proxy=function(e){na(e,a,e.href)}}const ia="undefined"!=typeof process&&"process"===P.kindOf(process),oa=(e,a)=>(({address:e,family:a})=>{if(!P.isString(e))throw TypeError("address must be a string");return{address:e,family:a||(e.indexOf(".")<0?6:4)}})(P.isObject(e)?e:{address:e,family:a}),sa=ia&&function(e){return a=async function(a,n,i){let{data:o,lookup:s,family:t}=e;const{responseType:r,responseEncoding:c}=e,p=e.method.toUpperCase();let l,u,m=!1;if(s){const e=Ge(s,(e=>P.isArray(e)?e:[e]));s=(a,n,i)=>{e(a,n,((e,a,o)=>{if(e)return i(e);const s=P.isArray(a)?a.map((e=>oa(e))):[oa(a,o)];n.all?i(e,s):i(e,s[0].address,s[0].family)}))}}const d=new Be,f=()=>{e.cancelToken&&e.cancelToken.unsubscribe(x),e.signal&&e.signal.removeEventListener("abort",x),d.removeAllListeners()};function x(a){d.emit("abort",!a||a.type?new ye(null,e,u):a)}i(((e,a)=>{l=!0,a&&(m=!0,f())})),d.once("abort",n),(e.cancelToken||e.signal)&&(e.cancelToken&&e.cancelToken.subscribe(x),e.signal&&(e.signal.aborted?x():e.signal.addEventListener("abort",x)));const h=ke(e.baseURL,e.url),v=new URL(h,"http://localhost"),b=v.protocol||ea[0];if("data:"===b){let i;if("GET"!==p)return we(a,n,{status:405,statusText:"method not allowed",headers:{},config:e});try{i=function(e,a,n){const i=n&&n.Blob||te.classes.Blob,o=Re(e);if(void 0===a&&i&&(a=!0),"data"===o){e=o.length?e.slice(o.length+1):e;const n=Fe.exec(e);if(!n)throw new U("Invalid URL",U.ERR_INVALID_URL);const s=n[1],t=n[2],r=n[3],c=Buffer.from(decodeURIComponent(r),t?"base64":"utf8");if(a){if(!i)throw new U("Blob is not supported",U.ERR_NOT_SUPPORT);return new i([c],{type:s})}return c}throw new U("Unsupported protocol "+o,U.ERR_NOT_SUPPORT)}(e.url,"blob"===r,{Blob:e.env&&e.env.Blob})}catch(a){throw U.from(a,U.ERR_BAD_REQUEST,e)}return"text"===r?(i=i.toString(c),c&&"utf8"!==c||(i=P.stripBOM(i))):"stream"===r&&(i=Le.Readable.from(i)),we(a,n,{data:i,status:200,statusText:"OK",headers:new he,config:e})}if(-1===ea.indexOf(b))return n(new U("Unsupported protocol "+b,U.ERR_BAD_REQUEST,e));const g=he.from(e.headers).normalize();g.set("User-Agent","axios/"+Te,!1);const y=e.onDownloadProgress,w=e.onUploadProgress,k=e.maxRate;let S,j;if(P.isSpecCompliantForm(o)){const e=g.getContentType(/boundary=([-_\w\d]{10,70})/i);o=((e,a,n)=>{const{tag:i="form-data-boundary",size:o=25,boundary:s=i+"-"+P.generateString(o,Ue)}=n||{};if(!P.isFormData(e))throw TypeError("FormData instance required");if(s.length<1||s.length>70)throw Error("boundary must be 10-70 characters long");const t=Ie.encode("--"+s+Me),r=Ie.encode("--"+s+"--"+Me+Me);let c=r.byteLength;const p=Array.from(e.entries()).map((([e,a])=>{const n=new We(e,a);return c+=n.size,n}));c+=t.byteLength*p.length,c=P.toFiniteNumber(c);const l={"Content-Type":`multipart/form-data; boundary=${s}`};return Number.isFinite(c)&&(l["Content-Length"]=c),a&&a(l),Le.Readable.from(async function*(){for(const e of p)yield t,yield*e.encode();yield r}())})(o,(e=>{g.set(e)}),{tag:`axios-${Te}-boundary`,boundary:e&&e[1]||void 0})}else if(P.isFormData(o)&&P.isFunction(o.getHeaders)){if(g.set(o.getHeaders()),!g.hasContentLength())try{const e=await Ee.promisify(o.getLength).call(o);Number.isFinite(e)&&e>=0&&g.setContentLength(e)}catch(e){}}else if(P.isBlob(o))o.size&&g.setContentType(o.type||"application/octet-stream"),g.setContentLength(o.size||0),o=Le.Readable.from(qe(o));else if(o&&!P.isStream(o)){if(Buffer.isBuffer(o));else if(P.isArrayBuffer(o))o=Buffer.from(new Uint8Array(o));else{if(!P.isString(o))return n(new U("Data after transformation must be a string, an ArrayBuffer, a Buffer, or a Stream",U.ERR_BAD_REQUEST,e));o=Buffer.from(o,"utf-8")}if(g.setContentLength(o.length,!1),e.maxBodyLength>-1&&o.length>e.maxBodyLength)return n(new U("Request body larger than maxBodyLength limit",U.ERR_BAD_REQUEST,e))}const _=P.toFiniteNumber(g.getContentLength());let E,O;P.isArray(k)?(S=k[0],j=k[1]):S=j=k,o&&(w||S)&&(P.isStream(o)||(o=Le.Readable.from(o,{objectMode:!1})),o=Le.pipeline([o,new Pe({length:_,maxRate:P.toFiniteNumber(S)})],P.noop),w&&o.on("progress",(e=>{w(Object.assign(e,{upload:!0}))}))),e.auth&&(E=(e.auth.username||"")+":"+(e.auth.password||"")),!E&&v.username&&(E=v.username+":"+v.password),E&&g.delete("authorization");try{O=Q(v.pathname+v.search,e.params,e.paramsSerializer).replace(/^\?/,"")}catch(a){const i=new Error(a.message);return i.config=e,i.url=e.url,i.exists=!0,n(i)}g.set("Accept-Encoding","gzip, compress, deflate"+(Ye?", br":""),!1);const C={path:O,method:p,headers:g.toJSON(),agents:{http:e.httpAgent,https:e.httpsAgent},auth:E,protocol:b,family:t,beforeRedirect:aa,beforeRedirects:{}};let T;!P.isUndefined(s)&&(C.lookup=s),e.socketPath?C.socketPath=e.socketPath:(C.hostname=v.hostname,C.port=v.port,na(C,e.proxy,b+"//"+v.hostname+(v.port?":"+v.port:"")+C.path));const R=Ze.test(C.protocol);if(C.agent=R?e.httpsAgent:e.httpAgent,e.transport?T=e.transport:0===e.maxRedirects?T=R?_e:je:(e.maxRedirects&&(C.maxRedirects=e.maxRedirects),e.beforeRedirect&&(C.beforeRedirects.config=e.beforeRedirect),T=R?Qe:Xe),e.maxBodyLength>-1?C.maxBodyLength=e.maxBodyLength:C.maxBodyLength=1/0,e.insecureHTTPParser&&(C.insecureHTTPParser=e.insecureHTTPParser),u=T.request(C,(function(i){if(u.destroyed)return;const o=[i],s=+i.headers["content-length"];if(y){const e=new Pe({length:P.toFiniteNumber(s),maxRate:P.toFiniteNumber(j)});y&&e.on("progress",(e=>{y(Object.assign(e,{download:!0}))})),o.push(e)}let t=i;const l=i.req||u;if(!1!==e.decompress&&i.headers["content-encoding"])switch("HEAD"!==p&&204!==i.statusCode||delete i.headers["content-encoding"],(i.headers["content-encoding"]||"").toLowerCase()){case"gzip":case"x-gzip":case"compress":case"x-compress":o.push(Ce.createUnzip(Ve)),delete i.headers["content-encoding"];break;case"deflate":o.push(new Je),o.push(Ce.createUnzip(Ve)),delete i.headers["content-encoding"];break;case"br":Ye&&(o.push(Ce.createBrotliDecompress(Ke)),delete i.headers["content-encoding"])}t=o.length>1?Le.pipeline(o,P.noop):o[0];const x=Le.finished(t,(()=>{x(),f()})),h={status:i.statusCode,statusText:i.statusMessage,headers:new he(i.headers),config:e,request:l};if("stream"===r)h.data=t,we(a,n,h);else{const i=[];let o=0;t.on("data",(function(a){i.push(a),o+=a.length,e.maxContentLength>-1&&o>e.maxContentLength&&(m=!0,t.destroy(),n(new U("maxContentLength size of "+e.maxContentLength+" exceeded",U.ERR_BAD_RESPONSE,e,l)))})),t.on("aborted",(function(){if(m)return;const a=new U("maxContentLength size of "+e.maxContentLength+" exceeded",U.ERR_BAD_RESPONSE,e,l);t.destroy(a),n(a)})),t.on("error",(function(a){u.destroyed||n(U.from(a,null,e,l))})),t.on("end",(function(){try{let e=1===i.length?i[0]:Buffer.concat(i);"arraybuffer"!==r&&(e=e.toString(c),c&&"utf8"!==c||(e=P.stripBOM(e))),h.data=e}catch(a){return n(U.from(a,null,e,h.request,h))}we(a,n,h)}))}d.once("abort",(e=>{t.destroyed||(t.emit("error",e),t.destroy())}))})),d.once("abort",(e=>{n(e),u.destroy(e)})),u.on("error",(function(a){n(U.from(a,null,e,u))})),u.on("socket",(function(e){e.setKeepAlive(!0,6e4)})),e.timeout){const a=parseInt(e.timeout,10);if(Number.isNaN(a))return void n(new U("error trying to parse `config.timeout` to int",U.ERR_BAD_OPTION_VALUE,e,u));u.setTimeout(a,(function(){if(l)return;let a=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const i=e.transitional||ee;e.timeoutErrorMessage&&(a=e.timeoutErrorMessage),n(new U(a,i.clarifyTimeoutError?U.ETIMEDOUT:U.ECONNABORTED,e,u)),x()}))}if(P.isStream(o)){let a=!1,n=!1;o.on("end",(()=>{a=!0})),o.once("error",(e=>{n=!0,u.destroy(e)})),o.on("close",(()=>{a||n||x(new ye("Request stream has been aborted",e,u))})),o.pipe(u)}else u.end(o)},new Promise(((e,n)=>{let i,o;const s=(e,a)=>{o||(o=!0,i&&i(e,a))},t=e=>{s(e,!0),n(e)};a((a=>{s(a),e(a)}),t,(e=>i=e)).catch(t)}));var a},ta=te.hasStandardBrowserEnv?{write(e,a,n,i,o,s){const t=[e+"="+encodeURIComponent(a)];P.isNumber(n)&&t.push("expires="+new Date(n).toGMTString()),P.isString(i)&&t.push("path="+i),P.isString(o)&&t.push("domain="+o),!0===s&&t.push("secure"),document.cookie=t.join("; ")},read(e){const a=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}},ra=te.hasStandardBrowserEnv?function(){const e=/(msie|trident)/i.test(navigator.userAgent),a=document.createElement("a");let n;function i(n){let i=n;return e&&(a.setAttribute("href",i),i=a.href),a.setAttribute("href",i),{href:a.href,protocol:a.protocol?a.protocol.replace(/:$/,""):"",host:a.host,search:a.search?a.search.replace(/^\?/,""):"",hash:a.hash?a.hash.replace(/^#/,""):"",hostname:a.hostname,port:a.port,pathname:"/"===a.pathname.charAt(0)?a.pathname:"/"+a.pathname}}return n=i(window.location.href),function(e){const a=P.isString(e)?i(e):e;return a.protocol===n.protocol&&a.host===n.host}}():function(){return!0};function ca(e,a){let n=0;const i=Ae(50,250);return o=>{const s=o.loaded,t=o.lengthComputable?o.total:void 0,r=s-n,c=i(r);n=s;const p={loaded:s,total:t,progress:t?s/t:void 0,bytes:r,rate:c||void 0,estimated:c&&t&&s<=t?(t-s)/c:void 0,event:o};p[a?"download":"upload"]=!0,e(p)}}const pa={http:sa,xhr:"undefined"!=typeof XMLHttpRequest&&function(e){return new Promise((function(a,n){let i=e.data;const o=he.from(e.headers).normalize();let s,t,{responseType:r,withXSRFToken:c}=e;function p(){e.cancelToken&&e.cancelToken.unsubscribe(s),e.signal&&e.signal.removeEventListener("abort",s)}if(P.isFormData(i))if(te.hasStandardBrowserEnv||te.hasStandardBrowserWebWorkerEnv)o.setContentType(!1);else if(!1!==(t=o.getContentType())){const[e,...a]=t?t.split(";").map((e=>e.trim())).filter(Boolean):[];o.setContentType([e||"multipart/form-data",...a].join("; "))}let l=new XMLHttpRequest;if(e.auth){const a=e.auth.username||"",n=e.auth.password?unescape(encodeURIComponent(e.auth.password)):"";o.set("Authorization","Basic "+btoa(a+":"+n))}const u=ke(e.baseURL,e.url);function m(){if(!l)return;const i=he.from("getAllResponseHeaders"in l&&l.getAllResponseHeaders());we((function(e){a(e),p()}),(function(e){n(e),p()}),{data:r&&"text"!==r&&"json"!==r?l.response:l.responseText,status:l.status,statusText:l.statusText,headers:i,config:e,request:l}),l=null}if(l.open(e.method.toUpperCase(),Q(u,e.params,e.paramsSerializer),!0),l.timeout=e.timeout,"onloadend"in l?l.onloadend=m:l.onreadystatechange=function(){l&&4===l.readyState&&(0!==l.status||l.responseURL&&0===l.responseURL.indexOf("file:"))&&setTimeout(m)},l.onabort=function(){l&&(n(new U("Request aborted",U.ECONNABORTED,e,l)),l=null)},l.onerror=function(){n(new U("Network Error",U.ERR_NETWORK,e,l)),l=null},l.ontimeout=function(){let a=e.timeout?"timeout of "+e.timeout+"ms exceeded":"timeout exceeded";const i=e.transitional||ee;e.timeoutErrorMessage&&(a=e.timeoutErrorMessage),n(new U(a,i.clarifyTimeoutError?U.ETIMEDOUT:U.ECONNABORTED,e,l)),l=null},te.hasStandardBrowserEnv&&(c&&P.isFunction(c)&&(c=c(e)),c||!1!==c&&ra(u))){const a=e.xsrfHeaderName&&e.xsrfCookieName&&ta.read(e.xsrfCookieName);a&&o.set(e.xsrfHeaderName,a)}void 0===i&&o.setContentType(null),"setRequestHeader"in l&&P.forEach(o.toJSON(),(function(e,a){l.setRequestHeader(a,e)})),P.isUndefined(e.withCredentials)||(l.withCredentials=!!e.withCredentials),r&&"json"!==r&&(l.responseType=e.responseType),"function"==typeof e.onDownloadProgress&&l.addEventListener("progress",ca(e.onDownloadProgress,!0)),"function"==typeof e.onUploadProgress&&l.upload&&l.upload.addEventListener("progress",ca(e.onUploadProgress)),(e.cancelToken||e.signal)&&(s=a=>{l&&(n(!a||a.type?new ye(null,e,l):a),l.abort(),l=null)},e.cancelToken&&e.cancelToken.subscribe(s),e.signal&&(e.signal.aborted?s():e.signal.addEventListener("abort",s)));const d=Re(u);d&&-1===te.protocols.indexOf(d)?n(new U("Unsupported protocol "+d+":",U.ERR_BAD_REQUEST,e)):l.send(i||null)}))}};P.forEach(pa,((e,a)=>{if(e){try{Object.defineProperty(e,"name",{value:a})}catch(e){}Object.defineProperty(e,"adapterName",{value:a})}}));const la=e=>`- ${e}`,ua=e=>P.isFunction(e)||null===e||!1===e,ma=e=>{e=P.isArray(e)?e:[e];const{length:a}=e;let n,i;const o={};for(let s=0;s<a;s++){let a;if(n=e[s],i=n,!ua(n)&&(i=pa[(a=String(n)).toLowerCase()],void 0===i))throw new U(`Unknown adapter '${a}'`);if(i)break;o[a||"#"+s]=i}if(!i){const e=Object.entries(o).map((([e,a])=>`adapter ${e} `+(!1===a?"is not supported by the environment":"is not available in the build")));let n=a?e.length>1?"since :\n"+e.map(la).join("\n"):" "+la(e[0]):"as no adapter specified";throw new U("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return i};function da(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ye(null,e)}function fa(e){return da(e),e.headers=he.from(e.headers),e.data=ve.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1),ma(e.adapter||pe.adapter)(e).then((function(a){return da(e),a.data=ve.call(e,e.transformResponse,a),a.headers=he.from(a.headers),a}),(function(a){return be(a)||(da(e),a&&a.response&&(a.response.data=ve.call(e,e.transformResponse,a.response),a.response.headers=he.from(a.response.headers))),Promise.reject(a)}))}const xa=e=>e instanceof he?e.toJSON():e;function ha(e,a){a=a||{};const n={};function i(e,a,n){return P.isPlainObject(e)&&P.isPlainObject(a)?P.merge.call({caseless:n},e,a):P.isPlainObject(a)?P.merge({},a):P.isArray(a)?a.slice():a}function o(e,a,n){return P.isUndefined(a)?P.isUndefined(e)?void 0:i(void 0,e,n):i(e,a,n)}function s(e,a){if(!P.isUndefined(a))return i(void 0,a)}function t(e,a){return P.isUndefined(a)?P.isUndefined(e)?void 0:i(void 0,e):i(void 0,a)}function r(n,o,s){return s in a?i(n,o):s in e?i(void 0,n):void 0}const c={url:s,method:s,data:s,baseURL:t,transformRequest:t,transformResponse:t,paramsSerializer:t,timeout:t,timeoutMessage:t,withCredentials:t,withXSRFToken:t,adapter:t,responseType:t,xsrfCookieName:t,xsrfHeaderName:t,onUploadProgress:t,onDownloadProgress:t,decompress:t,maxContentLength:t,maxBodyLength:t,beforeRedirect:t,transport:t,httpAgent:t,httpsAgent:t,cancelToken:t,socketPath:t,responseEncoding:t,validateStatus:r,headers:(e,a)=>o(xa(e),xa(a),!0)};return P.forEach(Object.keys(Object.assign({},e,a)),(function(i){const s=c[i]||o,t=s(e[i],a[i],i);P.isUndefined(t)&&s!==r||(n[i]=t)})),n}const va={};["object","boolean","number","function","string","symbol"].forEach(((e,a)=>{va[e]=function(n){return typeof n===e||"a"+(a<1?"n ":" ")+e}}));const ba={};va.transitional=function(e,a,n){function i(e,a){return"[Axios v1.6.7] Transitional option '"+e+"'"+a+(n?". "+n:"")}return(n,o,s)=>{if(!1===e)throw new U(i(o," has been removed"+(a?" in "+a:"")),U.ERR_DEPRECATED);return a&&!ba[o]&&(ba[o]=!0,console.warn(i(o," has been deprecated since v"+a+" and will be removed in the near future"))),!e||e(n,o,s)}};const ga={assertOptions:function(e,a,n){if("object"!=typeof e)throw new U("options must be an object",U.ERR_BAD_OPTION_VALUE);const i=Object.keys(e);let o=i.length;for(;o-- >0;){const s=i[o],t=a[s];if(t){const a=e[s],n=void 0===a||t(a,s,e);if(!0!==n)throw new U("option "+s+" must be "+n,U.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new U("Unknown option "+s,U.ERR_BAD_OPTION)}},validators:va},ya=ga.validators;class wa{constructor(e){this.defaults=e,this.interceptors={request:new Z,response:new Z}}async request(e,a){try{return await this._request(e,a)}catch(e){if(e instanceof Error){let a;Error.captureStackTrace?Error.captureStackTrace(a={}):a=new Error;const n=a.stack?a.stack.replace(/^.+\n/,""):"";e.stack?n&&!String(e.stack).endsWith(n.replace(/^.+\n.+\n/,""))&&(e.stack+="\n"+n):e.stack=n}throw e}}_request(e,a){"string"==typeof e?(a=a||{}).url=e:a=e||{},a=ha(this.defaults,a);const{transitional:n,paramsSerializer:i,headers:o}=a;void 0!==n&&ga.assertOptions(n,{silentJSONParsing:ya.transitional(ya.boolean),forcedJSONParsing:ya.transitional(ya.boolean),clarifyTimeoutError:ya.transitional(ya.boolean)},!1),null!=i&&(P.isFunction(i)?a.paramsSerializer={serialize:i}:ga.assertOptions(i,{encode:ya.function,serialize:ya.function},!0)),a.method=(a.method||this.defaults.method||"get").toLowerCase();let s=o&&P.merge(o.common,o[a.method]);o&&P.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete o[e]})),a.headers=he.concat(s,o);const t=[];let r=!0;this.interceptors.request.forEach((function(e){"function"==typeof e.runWhen&&!1===e.runWhen(a)||(r=r&&e.synchronous,t.unshift(e.fulfilled,e.rejected))}));const c=[];let p;this.interceptors.response.forEach((function(e){c.push(e.fulfilled,e.rejected)}));let l,u=0;if(!r){const e=[fa.bind(this),void 0];for(e.unshift.apply(e,t),e.push.apply(e,c),l=e.length,p=Promise.resolve(a);u<l;)p=p.then(e[u++],e[u++]);return p}l=t.length;let m=a;for(u=0;u<l;){const e=t[u++],a=t[u++];try{m=e(m)}catch(e){a.call(this,e);break}}try{p=fa.call(this,m)}catch(e){return Promise.reject(e)}for(u=0,l=c.length;u<l;)p=p.then(c[u++],c[u++]);return p}getUri(e){return Q(ke((e=ha(this.defaults,e)).baseURL,e.url),e.params,e.paramsSerializer)}}P.forEach(["delete","get","head","options"],(function(e){wa.prototype[e]=function(a,n){return this.request(ha(n||{},{method:e,url:a,data:(n||{}).data}))}})),P.forEach(["post","put","patch"],(function(e){function a(a){return function(n,i,o){return this.request(ha(o||{},{method:e,headers:a?{"Content-Type":"multipart/form-data"}:{},url:n,data:i}))}}wa.prototype[e]=a(),wa.prototype[e+"Form"]=a(!0)}));const ka=wa;class Sa{constructor(e){if("function"!=typeof e)throw new TypeError("executor must be a function.");let a;this.promise=new Promise((function(e){a=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let a=n._listeners.length;for(;a-- >0;)n._listeners[a](e);n._listeners=null})),this.promise.then=e=>{let a;const i=new Promise((e=>{n.subscribe(e),a=e})).then(e);return i.cancel=function(){n.unsubscribe(a)},i},e((function(e,i,o){n.reason||(n.reason=new ye(e,i,o),a(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const a=this._listeners.indexOf(e);-1!==a&&this._listeners.splice(a,1)}static source(){let e;return{token:new Sa((function(a){e=a})),cancel:e}}}const ja=Sa,_a={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(_a).forEach((([e,a])=>{_a[a]=e}));const Ea=_a,Oa=function e(n){const i=new ka(n),o=a(ka.prototype.request,i);return P.extend(o,ka.prototype,i,{allOwnKeys:!0}),P.extend(o,i,null,{allOwnKeys:!0}),o.create=function(a){return e(ha(n,a))},o}(pe);Oa.Axios=ka,Oa.CanceledError=ye,Oa.CancelToken=ja,Oa.isCancel=be,Oa.VERSION=Te,Oa.toFormData=J,Oa.AxiosError=U,Oa.Cancel=Oa.CanceledError,Oa.all=function(e){return Promise.all(e)},Oa.spread=function(e){return function(a){return e.apply(null,a)}},Oa.isAxiosError=function(e){return P.isObject(e)&&!0===e.isAxiosError},Oa.mergeConfig=ha,Oa.AxiosHeaders=he,Oa.formToJSON=e=>re(P.isHTMLForm(e)?new FormData(e):e),Oa.getAdapter=ma,Oa.HttpStatusCode=Ea,Oa.default=Oa;const Ca=Oa;var Ta=n(6474),Ra=n.n(Ta),Fa=n(5643),La=n.n(Fa),Aa=n(6928),za=n.n(Aa);const Na=(e=0)=>a=>`[${a+e}m`,Pa=(e=0)=>a=>`[${38+e};5;${a}m`,Ba=(e=0)=>(a,n,i)=>`[${38+e};2;${a};${n};${i}m`,Da={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],overline:[53,55],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],gray:[90,39],grey:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgGray:[100,49],bgGrey:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};Object.keys(Da.modifier),Object.keys(Da.color),Object.keys(Da.bgColor);const qa=function(){const e=new Map;for(const[a,n]of Object.entries(Da)){for(const[a,i]of Object.entries(n))Da[a]={open:`[${i[0]}m`,close:`[${i[1]}m`},n[a]=Da[a],e.set(i[0],i[1]);Object.defineProperty(Da,a,{value:n,enumerable:!1})}return Object.defineProperty(Da,"codes",{value:e,enumerable:!1}),Da.color.close="[39m",Da.bgColor.close="[49m",Da.color.ansi=Na(),Da.color.ansi256=Pa(),Da.color.ansi16m=Ba(),Da.bgColor.ansi=Na(10),Da.bgColor.ansi256=Pa(10),Da.bgColor.ansi16m=Ba(10),Object.defineProperties(Da,{rgbToAnsi256:{value:(e,a,n)=>e===a&&a===n?e<8?16:e>248?231:Math.round((e-8)/247*24)+232:16+36*Math.round(e/255*5)+6*Math.round(a/255*5)+Math.round(n/255*5),enumerable:!1},hexToRgb:{value(e){const a=/[a-f\d]{6}|[a-f\d]{3}/i.exec(e.toString(16));if(!a)return[0,0,0];let[n]=a;3===n.length&&(n=[...n].map((e=>e+e)).join(""));const i=Number.parseInt(n,16);return[i>>16&255,i>>8&255,255&i]},enumerable:!1},hexToAnsi256:{value:e=>Da.rgbToAnsi256(...Da.hexToRgb(e)),enumerable:!1},ansi256ToAnsi:{value(e){if(e<8)return 30+e;if(e<16)return e-8+90;let a,n,i;if(e>=232)a=(10*(e-232)+8)/255,n=a,i=a;else{const o=(e-=16)%36;a=Math.floor(e/36)/5,n=Math.floor(o/6)/5,i=o%6/5}const o=2*Math.max(a,n,i);if(0===o)return 30;let s=30+(Math.round(i)<<2|Math.round(n)<<1|Math.round(a));return 2===o&&(s+=60),s},enumerable:!1},rgbToAnsi:{value:(e,a,n)=>Da.ansi256ToAnsi(Da.rgbToAnsi256(e,a,n)),enumerable:!1},hexToAnsi:{value:e=>Da.ansi256ToAnsi(Da.hexToAnsi256(e)),enumerable:!1}}),Da}(),Ua=require("node:process"),Ia=require("node:os"),Ma=require("node:tty");function $a(e,a=(globalThis.Deno?globalThis.Deno.args:Ua.argv)){const n=e.startsWith("-")?"":1===e.length?"-":"--",i=a.indexOf(n+e),o=a.indexOf("--");return-1!==i&&(-1===o||i<o)}const{env:Wa}=Ua;let Ha;function Ja(e,a={}){return 0!==(n=function(e,{streamIsTTY:a,sniffFlags:n=!0}={}){const i=function(){if("FORCE_COLOR"in Wa)return"true"===Wa.FORCE_COLOR?1:"false"===Wa.FORCE_COLOR?0:0===Wa.FORCE_COLOR.length?1:Math.min(Number.parseInt(Wa.FORCE_COLOR,10),3)}();void 0!==i&&(Ha=i);const o=n?Ha:i;if(0===o)return 0;if(n){if($a("color=16m")||$a("color=full")||$a("color=truecolor"))return 3;if($a("color=256"))return 2}if("TF_BUILD"in Wa&&"AGENT_NAME"in Wa)return 1;if(e&&!a&&void 0===o)return 0;const s=o||0;if("dumb"===Wa.TERM)return s;if("win32"===Ua.platform){const e=Ia.release().split(".");return Number(e[0])>=10&&Number(e[2])>=10586?Number(e[2])>=14931?3:2:1}if("CI"in Wa)return"GITHUB_ACTIONS"in Wa||"GITEA_ACTIONS"in Wa?3:["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI","BUILDKITE","DRONE"].some((e=>e in Wa))||"codeship"===Wa.CI_NAME?1:s;if("TEAMCITY_VERSION"in Wa)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(Wa.TEAMCITY_VERSION)?1:0;if("truecolor"===Wa.COLORTERM)return 3;if("xterm-kitty"===Wa.TERM)return 3;if("TERM_PROGRAM"in Wa){const e=Number.parseInt((Wa.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(Wa.TERM_PROGRAM){case"iTerm.app":return e>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(Wa.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(Wa.TERM)||"COLORTERM"in Wa?1:s}(e,{streamIsTTY:e&&e.isTTY,...a}))&&{level:n,hasBasic:!0,has256:n>=2,has16m:n>=3};var n}$a("no-color")||$a("no-colors")||$a("color=false")||$a("color=never")?Ha=0:($a("color")||$a("colors")||$a("color=true")||$a("color=always"))&&(Ha=1);const Ga={stdout:Ja({isTTY:Ma.isatty(1)}),stderr:Ja({isTTY:Ma.isatty(2)})};function Va(e,a,n){let i=e.indexOf(a);if(-1===i)return e;const o=a.length;let s=0,t="";do{t+=e.slice(s,i)+a+n,s=i+o,i=e.indexOf(a,s)}while(-1!==i);return t+=e.slice(s),t}const{stdout:Ka,stderr:Ya}=Ga,Xa=Symbol("GENERATOR"),Qa=Symbol("STYLER"),Za=Symbol("IS_EMPTY"),en=["ansi","ansi","ansi256","ansi16m"],an=Object.create(null);function nn(e){return(e=>{const a=(...e)=>e.join(" ");return((e,a={})=>{if(a.level&&!(Number.isInteger(a.level)&&a.level>=0&&a.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");const n=Ka?Ka.level:0;e.level=void 0===a.level?n:a.level})(a,e),Object.setPrototypeOf(a,nn.prototype),a})(e)}Object.setPrototypeOf(nn.prototype,Function.prototype);for(const[e,a]of Object.entries(qa))an[e]={get(){const n=cn(this,rn(a.open,a.close,this[Qa]),this[Za]);return Object.defineProperty(this,e,{value:n}),n}};an.visible={get(){const e=cn(this,this[Qa],!0);return Object.defineProperty(this,"visible",{value:e}),e}};const on=(e,a,n,...i)=>"rgb"===e?"ansi16m"===a?qa[n].ansi16m(...i):"ansi256"===a?qa[n].ansi256(qa.rgbToAnsi256(...i)):qa[n].ansi(qa.rgbToAnsi(...i)):"hex"===e?on("rgb",a,n,...qa.hexToRgb(...i)):qa[n][e](...i),sn=["rgb","hex","ansi256"];for(const e of sn)an[e]={get(){const{level:a}=this;return function(...n){const i=rn(on(e,en[a],"color",...n),qa.color.close,this[Qa]);return cn(this,i,this[Za])}}},an["bg"+e[0].toUpperCase()+e.slice(1)]={get(){const{level:a}=this;return function(...n){const i=rn(on(e,en[a],"bgColor",...n),qa.bgColor.close,this[Qa]);return cn(this,i,this[Za])}}};const tn=Object.defineProperties((()=>{}),{...an,level:{enumerable:!0,get(){return this[Xa].level},set(e){this[Xa].level=e}}}),rn=(e,a,n)=>{let i,o;return void 0===n?(i=e,o=a):(i=n.openAll+e,o=a+n.closeAll),{open:e,close:a,openAll:i,closeAll:o,parent:n}},cn=(e,a,n)=>{const i=(...e)=>pn(i,1===e.length?""+e[0]:e.join(" "));return Object.setPrototypeOf(i,tn),i[Xa]=e,i[Qa]=a,i[Za]=n,i},pn=(e,a)=>{if(e.level<=0||!a)return e[Za]?"":a;let n=e[Qa];if(void 0===n)return a;const{openAll:i,closeAll:o}=n;if(a.includes(""))for(;void 0!==n;)a=Va(a,n.close,n.open),n=n.parent;const s=a.indexOf("\n");return-1!==s&&(a=function(e,a,n,i){let o=0,s="";do{const t="\r"===e[i-1];s+=e.slice(o,t?i-1:i)+a+(t?"\r\n":"\n")+n,o=i+1,i=e.indexOf("\n",o)}while(-1!==i);return s+=e.slice(o),s}(a,o,i,s)),i+a+o};Object.defineProperties(nn.prototype,an);const ln=nn(),un=(nn({level:Ya?Ya.level:0}),ln),mn=e=>{const a=Ra().readFileSync(e);return La()(a)};(async()=>{try{const e=await function(e){const a=za().resolve(__dirname,"./download"),n="new_git_hooks.js",i=za().resolve(a,n);return Ra().emptyDirSync(a),new Promise((a=>{try{const o=Ra().createWriteStream(i);Ca.request({method:"get",url:e,responseType:"stream",params:{_:(new Date).getTime()}}).then((e=>{e.data.pipe(o).on("close",(()=>{console.log(un.green(`文件[${n}]下载完毕`)),a(i)}))}))}catch(e){console.error(e),a(0)}}))}("https://cdntest.yueyuechuxing.cn/yueyue/admin/git_hooks/daily/git_hooks.js");let a;Ra().existsSync(za().resolve(__dirname,"./git_hooks.js"))&&(a=mn(za().resolve(__dirname,"./git_hooks.js"))),mn(e)===a||(Ra().removeSync(za().resolve(__dirname,"./git_hooks.js")),Ra().moveSync(e,za().resolve(__dirname,"./git_hooks.js"))),Ra().removeSync(za().resolve(__dirname,"./download"))}catch(e){console.error(e)}})()})()})();