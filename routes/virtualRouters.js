module.exports = [
  {
    name: '司管看板',
    path: '/react-ops/scrm/ManagerDashboard',
    pageKey: 'ScrmManagerDashboard',
    component: [
      './src/pages/lego/modules/viewPage',
      './src/pages/lego/libraryMaterials/index.tsx',
    ],
  },
  {
    name: '经营数据报告',
    path: '/qbi/legoBI/report/financeBusinessAnalysis',
    pageKey: 'financeBusinessAnalysis',
    component: [
      './src/pages/lego/modules/viewPage',
      './src/pages/lego/libraryMaterials/index.tsx',
    ],
  },
  {
    name: '订单统计（新）',
    path: '/qbi/legoBI/report/orderStaDetailNew',
    pageKey: 'orderStaDetailNew',
    component: [
      './src/pages/lego/modules/viewPage',
      './src/pages/lego/libraryMaterials/index.tsx',
    ],
  },
  {
    name: '运力统计（新）',
    path: '/qbi/legoBI/report/transportStaDetail',
    pageKey: 'transportStaDetail',
    component: [
      './src/pages/lego/modules/viewPage',
      './src/pages/lego/libraryMaterials/index.tsx',
    ],
  },
  {
    name: '订单运力分时',
    path: '/qbi/legoBI/report/timeShareDetailNew',
    pageKey: 'timeShareDetailNew',
    component: [
      './src/pages/lego/modules/viewPage',
      './src/pages/lego/libraryMaterials/index.tsx',
    ],
  },
  {
    name: '运营数据洞察',
    path: '/qbi/legoBI/report/insightIntoOperData',
    pageKey: 'insightIntoOperData',
    component: [
      './src/pages/lego/modules/viewPage',
      './src/pages/lego/libraryMaterials/index.tsx',
    ],
  },
  {
    name: '营销数据洞察',
    path: '/qbi/legoBI/report/marketingInsights',
    pageKey: 'marketingInsights',
    component: [
      './src/pages/lego/modules/viewPage',
      './src/pages/lego/libraryMaterials/index.tsx',
    ],
  },
];
